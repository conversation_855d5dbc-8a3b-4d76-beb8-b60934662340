"""
Consolidated AI Intelligence Module for TTM Squeeze Trading System
Combines all AI features: ChatGPT assistant, risk management, sentiment analysis, 
automated trading, parameter optimization, and dashboard intelligence
"""

import asyncio
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from collections import defaultdict, Counter
import logging
import json
import random
from abc import ABC, abstractmethod

logger = logging.getLogger(__name__)

# ============================================================================
# CORE AI DATA STRUCTURES
# ============================================================================

@dataclass
class TradingSignal:
    """Enhanced trading signal with AI analysis"""
    symbol: str
    signal_type: str
    timeframes: List[str]
    confidence: float
    ai_score: float
    risk_level: str
    entry_price: float
    stop_loss: Optional[float]
    take_profit: Optional[float]
    reasoning: List[str]
    timestamp: datetime = field(default_factory=datetime.now)

@dataclass
class MarketContext:
    """Market context analysis"""
    trend: str  # 'bullish', 'bearish', 'sideways'
    volatility: str  # 'low', 'normal', 'high'
    sentiment: float  # -1 to 1
    regime_score: float  # 0-100
    vix_level: float
    favorable_for_trading: bool

@dataclass
class RiskAssessment:
    """AI risk assessment result"""
    overall_risk_score: float  # 0-100
    risk_level: str  # 'low', 'moderate', 'high', 'very_high'
    recommended_position_size: float
    stop_loss_price: Optional[float]
    risk_reward_ratio: float
    confidence: float
    warnings: List[str]
    recommendations: List[str]

# ============================================================================
# CHATGPT TRADING ASSISTANT
# ============================================================================

class TradingContextManager:
    """Manages trading context for AI assistant"""
    
    def __init__(self, data_manager, trading_manager, scanner):
        self.data_manager = data_manager
        self.trading_manager = trading_manager
        self.scanner = scanner
        self.context_cache = {}
        self.cache_expiry = timedelta(minutes=5)
    
    async def get_current_alerts(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get current TTM Squeeze alerts"""
        try:
            if hasattr(self.scanner, 'get_recent_alerts'):
                return await self.scanner.get_recent_alerts(limit)
            return []
        except Exception as e:
            logger.error(f"Error getting alerts: {e}")
            return []
    
    async def get_portfolio_summary(self) -> Dict[str, Any]:
        """Get portfolio summary"""
        try:
            if self.trading_manager:
                account = await self.trading_manager.get_account()
                positions = await self.trading_manager.get_positions()
                return {
                    'account_value': account.get('portfolio_value', 0),
                    'buying_power': account.get('buying_power', 0),
                    'positions_count': len(positions),
                    'day_pnl': account.get('unrealized_pl', 0)
                }
            return {'error': 'Trading manager not available'}
        except Exception as e:
            logger.error(f"Error getting portfolio: {e}")
            return {'error': str(e)}
    
    async def get_market_status(self) -> Dict[str, Any]:
        """Get current market status"""
        try:
            # Simplified market status
            return {
                'market_open': True,  # Simplified
                'vix_level': 20.0,    # Default
                'spy_change': 0.0     # Default
            }
        except Exception as e:
            logger.error(f"Error getting market status: {e}")
            return {'error': str(e)}

class ChatGPTTradingAssistant:
    """AI Trading Assistant with natural language interface"""
    
    def __init__(self, context_manager: TradingContextManager):
        self.context_manager = context_manager
        self.conversation_history = defaultdict(list)
        
        # Check if OpenAI is available
        try:
            import openai
            self.openai_available = True
        except ImportError:
            self.openai_available = False
            logger.warning("OpenAI not available or API key not configured")
    
    async def process_trading_message(self, message: str, user_id: str = "default") -> Dict[str, Any]:
        """Process trading-related message"""
        try:
            # Add to conversation history
            self.conversation_history[user_id].append({
                'role': 'user',
                'content': message,
                'timestamp': datetime.now()
            })
            
            # Analyze message intent
            intent = self._analyze_message_intent(message)
            
            # Generate response based on intent
            if intent == 'market_analysis':
                response = await self._generate_market_analysis()
            elif intent == 'signal_analysis':
                response = await self._analyze_current_signals()
            elif intent == 'portfolio_status':
                response = await self._get_portfolio_status()
            elif intent == 'risk_assessment':
                response = await self._assess_current_risks()
            else:
                response = await self._generate_general_response(message)
            
            # Add response to history
            self.conversation_history[user_id].append({
                'role': 'assistant',
                'content': response,
                'timestamp': datetime.now()
            })
            
            return {
                'response': response,
                'type': 'success',
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error processing message: {e}")
            return {
                'response': f"I encountered an error: {str(e)}. Please try again.",
                'type': 'error',
                'timestamp': datetime.now().isoformat()
            }
    
    def _analyze_message_intent(self, message: str) -> str:
        """Analyze message intent"""
        message_lower = message.lower()
        
        if any(word in message_lower for word in ['market', 'conditions', 'trend', 'volatility']):
            return 'market_analysis'
        elif any(word in message_lower for word in ['signal', 'alert', 'opportunity', 'squeeze']):
            return 'signal_analysis'
        elif any(word in message_lower for word in ['portfolio', 'account', 'position', 'balance']):
            return 'portfolio_status'
        elif any(word in message_lower for word in ['risk', 'danger', 'safe', 'stop']):
            return 'risk_assessment'
        else:
            return 'general'
    
    async def _generate_market_analysis(self) -> str:
        """Generate market analysis response"""
        try:
            market_status = await self.context_manager.get_market_status()
            alerts = await self.context_manager.get_current_alerts(5)
            
            analysis = "📊 **Current Market Analysis:**\n\n"
            
            if market_status.get('market_open'):
                analysis += "✅ Market is currently open for trading.\n"
            else:
                analysis += "🔴 Market is currently closed.\n"
            
            vix = market_status.get('vix_level', 20)
            if vix > 25:
                analysis += f"⚠️ Elevated volatility (VIX: {vix:.1f}) - Exercise caution.\n"
            elif vix < 15:
                analysis += f"📈 Low volatility environment (VIX: {vix:.1f}) - Good for new positions.\n"
            else:
                analysis += f"📊 Normal volatility levels (VIX: {vix:.1f}).\n"
            
            if alerts:
                analysis += f"\n🎯 **Current TTM Squeeze Opportunities:** {len(alerts)} active signals\n"
                for alert in alerts[:3]:
                    symbol = alert.get('symbol', 'Unknown')
                    timeframes = ', '.join(alert.get('timeframes', []))
                    analysis += f"• **{symbol}**: {timeframes}\n"
            else:
                analysis += "\n📭 No active TTM Squeeze signals at the moment.\n"
            
            analysis += "\n💡 **Recommendation:** Focus on 5Min and 15Min timeframes for intraday opportunities."
            
            return analysis
            
        except Exception as e:
            return f"Unable to generate market analysis: {str(e)}"
    
    async def _analyze_current_signals(self) -> str:
        """Analyze current TTM Squeeze signals"""
        try:
            alerts = await self.context_manager.get_current_alerts(10)
            
            if not alerts:
                return "📭 No active TTM Squeeze signals detected at the moment. The scanner is continuously monitoring 507 stocks across 5Min and 15Min timeframes."
            
            analysis = f"🎯 **Current TTM Squeeze Analysis** ({len(alerts)} signals):\n\n"
            
            # Categorize signals by strength
            strong_signals = [a for a in alerts if len(a.get('timeframes', [])) >= 2]
            weak_signals = [a for a in alerts if len(a.get('timeframes', [])) == 1]
            
            if strong_signals:
                analysis += "🔥 **High-Confidence Signals** (Multiple timeframes):\n"
                for signal in strong_signals[:5]:
                    symbol = signal.get('symbol', 'Unknown')
                    timeframes = ', '.join(signal.get('timeframes', []))
                    signal_type = signal.get('signal_type', 'TTM Squeeze')
                    analysis += f"• **{symbol}**: {signal_type} | {timeframes}\n"
                analysis += "\n"
            
            if weak_signals:
                analysis += "⚡ **Single Timeframe Signals:**\n"
                for signal in weak_signals[:3]:
                    symbol = signal.get('symbol', 'Unknown')
                    timeframes = ', '.join(signal.get('timeframes', []))
                    analysis += f"• **{symbol}**: {timeframes}\n"
            
            analysis += "\n💡 **Trading Tip:** Focus on signals with confirmations from both 5Min and 15Min timeframes for higher probability setups."
            
            return analysis
            
        except Exception as e:
            return f"Unable to analyze signals: {str(e)}"
    
    async def _get_portfolio_status(self) -> str:
        """Get portfolio status"""
        try:
            portfolio = await self.context_manager.get_portfolio_summary()
            
            if 'error' in portfolio:
                return f"📊 Portfolio information unavailable: {portfolio['error']}"
            
            status = "💼 **Portfolio Status:**\n\n"
            status += f"💰 Account Value: ${portfolio.get('account_value', 0):,.2f}\n"
            status += f"💵 Buying Power: ${portfolio.get('buying_power', 0):,.2f}\n"
            status += f"📈 Active Positions: {portfolio.get('positions_count', 0)}\n"
            
            day_pnl = portfolio.get('day_pnl', 0)
            if day_pnl > 0:
                status += f"📈 Day P&L: +${day_pnl:,.2f}\n"
            elif day_pnl < 0:
                status += f"📉 Day P&L: -${abs(day_pnl):,.2f}\n"
            else:
                status += f"📊 Day P&L: ${day_pnl:,.2f}\n"
            
            return status
            
        except Exception as e:
            return f"Unable to get portfolio status: {str(e)}"
    
    async def _assess_current_risks(self) -> str:
        """Assess current trading risks"""
        try:
            market_status = await self.context_manager.get_market_status()
            portfolio = await self.context_manager.get_portfolio_summary()
            
            assessment = "🛡️ **Risk Assessment:**\n\n"
            
            # Market risk
            vix = market_status.get('vix_level', 20)
            if vix > 30:
                assessment += "🔴 **High Market Risk**: Elevated volatility detected\n"
            elif vix > 20:
                assessment += "🟡 **Moderate Market Risk**: Normal volatility levels\n"
            else:
                assessment += "🟢 **Low Market Risk**: Calm market conditions\n"
            
            # Portfolio risk
            if not portfolio.get('error'):
                positions = portfolio.get('positions_count', 0)
                if positions > 10:
                    assessment += "⚠️ **Portfolio Risk**: High number of positions\n"
                elif positions > 5:
                    assessment += "📊 **Portfolio Risk**: Moderate diversification\n"
                else:
                    assessment += "✅ **Portfolio Risk**: Well-controlled exposure\n"
            
            assessment += "\n💡 **Risk Management Tips:**\n"
            assessment += "• Use stop-losses on all positions\n"
            assessment += "• Limit position size to 2-5% of portfolio\n"
            assessment += "• Focus on high-probability setups only\n"
            
            return assessment
            
        except Exception as e:
            return f"Unable to assess risks: {str(e)}"
    
    async def _generate_general_response(self, message: str) -> str:
        """Generate general AI response"""
        responses = [
            f"I understand you're asking about: '{message}'. As your AI trading assistant, I can help you analyze TTM Squeeze signals, assess market conditions, review your portfolio, and manage trading risks.",
            f"Thanks for your question about '{message}'. I'm here to help with TTM Squeeze trading analysis. Try asking me about current market conditions, active signals, or risk assessment.",
            f"I received your message: '{message}'. I can provide insights on current TTM Squeeze opportunities, market analysis, portfolio status, and trading recommendations. What specific aspect would you like me to focus on?"
        ]
        
        return random.choice(responses)

# ============================================================================
# AI RISK MANAGEMENT
# ============================================================================

class AIRiskManager:
    """Advanced AI-powered risk management"""
    
    def __init__(self, data_manager, trading_manager):
        self.data_manager = data_manager
        self.trading_manager = trading_manager
        self.risk_models = {}
        self.portfolio_risk_limit = 0.15  # 15% max portfolio risk
    
    async def assess_trade_risk(self, signal: TradingSignal, market_context: MarketContext) -> RiskAssessment:
        """Comprehensive AI risk assessment"""
        try:
            # Calculate base risk score
            base_risk = self._calculate_base_risk(signal, market_context)
            
            # Technical risk factors
            technical_risk = self._assess_technical_risk(signal)
            
            # Market risk factors
            market_risk = self._assess_market_risk(market_context)
            
            # Portfolio risk factors
            portfolio_risk = await self._assess_portfolio_risk()
            
            # Combine risk factors
            overall_risk = (base_risk * 0.3 + technical_risk * 0.3 + 
                          market_risk * 0.2 + portfolio_risk * 0.2)
            
            # Determine risk level
            if overall_risk > 80:
                risk_level = 'very_high'
            elif overall_risk > 60:
                risk_level = 'high'
            elif overall_risk > 40:
                risk_level = 'moderate'
            else:
                risk_level = 'low'
            
            # Calculate position sizing
            position_size = self._calculate_optimal_position_size(overall_risk, market_context)
            
            # Calculate stop loss
            stop_loss = self._calculate_stop_loss(signal, overall_risk)
            
            # Generate warnings and recommendations
            warnings = self._generate_risk_warnings(overall_risk, market_context)
            recommendations = self._generate_risk_recommendations(risk_level, signal)
            
            return RiskAssessment(
                overall_risk_score=overall_risk,
                risk_level=risk_level,
                recommended_position_size=position_size,
                stop_loss_price=stop_loss,
                risk_reward_ratio=2.0,  # Default 2:1
                confidence=0.85,
                warnings=warnings,
                recommendations=recommendations
            )
            
        except Exception as e:
            logger.error(f"Error in risk assessment: {e}")
            return self._create_default_risk_assessment()
    
    def _calculate_base_risk(self, signal: TradingSignal, market_context: MarketContext) -> float:
        """Calculate base risk score"""
        risk_score = 50.0  # Base score
        
        # Signal confidence impact
        risk_score -= (signal.confidence - 0.5) * 40  # Higher confidence = lower risk
        
        # Timeframe impact
        if len(signal.timeframes) >= 2:
            risk_score -= 10  # Multiple timeframes reduce risk
        
        # Market volatility impact
        if market_context.volatility == 'high':
            risk_score += 20
        elif market_context.volatility == 'low':
            risk_score -= 10
        
        return max(0, min(100, risk_score))
    
    def _assess_technical_risk(self, signal: TradingSignal) -> float:
        """Assess technical risk factors"""
        risk_score = 50.0
        
        # AI score impact
        risk_score -= (signal.ai_score - 50) * 0.5
        
        # Signal type impact
        if signal.signal_type == 'TTM Squeeze setup':
            risk_score -= 5  # Setup signals are lower risk
        
        return max(0, min(100, risk_score))
    
    def _assess_market_risk(self, market_context: MarketContext) -> float:
        """Assess market risk factors"""
        risk_score = 50.0
        
        # VIX impact
        if market_context.vix_level > 30:
            risk_score += 25
        elif market_context.vix_level < 15:
            risk_score -= 15
        
        # Trend impact
        if market_context.trend == 'bearish':
            risk_score += 10
        elif market_context.trend == 'bullish':
            risk_score -= 10
        
        return max(0, min(100, risk_score))
    
    async def _assess_portfolio_risk(self) -> float:
        """Assess portfolio-level risk"""
        try:
            if not self.trading_manager:
                return 50.0
            
            positions = await self.trading_manager.get_positions()
            position_count = len(positions)
            
            risk_score = 50.0
            
            # Position concentration risk
            if position_count > 15:
                risk_score += 20
            elif position_count > 10:
                risk_score += 10
            elif position_count < 3:
                risk_score -= 10
            
            return max(0, min(100, risk_score))
            
        except Exception as e:
            logger.error(f"Error assessing portfolio risk: {e}")
            return 50.0
    
    def _calculate_optimal_position_size(self, risk_score: float, market_context: MarketContext) -> float:
        """Calculate optimal position size based on risk"""
        base_size = 0.03  # 3% base position size
        
        # Adjust for risk
        risk_multiplier = 1.0 - (risk_score / 100) * 0.7  # Reduce size as risk increases
        
        # Adjust for market conditions
        if market_context.volatility == 'high':
            risk_multiplier *= 0.7
        elif market_context.volatility == 'low':
            risk_multiplier *= 1.2
        
        optimal_size = base_size * risk_multiplier
        return max(0.005, min(0.05, optimal_size))  # Between 0.5% and 5%
    
    def _calculate_stop_loss(self, signal: TradingSignal, risk_score: float) -> Optional[float]:
        """Calculate stop loss price"""
        if signal.stop_loss:
            return signal.stop_loss
        
        # Default stop loss calculation
        if signal.entry_price:
            stop_distance = 0.02 + (risk_score / 100) * 0.03  # 2-5% based on risk
            return signal.entry_price * (1 - stop_distance)
        
        return None
    
    def _generate_risk_warnings(self, risk_score: float, market_context: MarketContext) -> List[str]:
        """Generate risk warnings"""
        warnings = []
        
        if risk_score > 80:
            warnings.append("Very high risk detected - consider avoiding this trade")
        elif risk_score > 60:
            warnings.append("High risk - use smaller position size and tight stops")
        
        if market_context.volatility == 'high':
            warnings.append("High market volatility - expect larger price swings")
        
        if market_context.vix_level > 30:
            warnings.append("Elevated VIX suggests increased market stress")
        
        return warnings
    
    def _generate_risk_recommendations(self, risk_level: str, signal: TradingSignal) -> List[str]:
        """Generate risk management recommendations"""
        recommendations = []
        
        if risk_level in ['high', 'very_high']:
            recommendations.append("Use smaller position size")
            recommendations.append("Set tight stop-loss orders")
            recommendations.append("Consider waiting for better setup")
        
        if len(signal.timeframes) == 1:
            recommendations.append("Wait for multi-timeframe confirmation")
        
        recommendations.append("Monitor position closely after entry")
        recommendations.append("Consider taking partial profits at resistance")
        
        return recommendations
    
    def _create_default_risk_assessment(self) -> RiskAssessment:
        """Create default risk assessment for errors"""
        return RiskAssessment(
            overall_risk_score=75.0,
            risk_level='high',
            recommended_position_size=0.01,
            stop_loss_price=None,
            risk_reward_ratio=2.0,
            confidence=0.5,
            warnings=["Risk assessment unavailable"],
            recommendations=["Use conservative position sizing"]
        )

# ============================================================================
# AUTOMATED TRADING ENGINE
# ============================================================================

class AutoTradingConfig:
    """Configuration for automated trading"""
    def __init__(self):
        self.mode = "disabled"  # disabled, paper_only, live
        self.max_daily_trades = 5
        self.max_position_size = 0.03  # 3% max position
        self.min_confidence_threshold = 0.75
        self.high_confidence_threshold = 0.85

class AITradingAutopilot:
    """AI-powered automated trading engine"""

    def __init__(self, data_manager, trading_manager, scanner):
        self.data_manager = data_manager
        self.trading_manager = trading_manager
        self.scanner = scanner
        self.config = AutoTradingConfig()
        self.risk_manager = AIRiskManager(data_manager, trading_manager)
        self.daily_trade_count = 0
        self.last_reset_date = datetime.now().date()

    async def evaluate_signal_for_auto_trade(self, signal: TradingSignal) -> Dict[str, Any]:
        """Evaluate signal for automated trading"""
        try:
            # Reset daily counter if new day
            if datetime.now().date() > self.last_reset_date:
                self.daily_trade_count = 0
                self.last_reset_date = datetime.now().date()

            # Check if autopilot is enabled
            if self.config.mode == "disabled":
                return {"execute": False, "reason": "Autopilot disabled"}

            # Check daily trade limit
            if self.daily_trade_count >= self.config.max_daily_trades:
                return {"execute": False, "reason": "Daily trade limit reached"}

            # Check confidence threshold
            if signal.confidence < self.config.min_confidence_threshold:
                return {"execute": False, "reason": "Confidence below threshold"}

            # Perform risk assessment
            market_context = MarketContext(
                trend='sideways', volatility='normal', sentiment=0.0,
                regime_score=50.0, vix_level=20.0, favorable_for_trading=True
            )

            risk_assessment = await self.risk_manager.assess_trade_risk(signal, market_context)

            # Check risk level
            if risk_assessment.risk_level in ['very_high']:
                return {"execute": False, "reason": "Risk too high"}

            # Determine execution
            should_execute = (
                signal.confidence >= self.config.min_confidence_threshold and
                len(signal.timeframes) >= 2 and  # Multi-timeframe confirmation
                risk_assessment.risk_level not in ['very_high']
            )

            if should_execute and self.config.mode == "live":
                # Execute trade
                await self._execute_trade(signal, risk_assessment)
                self.daily_trade_count += 1

            return {
                "execute": should_execute,
                "mode": self.config.mode,
                "confidence": signal.confidence,
                "risk_level": risk_assessment.risk_level,
                "position_size": risk_assessment.recommended_position_size
            }

        except Exception as e:
            logger.error(f"Error evaluating signal for auto-trade: {e}")
            return {"execute": False, "reason": f"Error: {str(e)}"}

    async def _execute_trade(self, signal: TradingSignal, risk_assessment: RiskAssessment):
        """Execute automated trade"""
        try:
            if not self.trading_manager:
                logger.warning("Trading manager not available")
                return

            # Calculate quantity
            account = await self.trading_manager.get_account()
            account_value = float(account.get('portfolio_value', 100000))
            dollar_amount = account_value * risk_assessment.recommended_position_size
            quantity = int(dollar_amount / signal.entry_price)

            if quantity > 0:
                # Place order
                order_result = await self.trading_manager.place_order(
                    symbol=signal.symbol,
                    side='buy',
                    qty=quantity,
                    type='market',
                    time_in_force='day'
                )

                logger.info(f"Auto-trade executed: {signal.symbol} x{quantity} @ ${signal.entry_price}")

        except Exception as e:
            logger.error(f"Error executing auto-trade: {e}")

    def update_config(self, new_config: Dict[str, Any]):
        """Update autopilot configuration"""
        for key, value in new_config.items():
            if hasattr(self.config, key):
                setattr(self.config, key, value)
        logger.info(f"Autopilot config updated: {new_config}")

    def get_status(self) -> Dict[str, Any]:
        """Get autopilot status"""
        return {
            'mode': self.config.mode,
            'daily_trades': self.daily_trade_count,
            'max_daily_trades': self.config.max_daily_trades,
            'min_confidence': self.config.min_confidence_threshold,
            'last_reset': self.last_reset_date.isoformat()
        }

# ============================================================================
# SIGNAL ENHANCEMENT AND QUALITY PREDICTION
# ============================================================================

class AISignalEnhancer:
    """AI-powered signal enhancement and quality prediction"""

    def __init__(self):
        self.quality_model = None
        self.enhancement_cache = {}
        self.cache_expiry = timedelta(minutes=5)

    async def enhance_signal(self, raw_signal: Dict[str, Any]) -> TradingSignal:
        """Enhance raw signal with AI analysis"""
        try:
            symbol = raw_signal.get('symbol', 'Unknown')
            signal_type = raw_signal.get('signal_type', 'TTM Squeeze')
            timeframes = raw_signal.get('timeframes', [])

            # Calculate base confidence
            base_confidence = self._calculate_base_confidence(raw_signal)

            # Calculate AI score
            ai_score = self._calculate_ai_score(raw_signal)

            # Determine risk level
            risk_level = self._determine_risk_level(raw_signal, ai_score)

            # Generate reasoning
            reasoning = self._generate_reasoning(raw_signal, ai_score)

            # Create enhanced signal
            enhanced_signal = TradingSignal(
                symbol=symbol,
                signal_type=signal_type,
                timeframes=timeframes,
                confidence=base_confidence,
                ai_score=ai_score,
                risk_level=risk_level,
                entry_price=raw_signal.get('current_price', 0.0),
                stop_loss=None,  # Will be calculated by risk manager
                take_profit=None,
                reasoning=reasoning
            )

            return enhanced_signal

        except Exception as e:
            logger.error(f"Error enhancing signal: {e}")
            return self._create_default_signal(raw_signal)

    def _calculate_base_confidence(self, signal: Dict[str, Any]) -> float:
        """Calculate base confidence score"""
        confidence = 0.5  # Base confidence

        # Timeframe confirmations
        timeframes = signal.get('timeframes', [])
        if len(timeframes) >= 2:
            confidence += 0.2
        if len(timeframes) >= 3:
            confidence += 0.1

        # Signal strength
        signal_strength = signal.get('signal_strength', 0.5)
        confidence += (signal_strength - 0.5) * 0.4

        # Volume confirmation
        if signal.get('volume_confirmation', False):
            confidence += 0.1

        return max(0.0, min(1.0, confidence))

    def _calculate_ai_score(self, signal: Dict[str, Any]) -> float:
        """Calculate AI quality score"""
        score = 50.0  # Base score

        # Multi-timeframe bonus
        timeframes = signal.get('timeframes', [])
        score += len(timeframes) * 10

        # Signal type bonus
        if 'setup' in signal.get('signal_type', '').lower():
            score += 15

        # Technical indicators
        if signal.get('momentum_positive', False):
            score += 10

        if signal.get('trend_alignment', False):
            score += 10

        return max(0.0, min(100.0, score))

    def _determine_risk_level(self, signal: Dict[str, Any], ai_score: float) -> str:
        """Determine risk level"""
        if ai_score > 80 and len(signal.get('timeframes', [])) >= 2:
            return 'low'
        elif ai_score > 60:
            return 'moderate'
        else:
            return 'high'

    def _generate_reasoning(self, signal: Dict[str, Any], ai_score: float) -> List[str]:
        """Generate reasoning for the signal"""
        reasoning = []

        timeframes = signal.get('timeframes', [])
        if len(timeframes) >= 2:
            reasoning.append(f"Multi-timeframe confirmation: {', '.join(timeframes)}")

        if ai_score > 70:
            reasoning.append("High AI quality score indicates strong setup")

        if signal.get('volume_confirmation'):
            reasoning.append("Volume confirmation supports the signal")

        if signal.get('momentum_positive'):
            reasoning.append("Positive momentum detected")

        if not reasoning:
            reasoning.append("Basic TTM Squeeze pattern detected")

        return reasoning

    def _create_default_signal(self, raw_signal: Dict[str, Any]) -> TradingSignal:
        """Create default signal for errors"""
        return TradingSignal(
            symbol=raw_signal.get('symbol', 'Unknown'),
            signal_type=raw_signal.get('signal_type', 'TTM Squeeze'),
            timeframes=raw_signal.get('timeframes', []),
            confidence=0.5,
            ai_score=50.0,
            risk_level='moderate',
            entry_price=raw_signal.get('current_price', 0.0),
            stop_loss=None,
            take_profit=None,
            reasoning=["Default signal processing"]
        )

# ============================================================================
# DASHBOARD INTELLIGENCE
# ============================================================================

class DashboardIntelligence:
    """AI-powered dashboard personalization and insights"""

    def __init__(self):
        self.user_behavior = defaultdict(list)
        self.personalization_cache = {}

    def track_user_interaction(self, user_id: str, interaction: Dict[str, Any]):
        """Track user interaction for learning"""
        interaction['timestamp'] = datetime.now()
        self.user_behavior[user_id].append(interaction)

        # Keep only recent interactions (last 1000)
        if len(self.user_behavior[user_id]) > 1000:
            self.user_behavior[user_id] = self.user_behavior[user_id][-1000:]

    async def get_personalized_dashboard(self, user_id: str) -> Dict[str, Any]:
        """Get personalized dashboard configuration"""
        try:
            # Analyze user behavior
            behavior = self._analyze_user_behavior(user_id)

            # Generate personalization
            personalization = {
                'layout_config': self._generate_layout_config(behavior),
                'refresh_intervals': self._optimize_refresh_intervals(behavior),
                'default_views': self._set_default_views(behavior),
                'quick_actions': self._generate_quick_actions(behavior),
                'alert_settings': self._customize_alert_settings(behavior)
            }

            return personalization

        except Exception as e:
            logger.error(f"Error generating personalization: {e}")
            return self._get_default_personalization()

    def _analyze_user_behavior(self, user_id: str) -> Dict[str, Any]:
        """Analyze user behavior patterns"""
        interactions = self.user_behavior[user_id]

        if not interactions:
            return {'trading_style': 'moderate', 'session_duration': 1800, 'most_used_features': []}

        # Analyze features used
        feature_usage = Counter()
        for interaction in interactions[-100:]:  # Last 100 interactions
            feature = interaction.get('feature')
            if feature:
                feature_usage[feature] += 1

        # Determine trading style
        trading_style = 'moderate'  # Default
        if 'risk' in str(feature_usage.most_common(5)):
            trading_style = 'conservative'
        elif 'trading' in str(feature_usage.most_common(5)):
            trading_style = 'aggressive'

        return {
            'trading_style': trading_style,
            'session_duration': 1800,  # Default 30 minutes
            'most_used_features': [f[0] for f in feature_usage.most_common(5)]
        }

    def _generate_layout_config(self, behavior: Dict[str, Any]) -> Dict[str, Any]:
        """Generate layout configuration"""
        style = behavior.get('trading_style', 'moderate')

        if style == 'conservative':
            return {
                'primary_panels': ['risk_management', 'portfolio', 'alerts'],
                'panel_sizes': {'risk_management': 'large'},
                'color_scheme': 'blue'
            }
        elif style == 'aggressive':
            return {
                'primary_panels': ['trading', 'scanner', 'alerts'],
                'panel_sizes': {'trading': 'large'},
                'color_scheme': 'red'
            }
        else:
            return {
                'primary_panels': ['scanner', 'alerts', 'portfolio'],
                'panel_sizes': {'scanner': 'large'},
                'color_scheme': 'green'
            }

    def _optimize_refresh_intervals(self, behavior: Dict[str, Any]) -> Dict[str, int]:
        """Optimize refresh intervals"""
        style = behavior.get('trading_style', 'moderate')

        if style == 'aggressive':
            return {'scanner': 5, 'alerts': 3, 'portfolio': 10}
        elif style == 'conservative':
            return {'scanner': 30, 'alerts': 15, 'portfolio': 60}
        else:
            return {'scanner': 15, 'alerts': 10, 'portfolio': 30}

    def _set_default_views(self, behavior: Dict[str, Any]) -> Dict[str, str]:
        """Set default views"""
        return {
            'scanner': 'detailed' if 'scanner' in behavior.get('most_used_features', []) else 'summary',
            'alerts': 'recent',
            'portfolio': 'overview'
        }

    def _generate_quick_actions(self, behavior: Dict[str, Any]) -> List[str]:
        """Generate quick action buttons"""
        style = behavior.get('trading_style', 'moderate')

        if style == 'aggressive':
            return ['quick_buy', 'quick_sell', 'run_scan', 'check_positions']
        elif style == 'conservative':
            return ['risk_check', 'portfolio_review', 'run_scan', 'view_alerts']
        else:
            return ['run_scan', 'view_alerts', 'check_portfolio', 'risk_analysis']

    def _customize_alert_settings(self, behavior: Dict[str, Any]) -> Dict[str, Any]:
        """Customize alert settings"""
        style = behavior.get('trading_style', 'moderate')

        return {
            'confidence_threshold': 0.8 if style == 'conservative' else 0.6 if style == 'aggressive' else 0.7,
            'timeframes': ['5Min', '15Min'],  # Focus on intraday
            'max_alerts_per_hour': 20 if style == 'aggressive' else 10 if style == 'conservative' else 15
        }

    def _get_default_personalization(self) -> Dict[str, Any]:
        """Get default personalization"""
        return {
            'layout_config': {
                'primary_panels': ['scanner', 'alerts', 'portfolio'],
                'color_scheme': 'green'
            },
            'refresh_intervals': {'scanner': 15, 'alerts': 10, 'portfolio': 30},
            'default_views': {'scanner': 'summary', 'alerts': 'recent'},
            'quick_actions': ['run_scan', 'view_alerts', 'check_portfolio'],
            'alert_settings': {'confidence_threshold': 0.7, 'timeframes': ['5Min', '15Min']}
        }
