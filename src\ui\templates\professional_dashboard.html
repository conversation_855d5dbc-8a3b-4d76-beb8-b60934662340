<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TTM Squeeze Trading System - Professional Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://unpkg.com/lightweight-charts/dist/lightweight-charts.standalone.production.js"></script>
    <style>
        /* Professional Trading Dashboard Theme */
        :root {
            --bg-primary: #0d1421;
            --bg-secondary: #1a1f2e;
            --bg-tertiary: #252a3a;
            --text-primary: #ffffff;
            --text-secondary: #b8bcc8;
            --text-muted: #6c757d;
            --accent-blue: #00d4ff;
            --accent-green: #00ff88;
            --accent-red: #ff4757;
            --accent-yellow: #ffa502;
            --border-color: #2d3748;
            --hover-bg: #2a3441;
        }

        * {
            box-sizing: border-box;
        }

        body {
            background: var(--bg-primary);
            color: var(--text-primary);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }

        /* Professional Navigation */
        .trading-navbar {
            background: var(--bg-secondary);
            border-bottom: 1px solid var(--border-color);
            padding: 0.5rem 1rem;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            height: 60px;
        }

        .navbar-brand {
            color: var(--accent-blue) !important;
            font-weight: 700;
            font-size: 1.2rem;
        }

        .nav-link {
            color: var(--text-secondary) !important;
            padding: 0.5rem 1rem !important;
            border-radius: 4px;
            transition: all 0.2s ease;
        }

        .nav-link:hover, .nav-link.active {
            background: var(--hover-bg);
            color: var(--text-primary) !important;
        }

        /* Main Dashboard Layout */
        .dashboard-container {
            margin-top: 60px;
            height: calc(100vh - 60px);
            display: flex;
            flex-direction: column;
        }

        .dashboard-tabs {
            background: var(--bg-secondary);
            border-bottom: 1px solid var(--border-color);
            padding: 0;
        }

        .nav-tabs {
            border-bottom: none;
        }

        .nav-tabs .nav-link {
            background: transparent;
            border: none;
            color: var(--text-secondary);
            padding: 1rem 1.5rem;
            border-radius: 0;
            border-bottom: 3px solid transparent;
        }

        .nav-tabs .nav-link.active {
            background: var(--bg-primary);
            color: var(--accent-blue);
            border-bottom-color: var(--accent-blue);
        }

        .tab-content {
            flex: 1;
            overflow: hidden;
        }

        .tab-pane {
            height: 100%;
            padding: 0;
        }

        /* Grid Layout System */
        .trading-grid {
            display: grid;
            grid-template-columns: 300px 1fr 350px;
            grid-template-rows: 200px 1fr;
            gap: 1px;
            height: 100%;
            background: var(--border-color);
        }

        .grid-panel {
            background: var(--bg-primary);
            padding: 1rem;
            overflow: auto;
        }

        .panel-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid var(--border-color);
        }

        .panel-title {
            font-size: 0.9rem;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
        }

        /* Market Data Panel */
        .market-overview {
            grid-column: 1 / 4;
            grid-row: 1;
            display: flex;
            gap: 1rem;
            padding: 1rem;
        }

        .market-metric {
            flex: 1;
            background: var(--bg-secondary);
            border-radius: 8px;
            padding: 1rem;
            text-align: center;
            border: 1px solid var(--border-color);
        }

        .metric-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0.25rem;
        }

        .metric-label {
            font-size: 0.8rem;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .metric-change {
            font-size: 0.9rem;
            margin-top: 0.25rem;
        }

        .positive { color: var(--accent-green); }
        .negative { color: var(--accent-red); }
        .neutral { color: var(--text-secondary); }

        /* Scanner Panel */
        .scanner-panel {
            grid-column: 1;
            grid-row: 2;
        }

        /* Chart Panel */
        .chart-panel {
            grid-column: 2;
            grid-row: 2;
            padding: 0;
        }

        .chart-container {
            height: 100%;
            position: relative;
        }

        .chart-controls {
            position: absolute;
            top: 10px;
            left: 10px;
            z-index: 10;
            display: flex;
            gap: 0.5rem;
        }

        .chart-btn {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            color: var(--text-secondary);
            padding: 0.25rem 0.75rem;
            border-radius: 4px;
            font-size: 0.8rem;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .chart-btn:hover, .chart-btn.active {
            background: var(--accent-blue);
            color: var(--text-primary);
        }

        /* Trading Panel */
        .trading-panel {
            grid-column: 3;
            grid-row: 2;
        }

        /* Alert Cards */
        .alert-item {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .alert-item:hover {
            background: var(--hover-bg);
            border-color: var(--accent-blue);
        }

        .alert-symbol {
            font-weight: 700;
            color: var(--accent-blue);
            font-size: 1rem;
        }

        .alert-signal {
            font-size: 0.8rem;
            color: var(--text-secondary);
            margin: 0.25rem 0;
        }

        .alert-timeframes {
            display: flex;
            gap: 0.25rem;
            margin-top: 0.5rem;
        }

        .timeframe-badge {
            background: var(--bg-tertiary);
            color: var(--text-secondary);
            padding: 0.125rem 0.5rem;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: 500;
        }

        .timeframe-badge.active {
            background: var(--accent-green);
            color: var(--text-primary);
        }

        /* Trading Controls */
        .order-panel {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .form-control {
            background: var(--bg-tertiary);
            border: 1px solid var(--border-color);
            color: var(--text-primary);
            border-radius: 4px;
        }

        .form-control:focus {
            background: var(--bg-tertiary);
            border-color: var(--accent-blue);
            color: var(--text-primary);
            box-shadow: 0 0 0 0.2rem rgba(0, 212, 255, 0.25);
        }

        .btn-trading {
            padding: 0.5rem 1rem;
            border-radius: 4px;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 0.8rem;
            letter-spacing: 0.5px;
            border: none;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .btn-buy {
            background: var(--accent-green);
            color: var(--text-primary);
        }

        .btn-buy:hover {
            background: #00e67a;
        }

        .btn-sell {
            background: var(--accent-red);
            color: var(--text-primary);
        }

        .btn-sell:hover {
            background: #ff3742;
        }

        /* AI Indicators */
        .ai-gauge {
            width: 80px;
            height: 80px;
            margin: 0 auto;
            position: relative;
        }

        .gauge-circle {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background: conic-gradient(var(--accent-green) 0deg, var(--accent-yellow) 180deg, var(--accent-red) 360deg);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .gauge-inner {
            width: 60px;
            height: 60px;
            background: var(--bg-secondary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 0.9rem;
        }

        /* Scrollbar Styling */
        ::-webkit-scrollbar {
            width: 6px;
        }

        ::-webkit-scrollbar-track {
            background: var(--bg-secondary);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--border-color);
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--text-muted);
        }

        /* Responsive Design */
        @media (max-width: 1400px) {
            .trading-grid {
                grid-template-columns: 280px 1fr 320px;
            }
        }

        @media (max-width: 1200px) {
            .trading-grid {
                grid-template-columns: 260px 1fr 300px;
            }

            .market-metric {
                padding: 0.75rem;
            }

            .metric-value {
                font-size: 1.3rem;
            }
        }

        @media (max-width: 992px) {
            .trading-grid {
                grid-template-columns: 1fr;
                grid-template-rows: auto auto 1fr auto;
                gap: 0.5rem;
            }

            .market-overview {
                grid-column: 1;
                grid-row: 1;
                flex-wrap: wrap;
            }

            .market-metric {
                min-width: 150px;
                flex: 1 1 calc(50% - 0.5rem);
            }

            .scanner-panel {
                grid-column: 1;
                grid-row: 2;
                max-height: 300px;
            }

            .chart-panel {
                grid-column: 1;
                grid-row: 3;
                min-height: 400px;
            }

            .trading-panel {
                grid-column: 1;
                grid-row: 4;
                max-height: 400px;
            }
        }

        @media (max-width: 768px) {
            .dashboard-tabs .nav-link {
                padding: 0.75rem 1rem;
                font-size: 0.9rem;
            }

            .market-overview {
                padding: 0.5rem;
                gap: 0.5rem;
            }

            .market-metric {
                flex: 1 1 100%;
                margin-bottom: 0.5rem;
            }

            .grid-panel {
                padding: 0.75rem;
            }

            .chart-controls {
                flex-wrap: wrap;
                gap: 0.25rem;
            }

            .chart-btn {
                padding: 0.25rem 0.5rem;
                font-size: 0.7rem;
            }
        }

        /* Animation and Transitions */
        .alert-item {
            animation: slideIn 0.3s ease-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .metric-value {
            transition: all 0.3s ease;
        }

        .metric-value.updating {
            transform: scale(1.05);
            color: var(--accent-blue);
        }

        /* Professional Table Styling */
        .table-dark {
            --bs-table-bg: var(--bg-secondary);
            --bs-table-border-color: var(--border-color);
        }

        .table-dark th,
        .table-dark td {
            border-color: var(--border-color);
            color: var(--text-primary);
        }

        .table-dark thead th {
            background-color: var(--bg-tertiary);
            border-bottom: 2px solid var(--border-color);
        }
    </style>
</head>
<body>
    <!-- Professional Navigation -->
    <nav class="navbar navbar-expand-lg trading-navbar">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-chart-line me-2"></i>TTM SQUEEZE PRO
            </a>
            <div class="navbar-nav ms-auto">
                <span class="nav-link" id="market-status">
                    <i class="fas fa-circle text-success"></i> Market Open
                </span>
                <span class="nav-link" id="account-value">
                    <i class="fas fa-wallet me-1"></i>$59,790.98
                </span>
                <span class="nav-link" id="current-time">
                    <i class="fas fa-clock me-1"></i><span id="live-time"></span>
                </span>
            </div>
        </div>
    </nav>

    <!-- Main Dashboard Container -->
    <div class="dashboard-container">
        <!-- Dashboard Tabs -->
        <div class="dashboard-tabs">
            <ul class="nav nav-tabs" id="dashboardTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="trading-tab" data-bs-toggle="tab" data-bs-target="#trading" type="button" role="tab">
                        <i class="fas fa-chart-line me-2"></i>Trading
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="scanner-tab" data-bs-toggle="tab" data-bs-target="#scanner" type="button" role="tab">
                        <i class="fas fa-search me-2"></i>Scanner
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="alerts-tab" data-bs-toggle="tab" data-bs-target="#alerts" type="button" role="tab">
                        <i class="fas fa-bell me-2"></i>Alerts
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="targets-tab" data-bs-toggle="tab" data-bs-target="#targets" type="button" role="tab">
                        <i class="fas fa-bullseye me-2"></i>Profit Targets
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="performance-tab" data-bs-toggle="tab" data-bs-target="#performance" type="button" role="tab">
                        <i class="fas fa-chart-bar me-2"></i>Performance
                    </button>
                </li>
            </ul>
        </div>

        <!-- Tab Content -->
        <div class="tab-content">
            <!-- Trading Tab -->
            <div class="tab-pane fade show active" id="trading" role="tabpanel">
                <div class="trading-grid">
                    <!-- Market Overview -->
                    <div class="market-overview">
                        <div class="market-metric">
                            <div class="metric-value positive" id="spy-price">$425.67</div>
                            <div class="metric-label">SPY</div>
                            <div class="metric-change positive" id="spy-change">+0.85%</div>
                        </div>
                        <div class="market-metric">
                            <div class="metric-value neutral" id="vix-value">22.45</div>
                            <div class="metric-label">VIX</div>
                            <div class="metric-change negative" id="vix-change">-2.1%</div>
                        </div>
                        <div class="market-metric">
                            <div class="metric-value positive" id="portfolio-pnl">+$1,247</div>
                            <div class="metric-label">Day P&L</div>
                            <div class="metric-change positive" id="portfolio-change">+2.08%</div>
                        </div>
                        <div class="market-metric">
                            <div class="metric-value neutral" id="active-alerts">4</div>
                            <div class="metric-label">Active Alerts</div>
                            <div class="metric-change neutral" id="alert-change">New: 2</div>
                        </div>
                        <div class="market-metric">
                            <div class="metric-value" id="ai-confidence">87%</div>
                            <div class="metric-label">AI Confidence</div>
                            <div class="metric-change positive" id="ai-trend">Bullish</div>
                        </div>
                    </div>

                    <!-- Scanner Panel -->
                    <div class="grid-panel scanner-panel">
                        <div class="panel-header">
                            <h6 class="panel-title">TTM Squeeze Alerts</h6>
                            <button class="btn btn-sm btn-outline-primary" onclick="refreshAlerts()">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                        </div>
                        <div id="alerts-container">
                            <!-- Alerts will be populated here -->
                        </div>
                    </div>

                    <!-- Market Analytics Panel -->
                    <div class="grid-panel chart-panel">
                        <div class="panel-header">
                            <h6 class="panel-title">Market Analytics & Signal Intelligence</h6>
                            <div class="chart-controls">
                                <button class="chart-btn active" onclick="showMarketOverview()">Market</button>
                                <button class="chart-btn" onclick="showSignalAnalytics()">Signals</button>
                                <button class="chart-btn" onclick="showAIInsights()">AI Insights</button>
                                <button class="chart-btn" onclick="showRiskMetrics()">Risk</button>
                            </div>
                        </div>
                        <div id="analytics-content" style="height: calc(100% - 60px); overflow-y: auto;">
                            <!-- Market analytics content will be populated here -->
                        </div>
                    </div>

                    <!-- Trading Panel -->
                    <div class="grid-panel trading-panel">
                        <div class="panel-header">
                            <h6 class="panel-title">Order Entry</h6>
                            <span class="badge bg-success">Live Trading</span>
                        </div>
                        
                        <div class="order-panel">
                            <div class="mb-3">
                                <label class="form-label">Symbol</label>
                                <input type="text" class="form-control" id="order-symbol" placeholder="AAPL">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Quantity</label>
                                <input type="number" class="form-control" id="order-quantity" placeholder="100">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Order Type</label>
                                <select class="form-control" id="order-type">
                                    <option value="market">Market</option>
                                    <option value="limit">Limit</option>
                                    <option value="stop">Stop</option>
                                </select>
                            </div>
                            <div class="d-grid gap-2">
                                <button class="btn btn-trading btn-buy" onclick="placeOrder('buy')">
                                    <i class="fas fa-arrow-up me-1"></i>BUY
                                </button>
                                <button class="btn btn-trading btn-sell" onclick="placeOrder('sell')">
                                    <i class="fas fa-arrow-down me-1"></i>SELL
                                </button>
                            </div>
                        </div>

                        <div class="panel-header mt-3">
                            <h6 class="panel-title">Positions</h6>
                        </div>
                        <div id="positions-container">
                            <!-- Positions will be populated here -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Scanner Tab -->
            <div class="tab-pane fade" id="scanner" role="tabpanel">
                <div class="trading-grid">
                    <div class="market-overview">
                        <div class="market-metric">
                            <div class="metric-value" id="scan-total">247</div>
                            <div class="metric-label">Total Scanned</div>
                            <div class="metric-change neutral">S&P 500</div>
                        </div>
                        <div class="market-metric">
                            <div class="metric-value positive" id="squeeze-signals">12</div>
                            <div class="metric-label">TTM Squeeze</div>
                            <div class="metric-change positive">+3 New</div>
                        </div>
                        <div class="market-metric">
                            <div class="metric-value" id="breakout-signals">8</div>
                            <div class="metric-label">Breakouts</div>
                            <div class="metric-change positive">+2 New</div>
                        </div>
                        <div class="market-metric">
                            <div class="metric-value" id="ai-filtered">5</div>
                            <div class="metric-label">AI Filtered</div>
                            <div class="metric-change positive">High Conf</div>
                        </div>
                        <div class="market-metric">
                            <div class="metric-value" id="scan-time">2.3s</div>
                            <div class="metric-label">Scan Time</div>
                            <div class="metric-change neutral">Real-time</div>
                        </div>
                    </div>

                    <div class="grid-panel scanner-panel">
                        <div class="panel-header">
                            <h6 class="panel-title">Scanner Results</h6>
                            <div>
                                <button class="btn btn-sm btn-outline-primary me-2" onclick="startScan()">
                                    <i class="fas fa-play"></i> Start
                                </button>
                                <button class="btn btn-sm btn-outline-secondary" onclick="stopScan()">
                                    <i class="fas fa-stop"></i> Stop
                                </button>
                            </div>
                        </div>
                        <div id="scanner-results">
                            <!-- Scanner results will be populated here -->
                        </div>
                    </div>

                    <div class="grid-panel chart-panel">
                        <div class="chart-controls">
                            <button class="chart-btn active" data-timeframe="1Min">1M</button>
                            <button class="chart-btn" data-timeframe="5Min">5M</button>
                            <button class="chart-btn" data-timeframe="15Min">15M</button>
                            <button class="chart-btn" data-timeframe="1Hour">1H</button>
                            <button class="chart-btn" data-timeframe="1Day">1D</button>
                        </div>
                        <div id="scanner-chart" style="height: 100%;"></div>
                    </div>

                    <div class="grid-panel trading-panel">
                        <div class="panel-header">
                            <h6 class="panel-title">Scanner Settings</h6>
                        </div>

                        <div class="order-panel">
                            <div class="mb-3">
                                <label class="form-label">Market Cap</label>
                                <select class="form-control" id="market-cap">
                                    <option value="all">All</option>
                                    <option value="large">Large Cap</option>
                                    <option value="mid">Mid Cap</option>
                                    <option value="small">Small Cap</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Min Volume</label>
                                <input type="number" class="form-control" id="min-volume" value="1000000">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">AI Confidence</label>
                                <input type="range" class="form-range" id="ai-confidence" min="50" max="100" value="80">
                                <div class="text-center"><span id="confidence-value">80%</span></div>
                            </div>
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="real-time-scan" checked>
                                <label class="form-check-label" for="real-time-scan">
                                    Real-time Scanning
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Alerts Tab -->
            <div class="tab-pane fade" id="alerts" role="tabpanel">
                <div class="trading-grid">
                    <div class="market-overview">
                        <div class="market-metric">
                            <div class="metric-value" id="total-alerts">23</div>
                            <div class="metric-label">Total Alerts</div>
                            <div class="metric-change positive">+5 Today</div>
                        </div>
                        <div class="market-metric">
                            <div class="metric-value positive" id="active-alerts">8</div>
                            <div class="metric-label">Active</div>
                            <div class="metric-change positive">Live</div>
                        </div>
                        <div class="market-metric">
                            <div class="metric-value" id="triggered-alerts">15</div>
                            <div class="metric-label">Triggered</div>
                            <div class="metric-change neutral">Today</div>
                        </div>
                        <div class="market-metric">
                            <div class="metric-value" id="alert-accuracy">87%</div>
                            <div class="metric-label">Accuracy</div>
                            <div class="metric-change positive">7-Day Avg</div>
                        </div>
                        <div class="market-metric">
                            <div class="metric-value" id="avg-profit">+2.3%</div>
                            <div class="metric-label">Avg Profit</div>
                            <div class="metric-change positive">Per Alert</div>
                        </div>
                    </div>

                    <div class="grid-panel" style="grid-column: 1 / 4; grid-row: 2;">
                        <div class="panel-header">
                            <h6 class="panel-title">Alert History</h6>
                            <div>
                                <button class="btn btn-sm btn-outline-primary me-2" onclick="exportAlerts()">
                                    <i class="fas fa-download"></i> Export
                                </button>
                                <button class="btn btn-sm btn-outline-secondary" onclick="clearAlerts()">
                                    <i class="fas fa-trash"></i> Clear
                                </button>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-dark table-striped">
                                <thead>
                                    <tr>
                                        <th>Time</th>
                                        <th>Symbol</th>
                                        <th>Signal</th>
                                        <th>Timeframes</th>
                                        <th>AI Score</th>
                                        <th>Price</th>
                                        <th>Status</th>
                                        <th>P&L</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="alerts-table">
                                    <!-- Alert history will be populated here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Profit Targets Tab -->
            <div class="tab-pane fade" id="targets" role="tabpanel">
                <div class="trading-grid">
                    <div class="market-overview">
                        <div class="market-metric">
                            <div class="metric-value" id="active-targets">3</div>
                            <div class="metric-label">Active Targets</div>
                            <div class="metric-change positive">Running</div>
                        </div>
                        <div class="market-metric">
                            <div class="metric-value positive" id="targets-achieved">12</div>
                            <div class="metric-label">Achieved</div>
                            <div class="metric-change positive">This Month</div>
                        </div>
                        <div class="market-metric">
                            <div class="metric-value" id="target-progress">67%</div>
                            <div class="metric-label">Progress</div>
                            <div class="metric-change positive">Daily Target</div>
                        </div>
                        <div class="market-metric">
                            <div class="metric-value" id="ai-efficiency">92%</div>
                            <div class="metric-label">AI Efficiency</div>
                            <div class="metric-change positive">Optimized</div>
                        </div>
                        <div class="market-metric">
                            <div class="metric-value positive" id="target-pnl">+$3,247</div>
                            <div class="metric-label">Target P&L</div>
                            <div class="metric-change positive">Today</div>
                        </div>
                    </div>

                    <div class="grid-panel" style="grid-column: 1 / 3; grid-row: 2;">
                        <div class="panel-header">
                            <h6 class="panel-title">Active Profit Targets</h6>
                            <button class="btn btn-sm btn-success" onclick="createTarget()">
                                <i class="fas fa-plus"></i> New Target
                            </button>
                        </div>
                        <div id="targets-container">
                            <!-- Profit targets will be populated here -->
                        </div>
                    </div>

                    <div class="grid-panel" style="grid-column: 3; grid-row: 2;">
                        <div class="panel-header">
                            <h6 class="panel-title">Target Controls</h6>
                        </div>

                        <div class="order-panel">
                            <div class="mb-3">
                                <label class="form-label">Target Type</label>
                                <select class="form-control" id="target-type">
                                    <option value="daily">Daily</option>
                                    <option value="weekly">Weekly</option>
                                    <option value="monthly">Monthly</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Target Amount ($)</label>
                                <input type="number" class="form-control" id="target-amount" value="1000">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Risk Level</label>
                                <select class="form-control" id="risk-level">
                                    <option value="conservative">Conservative</option>
                                    <option value="moderate">Moderate</option>
                                    <option value="aggressive">Aggressive</option>
                                </select>
                            </div>
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="ai-optimization" checked>
                                <label class="form-check-label" for="ai-optimization">
                                    AI Optimization
                                </label>
                            </div>
                            <div class="d-grid">
                                <button class="btn btn-trading btn-buy" onclick="createProfitTarget()">
                                    <i class="fas fa-bullseye me-1"></i>Create Target
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Performance Tab -->
            <div class="tab-pane fade" id="performance" role="tabpanel">
                <div class="trading-grid">
                    <div class="market-overview">
                        <div class="market-metric">
                            <div class="metric-value positive" id="total-return">+23.7%</div>
                            <div class="metric-label">Total Return</div>
                            <div class="metric-change positive">YTD</div>
                        </div>
                        <div class="market-metric">
                            <div class="metric-value" id="sharpe-ratio">1.84</div>
                            <div class="metric-label">Sharpe Ratio</div>
                            <div class="metric-change positive">Excellent</div>
                        </div>
                        <div class="market-metric">
                            <div class="metric-value" id="win-rate">68%</div>
                            <div class="metric-label">Win Rate</div>
                            <div class="metric-change positive">30-Day</div>
                        </div>
                        <div class="market-metric">
                            <div class="metric-value negative" id="max-drawdown">-4.2%</div>
                            <div class="metric-label">Max Drawdown</div>
                            <div class="metric-change positive">Low Risk</div>
                        </div>
                        <div class="market-metric">
                            <div class="metric-value" id="profit-factor">2.3</div>
                            <div class="metric-label">Profit Factor</div>
                            <div class="metric-change positive">Strong</div>
                        </div>
                    </div>

                    <div class="grid-panel" style="grid-column: 1 / 3; grid-row: 2;">
                        <div class="panel-header">
                            <h6 class="panel-title">Performance Chart</h6>
                            <div>
                                <button class="chart-btn active" data-period="1M">1M</button>
                                <button class="chart-btn" data-period="3M">3M</button>
                                <button class="chart-btn" data-period="6M">6M</button>
                                <button class="chart-btn" data-period="1Y">1Y</button>
                            </div>
                        </div>
                        <canvas id="performance-chart" style="height: 400px;"></canvas>
                    </div>

                    <div class="grid-panel" style="grid-column: 3; grid-row: 2;">
                        <div class="panel-header">
                            <h6 class="panel-title">Trade Statistics</h6>
                        </div>

                        <div class="order-panel">
                            <div class="d-flex justify-content-between mb-2">
                                <span>Total Trades:</span>
                                <span class="text-primary">247</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>Winning Trades:</span>
                                <span class="positive">168</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>Losing Trades:</span>
                                <span class="negative">79</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>Avg Win:</span>
                                <span class="positive">+$247</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>Avg Loss:</span>
                                <span class="negative">-$108</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>Best Trade:</span>
                                <span class="positive">+$1,247</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>Worst Trade:</span>
                                <span class="negative">-$342</span>
                            </div>
                            <hr>
                            <div class="d-flex justify-content-between mb-2">
                                <span>AI Accuracy:</span>
                                <span class="positive">87%</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>Model Confidence:</span>
                                <span class="positive">92%</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            initializeDashboard();
            startRealTimeUpdates();
        });

        function initializeDashboard() {
            // Initialize analytics panel
            showMarketOverview();

            // Load initial data
            loadAlerts();
            loadPositions();

            // Update time
            updateTime();
            setInterval(updateTime, 1000);
        }

        function initializeChart() {
            const chartContainer = document.getElementById('tradingview-chart');
            if (!chartContainer) return;

            // Clear any existing chart
            chartContainer.innerHTML = '';

            // Initialize TradingView-style chart
            const chart = LightweightCharts.createChart(chartContainer, {
                width: chartContainer.clientWidth,
                height: chartContainer.clientHeight,
                layout: {
                    backgroundColor: '#0d1421',
                    textColor: '#ffffff',
                },
                grid: {
                    vertLines: {
                        color: '#2d3748',
                    },
                    horzLines: {
                        color: '#2d3748',
                    },
                },
                crosshair: {
                    mode: LightweightCharts.CrosshairMode.Normal,
                },
                rightPriceScale: {
                    borderColor: '#2d3748',
                },
                timeScale: {
                    borderColor: '#2d3748',
                    timeVisible: true,
                    secondsVisible: false,
                },
            });

            const candlestickSeries = chart.addCandlestickSeries({
                upColor: '#00ff88',
                downColor: '#ff4757',
                borderDownColor: '#ff4757',
                borderUpColor: '#00ff88',
                wickDownColor: '#ff4757',
                wickUpColor: '#00ff88',
            });

            // Generate realistic sample data for AAPL
            const sampleData = generateSampleChartData();
            candlestickSeries.setData(sampleData);

            // Add volume series
            const volumeSeries = chart.addHistogramSeries({
                color: '#26a69a',
                priceFormat: {
                    type: 'volume',
                },
                priceScaleId: '',
                scaleMargins: {
                    top: 0.8,
                    bottom: 0,
                },
            });

            const volumeData = sampleData.map(d => ({
                time: d.time,
                value: Math.floor(Math.random() * 1000000) + 500000,
                color: d.close > d.open ? '#00ff88' : '#ff4757'
            }));

            volumeSeries.setData(volumeData);

            // Store chart reference for updates
            window.tradingChart = chart;
            window.candlestickSeries = candlestickSeries;

            // Handle resize
            window.addEventListener('resize', () => {
                chart.applyOptions({
                    width: chartContainer.clientWidth,
                    height: chartContainer.clientHeight,
                });
            });
        }

        function generateSampleChartData() {
            const data = [];
            const basePrice = 195.00;
            let currentPrice = basePrice;
            const now = new Date();

            for (let i = 100; i >= 0; i--) {
                const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
                const dateStr = date.toISOString().split('T')[0];

                const change = (Math.random() - 0.5) * 4; // Random change up to $2
                const open = currentPrice;
                const close = open + change;
                const high = Math.max(open, close) + Math.random() * 2;
                const low = Math.min(open, close) - Math.random() * 2;

                data.push({
                    time: dateStr,
                    open: parseFloat(open.toFixed(2)),
                    high: parseFloat(high.toFixed(2)),
                    low: parseFloat(low.toFixed(2)),
                    close: parseFloat(close.toFixed(2))
                });

                currentPrice = close;
            }

            return data;
        }

        function showMarketOverview() {
            const content = document.getElementById('analytics-content');
            content.innerHTML = `
                <div class="row g-3">
                    <div class="col-md-6">
                        <div class="order-panel">
                            <h6 class="text-primary mb-3"><i class="fas fa-chart-line me-2"></i>Market Sentiment</h6>
                            <div class="d-flex justify-content-between mb-2">
                                <span>Bull/Bear Ratio:</span>
                                <span class="positive">2.3:1</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>Market Breadth:</span>
                                <span class="positive">68% Advancing</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>Sector Rotation:</span>
                                <span class="neutral">Technology Leading</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>Options Flow:</span>
                                <span class="positive">Bullish</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="order-panel">
                            <h6 class="text-primary mb-3"><i class="fas fa-thermometer-half me-2"></i>Volatility Metrics</h6>
                            <div class="d-flex justify-content-between mb-2">
                                <span>VIX Level:</span>
                                <span class="neutral">22.45 (Normal)</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>VVIX:</span>
                                <span class="neutral">98.2</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>Term Structure:</span>
                                <span class="positive">Backwardation</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>Skew:</span>
                                <span class="neutral">15.2</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="order-panel">
                            <h6 class="text-primary mb-3"><i class="fas fa-globe me-2"></i>Global Markets</h6>
                            <div class="d-flex justify-content-between mb-2">
                                <span>S&P 500:</span>
                                <span class="positive">+0.85%</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>NASDAQ:</span>
                                <span class="positive">+1.23%</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>Russell 2000:</span>
                                <span class="negative">-0.34%</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>DXY:</span>
                                <span class="negative">-0.12%</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="order-panel">
                            <h6 class="text-primary mb-3"><i class="fas fa-clock me-2"></i>Market Schedule</h6>
                            <div class="d-flex justify-content-between mb-2">
                                <span>Market Status:</span>
                                <span class="positive">Open</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>Time to Close:</span>
                                <span class="neutral">3h 45m</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>Next Event:</span>
                                <span class="neutral">FOMC (2 days)</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>Earnings Today:</span>
                                <span class="neutral">12 Companies</span>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Update active button
            document.querySelectorAll('.chart-btn').forEach(btn => btn.classList.remove('active'));
            if (event?.target) event.target.classList.add('active');
        }

        function showSignalAnalytics() {
            const content = document.getElementById('analytics-content');
            content.innerHTML = `
                <div class="row g-3">
                    <div class="col-md-12">
                        <div class="order-panel">
                            <h6 class="text-primary mb-3"><i class="fas fa-signal me-2"></i>TTM Squeeze Signal Analytics</h6>
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="text-center mb-3">
                                        <div class="ai-gauge mx-auto mb-2">
                                            <div class="gauge-circle" style="background: conic-gradient(var(--accent-green) 280deg, var(--bg-tertiary) 0deg);">
                                                <div class="gauge-inner">78%</div>
                                            </div>
                                        </div>
                                        <small class="text-secondary">Signal Accuracy</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center mb-3">
                                        <div class="ai-gauge mx-auto mb-2">
                                            <div class="gauge-circle" style="background: conic-gradient(var(--accent-green) 310deg, var(--bg-tertiary) 0deg);">
                                                <div class="gauge-inner">86%</div>
                                            </div>
                                        </div>
                                        <small class="text-secondary">AI Confidence</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center mb-3">
                                        <div class="ai-gauge mx-auto mb-2">
                                            <div class="gauge-circle" style="background: conic-gradient(var(--accent-green) 245deg, var(--bg-tertiary) 0deg);">
                                                <div class="gauge-inner">68%</div>
                                            </div>
                                        </div>
                                        <small class="text-secondary">Win Rate</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center mb-3">
                                        <div class="ai-gauge mx-auto mb-2">
                                            <div class="gauge-circle" style="background: conic-gradient(var(--accent-green) 295deg, var(--bg-tertiary) 0deg);">
                                                <div class="gauge-inner">82%</div>
                                            </div>
                                        </div>
                                        <small class="text-secondary">Profit Factor</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Update active button
            document.querySelectorAll('.chart-btn').forEach(btn => btn.classList.remove('active'));
            if (event?.target) event.target.classList.add('active');
        }

        function showAIInsights() {
            const content = document.getElementById('analytics-content');
            content.innerHTML = `
                <div class="row g-3">
                    <div class="col-md-12">
                        <div class="order-panel">
                            <h6 class="text-primary mb-3"><i class="fas fa-brain me-2"></i>AI Model Performance</h6>
                            <div class="alert alert-info" style="background: var(--bg-tertiary); border: 1px solid var(--accent-blue); color: var(--text-primary);">
                                <i class="fas fa-lightbulb me-2"></i>
                                <strong>AI Insight:</strong> Current market conditions favor momentum-based strategies.
                                TTM Squeeze signals showing 23% higher success rate in trending markets.
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Update active button
            document.querySelectorAll('.chart-btn').forEach(btn => btn.classList.remove('active'));
            if (event?.target) event.target.classList.add('active');
        }

        function showRiskMetrics() {
            const content = document.getElementById('analytics-content');
            content.innerHTML = `
                <div class="row g-3">
                    <div class="col-md-6">
                        <div class="order-panel">
                            <h6 class="text-primary mb-3"><i class="fas fa-shield-alt me-2"></i>Portfolio Risk</h6>
                            <div class="d-flex justify-content-between mb-2">
                                <span>Portfolio Beta:</span>
                                <span class="neutral">1.12</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>Max Drawdown:</span>
                                <span class="positive">-4.2%</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>Sharpe Ratio:</span>
                                <span class="positive">1.84</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>VaR (95%):</span>
                                <span class="neutral">-$2,347</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="order-panel">
                            <h6 class="text-primary mb-3"><i class="fas fa-exclamation-triangle me-2"></i>Risk Alerts</h6>
                            <div class="d-flex justify-content-between mb-2">
                                <span>Concentration Risk:</span>
                                <span class="positive">Low</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>Correlation Risk:</span>
                                <span class="neutral">Medium</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>Liquidity Risk:</span>
                                <span class="positive">Low</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>Overnight Risk:</span>
                                <span class="positive">Low</span>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Update active button
            document.querySelectorAll('.chart-btn').forEach(btn => btn.classList.remove('active'));
            if (event?.target) event.target.classList.add('active');
        }

        function loadAlerts() {
            fetch('/api/scanner/alerts?limit=20')
                .then(response => response.json())
                .then(data => {
                    const container = document.getElementById('alerts-container');
                    container.innerHTML = '';

                    if (data.alerts && data.alerts.length > 0) {
                        data.alerts.forEach(alert => {
                            const alertElement = createAlertElement(alert);
                            container.appendChild(alertElement);
                        });
                    } else {
                        // Show sample alerts for demo
                        const sampleAlerts = [
                            { symbol: 'AAPL', signal_type: 'TTM Squeeze Fire', timeframes: ['5Min', '15Min'], ai_score: 87, price: 195.23 },
                            { symbol: 'TSLA', signal_type: 'Momentum Shift', timeframes: ['1Hour'], ai_score: 92, price: 248.67 },
                            { symbol: 'NVDA', signal_type: 'Breakout', timeframes: ['15Min', '1Hour'], ai_score: 78, price: 456.89 },
                            { symbol: 'MSFT', signal_type: 'TTM Squeeze Setup', timeframes: ['5Min'], ai_score: 83, price: 378.45 }
                        ];

                        sampleAlerts.forEach(alert => {
                            const alertElement = createAlertElement(alert);
                            container.appendChild(alertElement);
                        });
                    }
                })
                .catch(error => {
                    console.error('Error loading alerts:', error);
                    // Show sample data on error
                    loadSampleAlerts();
                });
        }

        function loadSampleAlerts() {
            const container = document.getElementById('alerts-container');
            const sampleAlerts = [
                { symbol: 'AAPL', signal_type: 'TTM Squeeze Fire', timeframes: ['5Min', '15Min'], ai_score: 87, price: 195.23 },
                { symbol: 'TSLA', signal_type: 'Momentum Shift', timeframes: ['1Hour'], ai_score: 92, price: 248.67 },
                { symbol: 'NVDA', signal_type: 'Breakout', timeframes: ['15Min', '1Hour'], ai_score: 78, price: 456.89 },
                { symbol: 'MSFT', signal_type: 'TTM Squeeze Setup', timeframes: ['5Min'], ai_score: 83, price: 378.45 }
            ];

            container.innerHTML = '';
            sampleAlerts.forEach(alert => {
                const alertElement = createAlertElement(alert);
                container.appendChild(alertElement);
            });
        }

        function createAlertElement(alert) {
            const div = document.createElement('div');
            div.className = 'alert-item';
            div.onclick = () => loadChart(alert.symbol);

            const aiScoreClass = alert.ai_score >= 85 ? 'positive' : alert.ai_score >= 70 ? 'neutral' : 'negative';

            div.innerHTML = `
                <div class="d-flex justify-content-between align-items-start mb-2">
                    <div class="alert-symbol">${alert.symbol}</div>
                    <div class="ai-gauge" style="width: 40px; height: 40px;">
                        <div class="gauge-circle" style="background: conic-gradient(var(--accent-green) ${alert.ai_score * 3.6}deg, var(--bg-tertiary) 0deg);">
                            <div class="gauge-inner" style="width: 30px; height: 30px; font-size: 0.7rem;">
                                ${alert.ai_score}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="alert-signal">${alert.signal_type}</div>
                <div class="d-flex justify-content-between align-items-center mt-2">
                    <div class="alert-timeframes">
                        ${alert.timeframes.map(tf => `<span class="timeframe-badge active">${tf}</span>`).join('')}
                    </div>
                    <div class="text-end">
                        <div class="text-primary" style="font-size: 0.9rem; font-weight: 600;">$${alert.price}</div>
                    </div>
                </div>
                <div class="mt-2">
                    <button class="btn btn-sm btn-trading btn-buy me-1" onclick="event.stopPropagation(); quickTrade('${alert.symbol}', 'buy')">
                        <i class="fas fa-arrow-up"></i>
                    </button>
                    <button class="btn btn-sm btn-trading btn-sell me-1" onclick="event.stopPropagation(); quickTrade('${alert.symbol}', 'sell')">
                        <i class="fas fa-arrow-down"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-primary" onclick="event.stopPropagation(); addToWatchlist('${alert.symbol}')">
                        <i class="fas fa-eye"></i>
                    </button>
                </div>
            `;

            return div;
        }

        function loadPositions() {
            fetch('/api/trading/positions')
                .then(response => response.json())
                .then(data => {
                    const container = document.getElementById('positions-container');
                    container.innerHTML = '';

                    if (data.positions && data.positions.length > 0) {
                        data.positions.forEach(position => {
                            const positionElement = createPositionElement(position);
                            container.appendChild(positionElement);
                        });
                    } else {
                        // Show sample positions for demo
                        const samplePositions = [
                            { symbol: 'AAPL', qty: 100, avg_entry_price: 193.45, current_price: 195.23, unrealized_pl: 178.00 },
                            { symbol: 'TSLA', qty: 50, avg_entry_price: 245.30, current_price: 248.67, unrealized_pl: 168.50 },
                            { symbol: 'NVDA', qty: 25, avg_entry_price: 458.90, current_price: 456.89, unrealized_pl: -50.25 }
                        ];

                        samplePositions.forEach(position => {
                            const positionElement = createPositionElement(position);
                            container.appendChild(positionElement);
                        });
                    }
                })
                .catch(error => {
                    console.error('Error loading positions:', error);
                    loadSamplePositions();
                });
        }

        function loadSamplePositions() {
            const container = document.getElementById('positions-container');
            const samplePositions = [
                { symbol: 'AAPL', qty: 100, avg_entry_price: 193.45, current_price: 195.23, unrealized_pl: 178.00 },
                { symbol: 'TSLA', qty: 50, avg_entry_price: 245.30, current_price: 248.67, unrealized_pl: 168.50 },
                { symbol: 'NVDA', qty: 25, avg_entry_price: 458.90, current_price: 456.89, unrealized_pl: -50.25 }
            ];

            container.innerHTML = '';
            samplePositions.forEach(position => {
                const positionElement = createPositionElement(position);
                container.appendChild(positionElement);
            });
        }

        function createPositionElement(position) {
            const div = document.createElement('div');
            div.className = 'alert-item';

            const pnlClass = position.unrealized_pl >= 0 ? 'positive' : 'negative';
            const pnlPercent = ((position.current_price - position.avg_entry_price) / position.avg_entry_price * 100).toFixed(2);
            const marketValue = (position.qty * position.current_price).toFixed(2);

            div.innerHTML = `
                <div class="d-flex justify-content-between align-items-start mb-2">
                    <div class="alert-symbol">${position.symbol}</div>
                    <div class="text-end">
                        <div class="${pnlClass}" style="font-weight: 700;">$${position.unrealized_pl.toFixed(2)}</div>
                        <div class="${pnlClass}" style="font-size: 0.8rem;">${pnlPercent >= 0 ? '+' : ''}${pnlPercent}%</div>
                    </div>
                </div>
                <div class="alert-signal">${position.qty} shares @ $${position.avg_entry_price}</div>
                <div class="d-flex justify-content-between align-items-center mt-2">
                    <div style="font-size: 0.8rem; color: var(--text-secondary);">
                        Current: $${position.current_price}
                    </div>
                    <div style="font-size: 0.8rem; color: var(--text-secondary);">
                        Value: $${marketValue}
                    </div>
                </div>
                <div class="mt-2">
                    <button class="btn btn-sm btn-trading btn-sell me-1" onclick="closePosition('${position.symbol}')">
                        <i class="fas fa-times"></i> Close
                    </button>
                    <button class="btn btn-sm btn-outline-primary me-1" onclick="loadChart('${position.symbol}')">
                        <i class="fas fa-chart-line"></i> Chart
                    </button>
                    <button class="btn btn-sm btn-outline-secondary" onclick="setStopLoss('${position.symbol}')">
                        <i class="fas fa-shield-alt"></i> Stop
                    </button>
                </div>
            `;

            return div;
        }

        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('en-US', {
                timeZone: 'America/Chicago',
                hour12: true,
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            document.getElementById('live-time').textContent = timeString + ' CST';
        }

        function startRealTimeUpdates() {
            // Update alerts every 5 seconds
            setInterval(loadAlerts, 5000);

            // Update positions every 10 seconds
            setInterval(loadPositions, 10000);

            // Update market data every 3 seconds
            setInterval(updateMarketData, 3000);

            // Simulate real-time price updates
            setInterval(simulatePriceUpdates, 2000);
        }

        function updateMarketData() {
            // Simulate market data updates
            const spyPrice = (425 + Math.random() * 10).toFixed(2);
            const spyChange = ((Math.random() - 0.5) * 2).toFixed(2);
            const vixValue = (20 + Math.random() * 10).toFixed(2);

            document.getElementById('spy-price').textContent = `$${spyPrice}`;
            document.getElementById('spy-change').textContent = `${spyChange >= 0 ? '+' : ''}${spyChange}%`;
            document.getElementById('vix-value').textContent = vixValue;

            // Update portfolio P&L
            const pnl = (1000 + Math.random() * 500).toFixed(0);
            document.getElementById('portfolio-pnl').textContent = `+$${pnl}`;
        }

        function simulatePriceUpdates() {
            // Simulate real-time price updates for positions
            const positions = document.querySelectorAll('.alert-item');
            positions.forEach(position => {
                // Add subtle animation for live updates
                position.style.backgroundColor = 'var(--hover-bg)';
                setTimeout(() => {
                    position.style.backgroundColor = 'var(--bg-secondary)';
                }, 200);
            });
        }

        function refreshAlerts() {
            loadAlerts();
            showNotification('Alerts refreshed', 'info');
        }

        function loadChart(symbol) {
            // Load chart for selected symbol
            document.getElementById('order-symbol').value = symbol;
            showNotification(`Loading chart for ${symbol}`, 'info');

            // Switch to trading tab if not already there
            const tradingTab = document.getElementById('trading-tab');
            tradingTab.click();
        }

        function placeOrder(side) {
            const symbol = document.getElementById('order-symbol').value;
            const quantity = document.getElementById('order-quantity').value;
            const orderType = document.getElementById('order-type').value;

            if (!symbol || !quantity) {
                showNotification('Please enter symbol and quantity', 'warning');
                return;
            }

            // Simulate order placement
            showNotification(`${side.toUpperCase()} order placed: ${quantity} ${symbol}`, 'success');
            console.log(`Placing ${side} order: ${quantity} ${symbol} (${orderType})`);
        }

        function quickTrade(symbol, side) {
            // Quick trade from alert
            document.getElementById('order-symbol').value = symbol;
            document.getElementById('order-quantity').value = '100'; // Default quantity

            showNotification(`Quick ${side.toUpperCase()} order prepared for ${symbol}`, 'info');

            // Switch to trading tab
            const tradingTab = document.getElementById('trading-tab');
            tradingTab.click();
        }

        function addToWatchlist(symbol) {
            showNotification(`${symbol} added to watchlist`, 'success');
        }

        function closePosition(symbol) {
            if (confirm(`Close position in ${symbol}?`)) {
                showNotification(`Position in ${symbol} closed`, 'success');
                setTimeout(loadPositions, 1000); // Refresh positions
            }
        }

        function setStopLoss(symbol) {
            const stopPrice = prompt(`Enter stop loss price for ${symbol}:`);
            if (stopPrice) {
                showNotification(`Stop loss set for ${symbol} at $${stopPrice}`, 'success');
            }
        }

        function showNotification(message, type) {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 80px; right: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 3000);
        }

        // Chart timeframe buttons
        document.querySelectorAll('.chart-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.chart-btn').forEach(b => b.classList.remove('active'));
                this.classList.add('active');

                const timeframe = this.dataset.timeframe;
                showNotification(`Loading ${timeframe} chart`, 'info');
                console.log('Loading chart for timeframe:', timeframe);
            });
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl/Cmd + number keys for tab switching
            if ((e.ctrlKey || e.metaKey) && e.key >= '1' && e.key <= '5') {
                e.preventDefault();
                const tabIndex = parseInt(e.key) - 1;
                const tabs = ['trading-tab', 'scanner-tab', 'alerts-tab', 'targets-tab', 'performance-tab'];
                if (tabs[tabIndex]) {
                    document.getElementById(tabs[tabIndex]).click();
                }
            }

            // F5 for refresh
            if (e.key === 'F5') {
                e.preventDefault();
                refreshAlerts();
            }

            // Escape to clear order form
            if (e.key === 'Escape') {
                document.getElementById('order-symbol').value = '';
                document.getElementById('order-quantity').value = '';
            }

            // Enter to place buy order when in symbol field
            if (e.key === 'Enter' && e.target.id === 'order-symbol') {
                e.preventDefault();
                placeOrder('buy');
            }
        });

        // Additional scanner functions
        function startScan() {
            showNotification('Scanner started', 'success');
            // Simulate scanning
            document.getElementById('scan-total').textContent = '247';
            document.getElementById('squeeze-signals').textContent = '12';
        }

        function stopScan() {
            showNotification('Scanner stopped', 'warning');
        }

        // Target functions
        function createTarget() {
            showNotification('Opening target creation dialog', 'info');
        }

        function createProfitTarget() {
            const targetType = document.getElementById('target-type').value;
            const targetAmount = document.getElementById('target-amount').value;
            const riskLevel = document.getElementById('risk-level').value;

            if (!targetAmount) {
                showNotification('Please enter target amount', 'warning');
                return;
            }

            showNotification(`${targetType} profit target of $${targetAmount} created (${riskLevel} risk)`, 'success');
        }

        // Performance chart initialization
        function initializePerformanceChart() {
            const ctx = document.getElementById('performance-chart');
            if (ctx) {
                new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                        datasets: [{
                            label: 'Portfolio Value',
                            data: [50000, 52000, 48000, 55000, 58000, 59791],
                            borderColor: '#00ff88',
                            backgroundColor: 'rgba(0, 255, 136, 0.1)',
                            tension: 0.4
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: {
                                labels: {
                                    color: '#ffffff'
                                }
                            }
                        },
                        scales: {
                            x: {
                                ticks: {
                                    color: '#b8bcc8'
                                },
                                grid: {
                                    color: '#2d3748'
                                }
                            },
                            y: {
                                ticks: {
                                    color: '#b8bcc8'
                                },
                                grid: {
                                    color: '#2d3748'
                                }
                            }
                        }
                    }
                });
            }
        }

        // Initialize performance chart when performance tab is shown
        document.getElementById('performance-tab').addEventListener('shown.bs.tab', function() {
            setTimeout(initializePerformanceChart, 100);
        });

        // AI confidence slider
        const confidenceSlider = document.getElementById('ai-confidence');
        if (confidenceSlider) {
            confidenceSlider.addEventListener('input', function() {
                document.getElementById('confidence-value').textContent = this.value + '%';
            });
        }
    </script>
</body>
</html>
