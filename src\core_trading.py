"""
Consolidated Trading Module for TTM Squeeze Trading System
Combines Alpaca trading, portfolio management, and risk management
"""

import asyncio
import aiohttp
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta
from dataclasses import dataclass
import logging
import json

logger = logging.getLogger(__name__)

# ============================================================================
# TRADING DATA STRUCTURES
# ============================================================================

@dataclass
class Position:
    """Trading position data"""
    symbol: str
    quantity: float
    side: str  # 'long' or 'short'
    avg_entry_price: float
    current_price: float
    market_value: float
    unrealized_pnl: float
    unrealized_pnl_percent: float
    timestamp: datetime

@dataclass
class Order:
    """Trading order data"""
    id: str
    symbol: str
    side: str  # 'buy' or 'sell'
    quantity: float
    order_type: str  # 'market', 'limit', 'stop'
    status: str
    filled_quantity: float
    avg_fill_price: float
    timestamp: datetime

@dataclass
class Account:
    """Account information"""
    id: str
    status: str
    buying_power: float
    portfolio_value: float
    cash: float
    long_market_value: float
    short_market_value: float
    equity: float
    last_equity: float
    multiplier: float
    initial_margin: float
    maintenance_margin: float
    daytrade_count: int
    sma: float

# ============================================================================
# ALPACA TRADING MANAGER
# ============================================================================

class AlpacaTradingManager:
    """Alpaca trading interface with comprehensive order management"""
    
    def __init__(self, api_key: str, secret_key: str, base_url: str = "https://paper-api.alpaca.markets"):
        self.api_key = api_key
        self.secret_key = secret_key
        self.base_url = base_url
        self.session = None
        self.connected = False
        
        # Trading state
        self.positions_cache = {}
        self.orders_cache = {}
        self.account_cache = None
        self.cache_expiry = timedelta(seconds=30)
        self.last_cache_update = None
        
        logger.info(f"Alpaca trading manager initialized - {base_url}")
    
    async def connect(self) -> bool:
        """Connect to Alpaca trading API"""
        try:
            if not self.session:
                self.session = aiohttp.ClientSession(
                    headers={
                        'APCA-API-KEY-ID': self.api_key,
                        'APCA-API-SECRET-KEY': self.secret_key,
                        'Content-Type': 'application/json'
                    },
                    timeout=aiohttp.ClientTimeout(total=30)
                )
            
            # Test connection
            account = await self.get_account()
            if account:
                self.connected = True
                logger.info(f"Connected to Alpaca trading - Account: {account.id}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Failed to connect to Alpaca trading: {e}")
            return False
    
    async def get_account(self) -> Optional[Account]:
        """Get account information"""
        try:
            # Check cache
            if (self.account_cache and self.last_cache_update and 
                datetime.now() - self.last_cache_update < self.cache_expiry):
                return self.account_cache
            
            async with self.session.get(f"{self.base_url}/v2/account") as response:
                if response.status == 200:
                    data = await response.json()
                    
                    account = Account(
                        id=data.get('id', ''),
                        status=data.get('status', ''),
                        buying_power=float(data.get('buying_power', 0)),
                        portfolio_value=float(data.get('portfolio_value', 0)),
                        cash=float(data.get('cash', 0)),
                        long_market_value=float(data.get('long_market_value', 0)),
                        short_market_value=float(data.get('short_market_value', 0)),
                        equity=float(data.get('equity', 0)),
                        last_equity=float(data.get('last_equity', 0)),
                        multiplier=float(data.get('multiplier', 1)),
                        initial_margin=float(data.get('initial_margin', 0)),
                        maintenance_margin=float(data.get('maintenance_margin', 0)),
                        daytrade_count=int(data.get('daytrade_count', 0)),
                        sma=float(data.get('sma', 0))
                    )
                    
                    # Update cache
                    self.account_cache = account
                    self.last_cache_update = datetime.now()
                    
                    return account
                else:
                    logger.error(f"Account request failed: {response.status}")
                    return None
                    
        except Exception as e:
            logger.error(f"Error getting account: {e}")
            return None
    
    async def get_positions(self) -> List[Position]:
        """Get current positions"""
        try:
            async with self.session.get(f"{self.base_url}/v2/positions") as response:
                if response.status == 200:
                    data = await response.json()
                    positions = []
                    
                    for pos_data in data:
                        position = Position(
                            symbol=pos_data.get('symbol', ''),
                            quantity=float(pos_data.get('qty', 0)),
                            side='long' if float(pos_data.get('qty', 0)) > 0 else 'short',
                            avg_entry_price=float(pos_data.get('avg_entry_price', 0)),
                            current_price=float(pos_data.get('current_price', 0)),
                            market_value=float(pos_data.get('market_value', 0)),
                            unrealized_pnl=float(pos_data.get('unrealized_pl', 0)),
                            unrealized_pnl_percent=float(pos_data.get('unrealized_plpc', 0)),
                            timestamp=datetime.now()
                        )
                        positions.append(position)
                    
                    return positions
                else:
                    logger.error(f"Positions request failed: {response.status}")
                    return []
                    
        except Exception as e:
            logger.error(f"Error getting positions: {e}")
            return []
    
    async def place_order(self, symbol: str, side: str, qty: Union[int, float], 
                         type: str = 'market', time_in_force: str = 'day',
                         limit_price: Optional[float] = None,
                         stop_price: Optional[float] = None) -> Dict[str, Any]:
        """Place a trading order"""
        try:
            order_data = {
                'symbol': symbol.upper(),
                'side': side.lower(),
                'type': type.lower(),
                'qty': str(qty),
                'time_in_force': time_in_force.lower()
            }
            
            # Add price parameters if specified
            if limit_price is not None:
                order_data['limit_price'] = str(limit_price)
            
            if stop_price is not None:
                order_data['stop_price'] = str(stop_price)
            
            async with self.session.post(
                f"{self.base_url}/v2/orders",
                json=order_data
            ) as response:
                if response.status == 201:
                    result = await response.json()
                    logger.info(f"Order placed: {side} {qty} {symbol} @ {type}")
                    return {
                        'success': True,
                        'order_id': result.get('id'),
                        'status': result.get('status'),
                        'symbol': symbol,
                        'side': side,
                        'quantity': qty,
                        'type': type
                    }
                else:
                    error_text = await response.text()
                    logger.error(f"Order failed: {response.status} - {error_text}")
                    return {
                        'success': False,
                        'error': f"HTTP {response.status}: {error_text}",
                        'symbol': symbol,
                        'side': side,
                        'quantity': qty
                    }
                    
        except Exception as e:
            logger.error(f"Error placing order: {e}")
            return {
                'success': False,
                'error': str(e),
                'symbol': symbol,
                'side': side,
                'quantity': qty
            }
    
    async def get_orders(self, status: str = 'all', limit: int = 50) -> List[Order]:
        """Get orders"""
        try:
            params = {'limit': limit}
            if status != 'all':
                params['status'] = status
            
            async with self.session.get(f"{self.base_url}/v2/orders", params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    orders = []
                    
                    for order_data in data:
                        order = Order(
                            id=order_data.get('id', ''),
                            symbol=order_data.get('symbol', ''),
                            side=order_data.get('side', ''),
                            quantity=float(order_data.get('qty', 0)),
                            order_type=order_data.get('type', ''),
                            status=order_data.get('status', ''),
                            filled_quantity=float(order_data.get('filled_qty', 0)),
                            avg_fill_price=float(order_data.get('filled_avg_price', 0) or 0),
                            timestamp=datetime.fromisoformat(order_data.get('created_at', '').replace('Z', '+00:00'))
                        )
                        orders.append(order)
                    
                    return orders
                else:
                    logger.error(f"Orders request failed: {response.status}")
                    return []
                    
        except Exception as e:
            logger.error(f"Error getting orders: {e}")
            return []
    
    async def cancel_order(self, order_id: str) -> bool:
        """Cancel an order"""
        try:
            async with self.session.delete(f"{self.base_url}/v2/orders/{order_id}") as response:
                if response.status == 204:
                    logger.info(f"Order cancelled: {order_id}")
                    return True
                else:
                    logger.error(f"Cancel order failed: {response.status}")
                    return False
                    
        except Exception as e:
            logger.error(f"Error cancelling order: {e}")
            return False
    
    async def close_position(self, symbol: str, percentage: float = 100.0) -> Dict[str, Any]:
        """Close a position"""
        try:
            close_data = {}
            if percentage < 100.0:
                close_data['percentage'] = str(percentage)
            
            async with self.session.delete(
                f"{self.base_url}/v2/positions/{symbol}",
                json=close_data if close_data else None
            ) as response:
                if response.status == 207:
                    result = await response.json()
                    logger.info(f"Position closed: {symbol} ({percentage}%)")
                    return {'success': True, 'result': result}
                else:
                    error_text = await response.text()
                    logger.error(f"Close position failed: {response.status} - {error_text}")
                    return {'success': False, 'error': error_text}
                    
        except Exception as e:
            logger.error(f"Error closing position: {e}")
            return {'success': False, 'error': str(e)}
    
    async def close(self):
        """Close the session"""
        if self.session:
            await self.session.close()
            self.session = None
            self.connected = False

# ============================================================================
# PORTFOLIO MANAGER
# ============================================================================

class PortfolioManager:
    """Portfolio management and analysis"""
    
    def __init__(self, trading_manager: AlpacaTradingManager):
        self.trading_manager = trading_manager
        self.performance_history = []
        
    async def get_portfolio_summary(self) -> Dict[str, Any]:
        """Get comprehensive portfolio summary"""
        try:
            account = await self.trading_manager.get_account()
            positions = await self.trading_manager.get_positions()
            
            if not account:
                return {'error': 'Unable to get account information'}
            
            # Calculate portfolio metrics
            total_positions = len(positions)
            long_positions = len([p for p in positions if p.side == 'long'])
            short_positions = len([p for p in positions if p.side == 'short'])
            
            total_unrealized_pnl = sum(p.unrealized_pnl for p in positions)
            total_market_value = sum(abs(p.market_value) for p in positions)
            
            # Calculate day change
            day_change = account.equity - account.last_equity
            day_change_percent = (day_change / account.last_equity * 100) if account.last_equity > 0 else 0
            
            # Position breakdown
            position_breakdown = []
            for position in positions:
                position_breakdown.append({
                    'symbol': position.symbol,
                    'quantity': position.quantity,
                    'side': position.side,
                    'market_value': position.market_value,
                    'unrealized_pnl': position.unrealized_pnl,
                    'unrealized_pnl_percent': position.unrealized_pnl_percent,
                    'weight': (abs(position.market_value) / total_market_value * 100) if total_market_value > 0 else 0
                })
            
            summary = {
                'account_id': account.id,
                'account_status': account.status,
                'portfolio_value': account.portfolio_value,
                'buying_power': account.buying_power,
                'cash': account.cash,
                'equity': account.equity,
                'day_change': day_change,
                'day_change_percent': day_change_percent,
                'total_positions': total_positions,
                'long_positions': long_positions,
                'short_positions': short_positions,
                'total_unrealized_pnl': total_unrealized_pnl,
                'total_market_value': total_market_value,
                'daytrade_count': account.daytrade_count,
                'multiplier': account.multiplier,
                'positions': position_breakdown,
                'timestamp': datetime.now().isoformat()
            }
            
            return summary
            
        except Exception as e:
            logger.error(f"Error getting portfolio summary: {e}")
            return {'error': str(e)}
    
    async def calculate_position_size(self, symbol: str, risk_percent: float = 2.0, 
                                    stop_loss_percent: float = 5.0) -> Dict[str, Any]:
        """Calculate optimal position size based on risk management"""
        try:
            account = await self.trading_manager.get_account()
            if not account:
                return {'error': 'Unable to get account information'}
            
            # Get current price (simplified - would use real-time quote)
            portfolio_value = account.portfolio_value
            risk_amount = portfolio_value * (risk_percent / 100)
            
            # Calculate position size
            # This is a simplified calculation - in practice, you'd get the actual current price
            estimated_price = 100.0  # Placeholder
            stop_loss_amount = estimated_price * (stop_loss_percent / 100)
            
            if stop_loss_amount > 0:
                shares = int(risk_amount / stop_loss_amount)
                position_value = shares * estimated_price
                position_percent = (position_value / portfolio_value * 100) if portfolio_value > 0 else 0
                
                return {
                    'symbol': symbol,
                    'recommended_shares': shares,
                    'estimated_price': estimated_price,
                    'position_value': position_value,
                    'position_percent': position_percent,
                    'risk_amount': risk_amount,
                    'risk_percent': risk_percent,
                    'stop_loss_percent': stop_loss_percent,
                    'max_loss': risk_amount
                }
            else:
                return {'error': 'Invalid stop loss calculation'}
                
        except Exception as e:
            logger.error(f"Error calculating position size: {e}")
            return {'error': str(e)}
    
    async def get_performance_metrics(self, days: int = 30) -> Dict[str, Any]:
        """Calculate performance metrics"""
        try:
            account = await self.trading_manager.get_account()
            if not account:
                return {'error': 'Unable to get account information'}
            
            # This is simplified - in practice, you'd track historical performance
            current_equity = account.equity
            last_equity = account.last_equity
            
            # Calculate basic metrics
            daily_return = ((current_equity - last_equity) / last_equity * 100) if last_equity > 0 else 0
            
            # Placeholder metrics (would be calculated from historical data)
            metrics = {
                'current_equity': current_equity,
                'daily_return': daily_return,
                'total_return': 0.0,  # Would calculate from inception
                'max_drawdown': 0.0,  # Would track maximum drawdown
                'sharpe_ratio': 0.0,  # Would calculate from returns
                'win_rate': 0.0,      # Would track from completed trades
                'profit_factor': 0.0, # Would calculate from wins/losses
                'avg_win': 0.0,       # Average winning trade
                'avg_loss': 0.0,      # Average losing trade
                'total_trades': 0,    # Total number of trades
                'winning_trades': 0,  # Number of winning trades
                'losing_trades': 0,   # Number of losing trades
                'timestamp': datetime.now().isoformat()
            }
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error calculating performance metrics: {e}")
            return {'error': str(e)}

# ============================================================================
# RISK MANAGER
# ============================================================================

class RiskManager:
    """Risk management and position monitoring"""
    
    def __init__(self, trading_manager: AlpacaTradingManager):
        self.trading_manager = trading_manager
        self.max_portfolio_risk = 0.10  # 10% max portfolio risk
        self.max_position_size = 0.05   # 5% max single position
        self.max_daily_loss = 0.03      # 3% max daily loss
        
    async def check_risk_limits(self) -> Dict[str, Any]:
        """Check current risk limits"""
        try:
            account = await self.trading_manager.get_account()
            positions = await self.trading_manager.get_positions()
            
            if not account:
                return {'error': 'Unable to get account information'}
            
            # Calculate current risks
            portfolio_value = account.portfolio_value
            total_exposure = sum(abs(p.market_value) for p in positions)
            portfolio_risk = (total_exposure / portfolio_value) if portfolio_value > 0 else 0
            
            # Check daily loss
            daily_pnl = account.equity - account.last_equity
            daily_loss_percent = abs(daily_pnl / account.last_equity) if account.last_equity > 0 and daily_pnl < 0 else 0
            
            # Check position sizes
            oversized_positions = []
            for position in positions:
                position_percent = abs(position.market_value) / portfolio_value if portfolio_value > 0 else 0
                if position_percent > self.max_position_size:
                    oversized_positions.append({
                        'symbol': position.symbol,
                        'current_size': position_percent,
                        'max_allowed': self.max_position_size,
                        'excess': position_percent - self.max_position_size
                    })
            
            # Risk warnings
            warnings = []
            if portfolio_risk > self.max_portfolio_risk:
                warnings.append(f"Portfolio risk ({portfolio_risk:.1%}) exceeds limit ({self.max_portfolio_risk:.1%})")
            
            if daily_loss_percent > self.max_daily_loss:
                warnings.append(f"Daily loss ({daily_loss_percent:.1%}) exceeds limit ({self.max_daily_loss:.1%})")
            
            if oversized_positions:
                warnings.append(f"{len(oversized_positions)} positions exceed size limits")
            
            risk_check = {
                'portfolio_risk': portfolio_risk,
                'max_portfolio_risk': self.max_portfolio_risk,
                'daily_loss_percent': daily_loss_percent,
                'max_daily_loss': self.max_daily_loss,
                'total_positions': len(positions),
                'oversized_positions': oversized_positions,
                'warnings': warnings,
                'risk_level': 'high' if warnings else 'normal',
                'timestamp': datetime.now().isoformat()
            }
            
            return risk_check
            
        except Exception as e:
            logger.error(f"Error checking risk limits: {e}")
            return {'error': str(e)}
    
    async def suggest_risk_actions(self) -> List[str]:
        """Suggest risk management actions"""
        try:
            risk_check = await self.check_risk_limits()
            
            if risk_check.get('error'):
                return ['Unable to assess risk - check connection']
            
            suggestions = []
            
            # Portfolio risk suggestions
            if risk_check['portfolio_risk'] > self.max_portfolio_risk:
                suggestions.append("Reduce overall portfolio exposure")
                suggestions.append("Consider closing some positions")
            
            # Daily loss suggestions
            if risk_check['daily_loss_percent'] > self.max_daily_loss:
                suggestions.append("Stop trading for today - daily loss limit reached")
                suggestions.append("Review and analyze losing trades")
            
            # Position size suggestions
            oversized = risk_check.get('oversized_positions', [])
            if oversized:
                for pos in oversized:
                    suggestions.append(f"Reduce {pos['symbol']} position size by {pos['excess']:.1%}")
            
            # General suggestions
            if not suggestions:
                suggestions.append("Risk levels are within acceptable limits")
                suggestions.append("Continue monitoring positions closely")
            
            return suggestions
            
        except Exception as e:
            logger.error(f"Error generating risk suggestions: {e}")
            return ['Error generating risk suggestions']
