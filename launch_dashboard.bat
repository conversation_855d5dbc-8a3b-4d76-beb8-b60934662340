@echo off
echo ========================================
echo   TTM Squeeze Professional Dashboard
echo ========================================
echo.
echo Starting the professional trading dashboard...
echo.

REM Try different Python executables
echo Attempting to launch with python...
python standalone_dashboard.py
if %errorlevel% equ 0 goto :success

echo.
echo Attempting to launch with py...
py standalone_dashboard.py
if %errorlevel% equ 0 goto :success

echo.
echo Attempting to launch with py -3...
py -3 standalone_dashboard.py
if %errorlevel% equ 0 goto :success

echo.
echo Attempting to launch with full Python path...
C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe standalone_dashboard.py
if %errorlevel% equ 0 goto :success

echo.
echo Attempting to launch main application...
python main.py
if %errorlevel% equ 0 goto :success

echo.
echo ========================================
echo   Opening HTML Demo Instead
echo ========================================
echo.
echo Python environment issue detected.
echo Opening standalone HTML demo...
echo.
start professional_dashboard_demo.html
echo.
echo Professional Dashboard Demo opened in your browser!
echo.
echo Features available in the demo:
echo - Professional Bloomberg Terminal-style interface
echo - Real-time market data display
echo - TTM Squeeze alerts with AI confidence scores
echo - Market analytics and insights
echo - Professional order entry panel
echo.
goto :end

:success
echo.
echo ========================================
echo   Dashboard Successfully Launched!
echo ========================================
echo.
echo Access your professional dashboard at:
echo   http://127.0.0.1:5000/pro
echo.
echo Available interfaces:
echo   - Professional Dashboard: http://127.0.0.1:5000/pro
echo   - Simple Interface: http://127.0.0.1:5000/simple
echo   - Main Interface: http://127.0.0.1:5000
echo.
echo Press Ctrl+C to stop the server
echo.

:end
pause
