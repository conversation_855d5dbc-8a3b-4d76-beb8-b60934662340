"""
Profit Target Configuration System
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Union
import logging
from datetime import datetime, timedelta, time
from dataclasses import dataclass, asdict
from enum import Enum
import json

logger = logging.getLogger(__name__)

class TargetType(Enum):
    ABSOLUTE_DOLLAR = "absolute_dollar"
    PERCENTAGE_ACCOUNT = "percentage_account"
    PERCENTAGE_DAILY = "percentage_daily"
    RISK_ADJUSTED = "risk_adjusted"

class TimeFrame(Enum):
    DAILY = "daily"
    WEEKLY = "weekly"
    MONTHLY = "monthly"
    SESSION = "session"  # Single trading session

class TargetStatus(Enum):
    ACTIVE = "active"
    ACHIEVED = "achieved"
    PAUSED = "paused"
    EXPIRED = "expired"
    CANCELLED = "cancelled"

@dataclass
class ProfitTarget:
    """Individual profit target configuration"""
    target_id: str
    name: str
    target_type: TargetType
    target_value: float  # Dollar amount or percentage
    timeframe: TimeFrame
    start_date: datetime
    end_date: Optional[datetime]
    max_drawdown_pct: float  # Maximum acceptable drawdown
    max_loss_limit: float   # Stop loss override amount
    status: TargetStatus
    
    # Progress tracking
    current_progress: float = 0.0
    trades_executed: int = 0
    achievement_probability: float = 0.0
    
    # Risk management
    conservative_threshold: float = 0.8  # Become conservative at 80% of target
    halt_on_achievement: bool = True
    
    # Time constraints
    trading_start_time: Optional[time] = None
    trading_end_time: Optional[time] = None
    
    # Advanced settings
    min_trade_size_pct: float = 0.01  # Minimum 1% position size
    max_trade_size_pct: float = 0.10  # Maximum 10% position size
    
    created_at: datetime = None
    updated_at: datetime = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()
        self.updated_at = datetime.now()

@dataclass
class TargetAchievementHistory:
    """Historical record of target achievements"""
    target_id: str
    target_name: str
    target_value: float
    achieved_value: float
    achievement_date: datetime
    time_to_achieve: timedelta
    trades_count: int
    win_rate: float
    max_drawdown_experienced: float
    ai_efficiency_score: float

class ProfitTargetManager:
    """Manages profit targets and their configurations"""
    
    def __init__(self):
        self.active_targets = {}  # target_id -> ProfitTarget
        self.achievement_history = []
        self.config_file = "data/profit_targets.json"
        self.history_file = "data/target_achievements.json"
        
        # Load existing configurations
        self._load_configurations()
        
        logger.info("Profit Target Manager initialized")
    
    def create_target(self, target_config: Dict) -> str:
        """
        Create a new profit target
        
        Args:
            target_config: Target configuration dictionary
            
        Returns:
            Target ID of created target
        """
        try:
            # Generate unique target ID
            target_id = f"target_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # Validate configuration
            validated_config = self._validate_target_config(target_config)
            
            # Create target object
            target = ProfitTarget(
                target_id=target_id,
                name=validated_config['name'],
                target_type=TargetType(validated_config['target_type']),
                target_value=validated_config['target_value'],
                timeframe=TimeFrame(validated_config['timeframe']),
                start_date=validated_config.get('start_date', datetime.now()),
                end_date=validated_config.get('end_date'),
                max_drawdown_pct=validated_config.get('max_drawdown_pct', 0.05),
                max_loss_limit=validated_config.get('max_loss_limit', 1000.0),
                status=TargetStatus.ACTIVE,
                conservative_threshold=validated_config.get('conservative_threshold', 0.8),
                halt_on_achievement=validated_config.get('halt_on_achievement', True),
                trading_start_time=validated_config.get('trading_start_time'),
                trading_end_time=validated_config.get('trading_end_time'),
                min_trade_size_pct=validated_config.get('min_trade_size_pct', 0.01),
                max_trade_size_pct=validated_config.get('max_trade_size_pct', 0.10)
            )
            
            # Store target
            self.active_targets[target_id] = target
            
            # Save to file
            self._save_configurations()
            
            logger.info(f"Created profit target: {target.name} - "
                       f"{target.target_value} ({target.target_type.value})")
            
            return target_id
            
        except Exception as e:
            logger.error(f"Error creating profit target: {e}")
            raise
    
    def update_target(self, target_id: str, updates: Dict) -> bool:
        """Update an existing profit target"""
        try:
            if target_id not in self.active_targets:
                logger.warning(f"Target {target_id} not found")
                return False
            
            target = self.active_targets[target_id]
            
            # Update allowed fields
            updatable_fields = [
                'name', 'max_drawdown_pct', 'max_loss_limit', 'conservative_threshold',
                'halt_on_achievement', 'trading_start_time', 'trading_end_time',
                'min_trade_size_pct', 'max_trade_size_pct', 'status'
            ]
            
            for field, value in updates.items():
                if field in updatable_fields and hasattr(target, field):
                    setattr(target, field, value)
            
            target.updated_at = datetime.now()
            
            # Save changes
            self._save_configurations()
            
            logger.info(f"Updated profit target: {target_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error updating profit target: {e}")
            return False
    
    def get_active_targets(self) -> List[ProfitTarget]:
        """Get all active profit targets"""
        return [target for target in self.active_targets.values() 
                if target.status == TargetStatus.ACTIVE]
    
    def get_target_by_id(self, target_id: str) -> Optional[ProfitTarget]:
        """Get specific target by ID"""
        return self.active_targets.get(target_id)
    
    def calculate_target_progress(self, target_id: str, 
                                current_account_value: float,
                                session_pnl: float = 0.0) -> Dict:
        """
        Calculate progress toward a specific target
        
        Args:
            target_id: Target identifier
            current_account_value: Current account value
            session_pnl: Current session P&L
            
        Returns:
            Progress information dictionary
        """
        try:
            target = self.active_targets.get(target_id)
            if not target:
                return {'error': 'Target not found'}
            
            # Calculate progress based on target type
            if target.target_type == TargetType.ABSOLUTE_DOLLAR:
                if target.timeframe == TimeFrame.SESSION:
                    progress = session_pnl
                    remaining = target.target_value - progress
                else:
                    # For daily/weekly/monthly, calculate from start date
                    start_value = self._get_account_value_at_date(target.start_date)
                    progress = current_account_value - start_value
                    remaining = target.target_value - progress
                    
            elif target.target_type == TargetType.PERCENTAGE_ACCOUNT:
                start_value = self._get_account_value_at_date(target.start_date)
                target_amount = start_value * (target.target_value / 100)
                progress = current_account_value - start_value
                remaining = target_amount - progress
                
            elif target.target_type == TargetType.PERCENTAGE_DAILY:
                # Daily percentage target
                daily_target = current_account_value * (target.target_value / 100)
                progress = session_pnl
                remaining = daily_target - progress
                
            else:  # RISK_ADJUSTED
                # Risk-adjusted target considers drawdown
                start_value = self._get_account_value_at_date(target.start_date)
                risk_adjusted_target = self._calculate_risk_adjusted_target(
                    target, start_value, current_account_value
                )
                progress = current_account_value - start_value
                remaining = risk_adjusted_target - progress
            
            # Calculate percentage completion
            target_amount = target.target_value
            if target.target_type in [TargetType.PERCENTAGE_ACCOUNT, TargetType.PERCENTAGE_DAILY]:
                if target.target_type == TargetType.PERCENTAGE_ACCOUNT:
                    start_value = self._get_account_value_at_date(target.start_date)
                    target_amount = start_value * (target.target_value / 100)
                else:
                    target_amount = current_account_value * (target.target_value / 100)
            
            completion_pct = (progress / target_amount) * 100 if target_amount > 0 else 0
            completion_pct = max(0, min(100, completion_pct))
            
            # Update target progress
            target.current_progress = progress
            
            # Check if target is achieved
            is_achieved = progress >= target_amount
            
            # Calculate time remaining
            time_remaining = self._calculate_time_remaining(target)
            
            return {
                'target_id': target_id,
                'target_name': target.name,
                'target_amount': target_amount,
                'current_progress': progress,
                'remaining_amount': remaining,
                'completion_percentage': completion_pct,
                'is_achieved': is_achieved,
                'time_remaining': time_remaining,
                'is_conservative_mode': completion_pct >= (target.conservative_threshold * 100),
                'trades_executed': target.trades_executed,
                'achievement_probability': target.achievement_probability
            }
            
        except Exception as e:
            logger.error(f"Error calculating target progress: {e}")
            return {'error': str(e)}
    
    def mark_target_achieved(self, target_id: str, final_value: float,
                           trades_count: int, win_rate: float,
                           max_drawdown: float, ai_score: float) -> bool:
        """Mark a target as achieved and record history"""
        try:
            target = self.active_targets.get(target_id)
            if not target:
                return False
            
            # Update target status
            target.status = TargetStatus.ACHIEVED
            target.updated_at = datetime.now()
            
            # Calculate achievement time
            time_to_achieve = datetime.now() - target.start_date
            
            # Create achievement record
            achievement = TargetAchievementHistory(
                target_id=target_id,
                target_name=target.name,
                target_value=target.target_value,
                achieved_value=final_value,
                achievement_date=datetime.now(),
                time_to_achieve=time_to_achieve,
                trades_count=trades_count,
                win_rate=win_rate,
                max_drawdown_experienced=max_drawdown,
                ai_efficiency_score=ai_score
            )
            
            self.achievement_history.append(achievement)
            
            # Save data
            self._save_configurations()
            self._save_achievement_history()
            
            logger.info(f"Target achieved: {target.name} - "
                       f"${final_value:.2f} in {time_to_achieve}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error marking target achieved: {e}")
            return False
    
    def _validate_target_config(self, config: Dict) -> Dict:
        """Validate target configuration"""
        required_fields = ['name', 'target_type', 'target_value', 'timeframe']
        
        for field in required_fields:
            if field not in config:
                raise ValueError(f"Missing required field: {field}")
        
        # Validate target type
        if config['target_type'] not in [t.value for t in TargetType]:
            raise ValueError(f"Invalid target type: {config['target_type']}")
        
        # Validate timeframe
        if config['timeframe'] not in [t.value for t in TimeFrame]:
            raise ValueError(f"Invalid timeframe: {config['timeframe']}")
        
        # Validate target value
        if config['target_value'] <= 0:
            raise ValueError("Target value must be positive")
        
        # Validate percentage targets
        if config['target_type'] in ['percentage_account', 'percentage_daily']:
            if config['target_value'] > 100:
                logger.warning("Percentage target > 100% - this is very aggressive")
        
        return config
    
    def _get_account_value_at_date(self, date: datetime) -> float:
        """Get account value at specific date (placeholder)"""
        # In production, this would query historical account data
        # For now, return a reasonable default
        return 100000.0  # $100k default
    
    def _calculate_risk_adjusted_target(self, target: ProfitTarget,
                                      start_value: float,
                                      current_value: float) -> float:
        """Calculate risk-adjusted target amount"""
        # Risk-adjusted target considers maximum drawdown tolerance
        base_target = start_value * (target.target_value / 100)
        
        # Adjust based on current drawdown
        current_drawdown = max(0, (start_value - current_value) / start_value)
        drawdown_factor = max(0.5, 1 - (current_drawdown / target.max_drawdown_pct))
        
        return base_target * drawdown_factor
    
    def _calculate_time_remaining(self, target: ProfitTarget) -> Optional[timedelta]:
        """Calculate time remaining for target achievement"""
        if not target.end_date:
            # Calculate based on timeframe
            if target.timeframe == TimeFrame.DAILY:
                end_of_day = datetime.combine(datetime.now().date(), time(16, 0))  # 4 PM
                if datetime.now() > end_of_day:
                    return timedelta(0)
                return end_of_day - datetime.now()
            elif target.timeframe == TimeFrame.WEEKLY:
                # End of week (Friday 4 PM)
                days_until_friday = (4 - datetime.now().weekday()) % 7
                end_of_week = datetime.combine(
                    datetime.now().date() + timedelta(days=days_until_friday),
                    time(16, 0)
                )
                return end_of_week - datetime.now()
            elif target.timeframe == TimeFrame.MONTHLY:
                # End of month
                next_month = datetime.now().replace(day=28) + timedelta(days=4)
                end_of_month = next_month - timedelta(days=next_month.day)
                return end_of_month - datetime.now()
            else:  # SESSION
                if target.trading_end_time:
                    end_time = datetime.combine(datetime.now().date(), target.trading_end_time)
                    if datetime.now() > end_time:
                        return timedelta(0)
                    return end_time - datetime.now()
                return None
        else:
            return target.end_date - datetime.now()
    
    def _save_configurations(self):
        """Save target configurations to file"""
        try:
            targets_data = {}
            for target_id, target in self.active_targets.items():
                target_dict = asdict(target)
                # Convert datetime objects to ISO strings
                target_dict['start_date'] = target.start_date.isoformat()
                target_dict['end_date'] = target.end_date.isoformat() if target.end_date else None
                target_dict['created_at'] = target.created_at.isoformat()
                target_dict['updated_at'] = target.updated_at.isoformat()
                target_dict['target_type'] = target.target_type.value
                target_dict['timeframe'] = target.timeframe.value
                target_dict['status'] = target.status.value
                
                # Convert time objects
                if target.trading_start_time:
                    target_dict['trading_start_time'] = target.trading_start_time.isoformat()
                if target.trading_end_time:
                    target_dict['trading_end_time'] = target.trading_end_time.isoformat()
                
                targets_data[target_id] = target_dict
            
            with open(self.config_file, 'w') as f:
                json.dump(targets_data, f, indent=2)
                
        except Exception as e:
            logger.error(f"Error saving configurations: {e}")
    
    def _save_achievement_history(self):
        """Save achievement history to file"""
        try:
            history_data = []
            for achievement in self.achievement_history:
                achievement_dict = asdict(achievement)
                achievement_dict['achievement_date'] = achievement.achievement_date.isoformat()
                achievement_dict['time_to_achieve'] = str(achievement.time_to_achieve)
                history_data.append(achievement_dict)
            
            with open(self.history_file, 'w') as f:
                json.dump(history_data, f, indent=2)
                
        except Exception as e:
            logger.error(f"Error saving achievement history: {e}")
    
    def _load_configurations(self):
        """Load existing configurations from file"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    targets_data = json.load(f)
                
                for target_id, target_dict in targets_data.items():
                    # Convert strings back to appropriate types
                    target_dict['start_date'] = datetime.fromisoformat(target_dict['start_date'])
                    target_dict['end_date'] = datetime.fromisoformat(target_dict['end_date']) if target_dict['end_date'] else None
                    target_dict['created_at'] = datetime.fromisoformat(target_dict['created_at'])
                    target_dict['updated_at'] = datetime.fromisoformat(target_dict['updated_at'])
                    target_dict['target_type'] = TargetType(target_dict['target_type'])
                    target_dict['timeframe'] = TimeFrame(target_dict['timeframe'])
                    target_dict['status'] = TargetStatus(target_dict['status'])
                    
                    if target_dict.get('trading_start_time'):
                        target_dict['trading_start_time'] = time.fromisoformat(target_dict['trading_start_time'])
                    if target_dict.get('trading_end_time'):
                        target_dict['trading_end_time'] = time.fromisoformat(target_dict['trading_end_time'])
                    
                    target = ProfitTarget(**target_dict)
                    self.active_targets[target_id] = target
            
            # Load achievement history
            if os.path.exists(self.history_file):
                with open(self.history_file, 'r') as f:
                    history_data = json.load(f)
                
                for achievement_dict in history_data:
                    achievement_dict['achievement_date'] = datetime.fromisoformat(achievement_dict['achievement_date'])
                    # Parse timedelta
                    time_parts = achievement_dict['time_to_achieve'].split(':')
                    achievement_dict['time_to_achieve'] = timedelta(
                        hours=int(time_parts[0]),
                        minutes=int(time_parts[1]),
                        seconds=float(time_parts[2])
                    )
                    
                    achievement = TargetAchievementHistory(**achievement_dict)
                    self.achievement_history.append(achievement)
            
            logger.info(f"Loaded {len(self.active_targets)} profit targets")
            
        except Exception as e:
            logger.error(f"Error loading configurations: {e}")
    
    def get_achievement_statistics(self) -> Dict:
        """Get statistics on target achievements"""
        try:
            if not self.achievement_history:
                return {'total_achievements': 0}
            
            total_achievements = len(self.achievement_history)
            avg_time_to_achieve = sum(
                [a.time_to_achieve.total_seconds() for a in self.achievement_history]
            ) / total_achievements / 3600  # Convert to hours
            
            avg_trades_count = sum([a.trades_count for a in self.achievement_history]) / total_achievements
            avg_win_rate = sum([a.win_rate for a in self.achievement_history]) / total_achievements
            avg_ai_score = sum([a.ai_efficiency_score for a in self.achievement_history]) / total_achievements
            
            return {
                'total_achievements': total_achievements,
                'avg_time_to_achieve_hours': avg_time_to_achieve,
                'avg_trades_per_target': avg_trades_count,
                'avg_win_rate': avg_win_rate,
                'avg_ai_efficiency_score': avg_ai_score,
                'recent_achievements': [asdict(a) for a in self.achievement_history[-5:]]
            }
            
        except Exception as e:
            logger.error(f"Error getting achievement statistics: {e}")
            return {'error': str(e)}
    
    def cleanup_expired_targets(self):
        """Clean up expired targets"""
        try:
            current_time = datetime.now()
            expired_targets = []
            
            for target_id, target in self.active_targets.items():
                if target.end_date and current_time > target.end_date:
                    target.status = TargetStatus.EXPIRED
                    expired_targets.append(target_id)
            
            if expired_targets:
                self._save_configurations()
                logger.info(f"Marked {len(expired_targets)} targets as expired")
            
        except Exception as e:
            logger.error(f"Error cleaning up expired targets: {e}")
    
    def get_target_summary(self) -> Dict:
        """Get summary of all targets"""
        active_count = len([t for t in self.active_targets.values() if t.status == TargetStatus.ACTIVE])
        achieved_count = len([t for t in self.active_targets.values() if t.status == TargetStatus.ACHIEVED])
        paused_count = len([t for t in self.active_targets.values() if t.status == TargetStatus.PAUSED])
        
        return {
            'total_targets': len(self.active_targets),
            'active_targets': active_count,
            'achieved_targets': achieved_count,
            'paused_targets': paused_count,
            'achievement_history_count': len(self.achievement_history)
        }
