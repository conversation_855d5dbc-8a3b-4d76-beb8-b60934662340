"""
OpenAI-Powered Market Intelligence for Enhanced Context Analysis
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
import logging
from datetime import datetime, timedelta
import asyncio
import aiohttp
import json
import os
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class NewsAnalysis:
    """News sentiment analysis result"""
    symbol: str
    sentiment_score: float  # -1 to 1
    confidence: float      # 0 to 1
    key_themes: List[str]
    impact_assessment: str  # 'positive', 'negative', 'neutral'
    urgency: str           # 'high', 'medium', 'low'

@dataclass
class EconomicEventImpact:
    """Economic event impact assessment"""
    event_name: str
    event_date: datetime
    impact_level: str      # 'high', 'medium', 'low'
    market_sectors_affected: List[str]
    expected_direction: str  # 'bullish', 'bearish', 'neutral'
    confidence: float

@dataclass
class MarketRegimeAnalysis:
    """AI-powered market regime analysis"""
    regime_type: str       # 'bull_market', 'bear_market', 'sideways', 'transition'
    confidence: float
    key_indicators: List[str]
    duration_estimate: str  # 'short_term', 'medium_term', 'long_term'
    risk_level: str        # 'low', 'medium', 'high', 'extreme'

class OpenAIMarketIntelligence:
    """OpenAI-powered market intelligence and analysis"""
    
    def __init__(self):
        self.api_key = "********************************************************************************************************************************************************************"
        self.base_url = "https://api.openai.com/v1"
        
        # Cache for API responses
        self.news_cache = {}
        self.regime_cache = {}
        self.cache_duration = timedelta(hours=1)
        
        # Rate limiting
        self.last_api_call = None
        self.min_api_interval = timedelta(seconds=1)
        
        logger.info("OpenAI Market Intelligence initialized")
    
    async def analyze_news_sentiment(self, symbol: str, 
                                   news_headlines: List[str]) -> NewsAnalysis:
        """
        Analyze news sentiment for a specific symbol using OpenAI
        
        Args:
            symbol: Stock symbol
            news_headlines: List of recent news headlines
            
        Returns:
            NewsAnalysis with sentiment and impact assessment
        """
        try:
            # Check cache
            cache_key = f"news_{symbol}_{hash(str(news_headlines))}"
            if cache_key in self.news_cache:
                cached_result, timestamp = self.news_cache[cache_key]
                if datetime.now() - timestamp < self.cache_duration:
                    return cached_result
            
            # Prepare prompt for OpenAI
            prompt = self._create_news_analysis_prompt(symbol, news_headlines)
            
            # Call OpenAI API
            response = await self._call_openai_api(prompt, max_tokens=500)
            
            if response:
                # Parse response
                analysis = self._parse_news_analysis_response(symbol, response)
                
                # Cache result
                self.news_cache[cache_key] = (analysis, datetime.now())
                
                return analysis
            else:
                return self._get_default_news_analysis(symbol)
                
        except Exception as e:
            logger.error(f"Error analyzing news sentiment for {symbol}: {e}")
            return self._get_default_news_analysis(symbol)
    
    async def assess_economic_events(self, events: List[Dict]) -> List[EconomicEventImpact]:
        """
        Assess impact of economic events using OpenAI
        
        Args:
            events: List of economic events with details
            
        Returns:
            List of EconomicEventImpact assessments
        """
        try:
            if not events:
                return []
            
            # Prepare prompt
            prompt = self._create_economic_events_prompt(events)
            
            # Call OpenAI API
            response = await self._call_openai_api(prompt, max_tokens=800)
            
            if response:
                return self._parse_economic_events_response(response)
            else:
                return []
                
        except Exception as e:
            logger.error(f"Error assessing economic events: {e}")
            return []
    
    async def analyze_market_regime(self, market_data: Dict) -> MarketRegimeAnalysis:
        """
        Analyze current market regime using OpenAI
        
        Args:
            market_data: Current market indicators and data
            
        Returns:
            MarketRegimeAnalysis with regime classification
        """
        try:
            # Check cache
            cache_key = f"regime_{hash(str(market_data))}"
            if cache_key in self.regime_cache:
                cached_result, timestamp = self.regime_cache[cache_key]
                if datetime.now() - timestamp < self.cache_duration:
                    return cached_result
            
            # Prepare prompt
            prompt = self._create_market_regime_prompt(market_data)
            
            # Call OpenAI API
            response = await self._call_openai_api(prompt, max_tokens=600)
            
            if response:
                analysis = self._parse_market_regime_response(response)
                
                # Cache result
                self.regime_cache[cache_key] = (analysis, datetime.now())
                
                return analysis
            else:
                return self._get_default_regime_analysis()
                
        except Exception as e:
            logger.error(f"Error analyzing market regime: {e}")
            return self._get_default_regime_analysis()
    
    async def _call_openai_api(self, prompt: str, max_tokens: int = 500) -> Optional[str]:
        """Call OpenAI API with rate limiting"""
        try:
            # Rate limiting
            if self.last_api_call:
                time_since_last = datetime.now() - self.last_api_call
                if time_since_last < self.min_api_interval:
                    await asyncio.sleep((self.min_api_interval - time_since_last).total_seconds())
            
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            payload = {
                "model": "gpt-4",
                "messages": [
                    {
                        "role": "system",
                        "content": "You are a professional financial analyst with expertise in market analysis, sentiment analysis, and economic impact assessment. Provide concise, actionable insights."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "max_tokens": max_tokens,
                "temperature": 0.3
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.base_url}/chat/completions",
                    headers=headers,
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    
                    self.last_api_call = datetime.now()
                    
                    if response.status == 200:
                        data = await response.json()
                        return data['choices'][0]['message']['content']
                    else:
                        logger.warning(f"OpenAI API error: {response.status}")
                        return None
                        
        except Exception as e:
            logger.error(f"Error calling OpenAI API: {e}")
            return None
    
    def _create_news_analysis_prompt(self, symbol: str, headlines: List[str]) -> str:
        """Create prompt for news sentiment analysis"""
        headlines_text = "\n".join([f"- {headline}" for headline in headlines[:10]])
        
        return f"""
Analyze the sentiment and potential market impact of these news headlines for {symbol}:

{headlines_text}

Please provide your analysis in the following JSON format:
{{
    "sentiment_score": <float between -1 and 1>,
    "confidence": <float between 0 and 1>,
    "key_themes": [<list of 2-3 key themes>],
    "impact_assessment": "<positive/negative/neutral>",
    "urgency": "<high/medium/low>",
    "reasoning": "<brief explanation>"
}}

Consider:
- Overall sentiment (positive/negative/neutral)
- Potential impact on stock price
- Urgency of the news
- Key themes and topics
"""
    
    def _create_economic_events_prompt(self, events: List[Dict]) -> str:
        """Create prompt for economic events analysis"""
        events_text = "\n".join([
            f"- {event.get('name', 'Unknown')}: {event.get('description', '')}"
            for event in events[:5]
        ])
        
        return f"""
Analyze the potential market impact of these upcoming economic events:

{events_text}

For each event, provide analysis in JSON format:
[
    {{
        "event_name": "<event name>",
        "impact_level": "<high/medium/low>",
        "market_sectors_affected": [<list of affected sectors>],
        "expected_direction": "<bullish/bearish/neutral>",
        "confidence": <float between 0 and 1>,
        "reasoning": "<brief explanation>"
    }}
]

Consider:
- Historical impact of similar events
- Current market conditions
- Sector-specific effects
- Timeline and magnitude of impact
"""
    
    def _create_market_regime_prompt(self, market_data: Dict) -> str:
        """Create prompt for market regime analysis"""
        data_summary = []
        for key, value in market_data.items():
            if isinstance(value, (int, float)):
                data_summary.append(f"- {key}: {value}")
        
        data_text = "\n".join(data_summary[:15])
        
        return f"""
Analyze the current market regime based on these indicators:

{data_text}

Provide your analysis in JSON format:
{{
    "regime_type": "<bull_market/bear_market/sideways/transition>",
    "confidence": <float between 0 and 1>,
    "key_indicators": [<list of 3-5 key supporting indicators>],
    "duration_estimate": "<short_term/medium_term/long_term>",
    "risk_level": "<low/medium/high/extreme>",
    "reasoning": "<detailed explanation>",
    "trading_implications": "<brief trading guidance>"
}}

Consider:
- Trend analysis and momentum
- Volatility patterns
- Market breadth and participation
- Economic backdrop
- Historical context
"""
    
    def _parse_news_analysis_response(self, symbol: str, response: str) -> NewsAnalysis:
        """Parse OpenAI response for news analysis"""
        try:
            # Try to extract JSON from response
            start_idx = response.find('{')
            end_idx = response.rfind('}') + 1
            
            if start_idx >= 0 and end_idx > start_idx:
                json_str = response[start_idx:end_idx]
                data = json.loads(json_str)
                
                return NewsAnalysis(
                    symbol=symbol,
                    sentiment_score=float(data.get('sentiment_score', 0.0)),
                    confidence=float(data.get('confidence', 0.5)),
                    key_themes=data.get('key_themes', []),
                    impact_assessment=data.get('impact_assessment', 'neutral'),
                    urgency=data.get('urgency', 'medium')
                )
            else:
                # Fallback parsing
                return self._fallback_parse_news(symbol, response)
                
        except Exception as e:
            logger.warning(f"Error parsing news analysis response: {e}")
            return self._get_default_news_analysis(symbol)
    
    def _parse_economic_events_response(self, response: str) -> List[EconomicEventImpact]:
        """Parse OpenAI response for economic events"""
        try:
            # Try to extract JSON array from response
            start_idx = response.find('[')
            end_idx = response.rfind(']') + 1
            
            if start_idx >= 0 and end_idx > start_idx:
                json_str = response[start_idx:end_idx]
                data = json.loads(json_str)
                
                events = []
                for event_data in data:
                    events.append(EconomicEventImpact(
                        event_name=event_data.get('event_name', 'Unknown'),
                        event_date=datetime.now(),  # Would be parsed from actual data
                        impact_level=event_data.get('impact_level', 'medium'),
                        market_sectors_affected=event_data.get('market_sectors_affected', []),
                        expected_direction=event_data.get('expected_direction', 'neutral'),
                        confidence=float(event_data.get('confidence', 0.5))
                    ))
                
                return events
            else:
                return []
                
        except Exception as e:
            logger.warning(f"Error parsing economic events response: {e}")
            return []
    
    def _parse_market_regime_response(self, response: str) -> MarketRegimeAnalysis:
        """Parse OpenAI response for market regime"""
        try:
            # Try to extract JSON from response
            start_idx = response.find('{')
            end_idx = response.rfind('}') + 1
            
            if start_idx >= 0 and end_idx > start_idx:
                json_str = response[start_idx:end_idx]
                data = json.loads(json_str)
                
                return MarketRegimeAnalysis(
                    regime_type=data.get('regime_type', 'sideways'),
                    confidence=float(data.get('confidence', 0.5)),
                    key_indicators=data.get('key_indicators', []),
                    duration_estimate=data.get('duration_estimate', 'medium_term'),
                    risk_level=data.get('risk_level', 'medium')
                )
            else:
                return self._get_default_regime_analysis()
                
        except Exception as e:
            logger.warning(f"Error parsing market regime response: {e}")
            return self._get_default_regime_analysis()
    
    def _fallback_parse_news(self, symbol: str, response: str) -> NewsAnalysis:
        """Fallback parsing for news analysis"""
        # Simple keyword-based sentiment analysis
        positive_words = ['bullish', 'positive', 'growth', 'strong', 'beat', 'exceed']
        negative_words = ['bearish', 'negative', 'decline', 'weak', 'miss', 'concern']
        
        response_lower = response.lower()
        positive_count = sum(1 for word in positive_words if word in response_lower)
        negative_count = sum(1 for word in negative_words if word in response_lower)
        
        if positive_count > negative_count:
            sentiment = 0.3
            impact = 'positive'
        elif negative_count > positive_count:
            sentiment = -0.3
            impact = 'negative'
        else:
            sentiment = 0.0
            impact = 'neutral'
        
        return NewsAnalysis(
            symbol=symbol,
            sentiment_score=sentiment,
            confidence=0.4,
            key_themes=['general_sentiment'],
            impact_assessment=impact,
            urgency='medium'
        )
    
    def _get_default_news_analysis(self, symbol: str) -> NewsAnalysis:
        """Get default news analysis"""
        return NewsAnalysis(
            symbol=symbol,
            sentiment_score=0.0,
            confidence=0.3,
            key_themes=[],
            impact_assessment='neutral',
            urgency='low'
        )
    
    def _get_default_regime_analysis(self) -> MarketRegimeAnalysis:
        """Get default market regime analysis"""
        return MarketRegimeAnalysis(
            regime_type='sideways',
            confidence=0.3,
            key_indicators=[],
            duration_estimate='medium_term',
            risk_level='medium'
        )
    
    async def get_comprehensive_market_intelligence(self, symbol: str,
                                                  market_data: Dict,
                                                  news_headlines: List[str] = None,
                                                  economic_events: List[Dict] = None) -> Dict:
        """
        Get comprehensive market intelligence combining all AI analysis
        
        Args:
            symbol: Stock symbol
            market_data: Current market data
            news_headlines: Recent news headlines
            economic_events: Upcoming economic events
            
        Returns:
            Comprehensive intelligence report
        """
        try:
            # Run all analyses concurrently
            tasks = []
            
            # News sentiment analysis
            if news_headlines:
                tasks.append(self.analyze_news_sentiment(symbol, news_headlines))
            else:
                tasks.append(asyncio.create_task(asyncio.coroutine(lambda: self._get_default_news_analysis(symbol))()))
            
            # Economic events analysis
            if economic_events:
                tasks.append(self.assess_economic_events(economic_events))
            else:
                tasks.append(asyncio.create_task(asyncio.coroutine(lambda: [])()))
            
            # Market regime analysis
            tasks.append(self.analyze_market_regime(market_data))
            
            # Wait for all analyses to complete
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            news_analysis = results[0] if not isinstance(results[0], Exception) else self._get_default_news_analysis(symbol)
            economic_analysis = results[1] if not isinstance(results[1], Exception) else []
            regime_analysis = results[2] if not isinstance(results[2], Exception) else self._get_default_regime_analysis()
            
            # Combine into comprehensive report
            return {
                'symbol': symbol,
                'timestamp': datetime.now().isoformat(),
                'news_sentiment': {
                    'score': news_analysis.sentiment_score,
                    'confidence': news_analysis.confidence,
                    'impact': news_analysis.impact_assessment,
                    'urgency': news_analysis.urgency,
                    'themes': news_analysis.key_themes
                },
                'economic_events': [
                    {
                        'name': event.event_name,
                        'impact_level': event.impact_level,
                        'direction': event.expected_direction,
                        'confidence': event.confidence,
                        'sectors_affected': event.market_sectors_affected
                    }
                    for event in economic_analysis
                ],
                'market_regime': {
                    'type': regime_analysis.regime_type,
                    'confidence': regime_analysis.confidence,
                    'risk_level': regime_analysis.risk_level,
                    'duration': regime_analysis.duration_estimate,
                    'key_indicators': regime_analysis.key_indicators
                },
                'overall_assessment': self._calculate_overall_assessment(
                    news_analysis, economic_analysis, regime_analysis
                )
            }
            
        except Exception as e:
            logger.error(f"Error getting comprehensive market intelligence: {e}")
            return {
                'symbol': symbol,
                'timestamp': datetime.now().isoformat(),
                'error': str(e),
                'overall_assessment': 'neutral'
            }
    
    def _calculate_overall_assessment(self, news: NewsAnalysis,
                                    events: List[EconomicEventImpact],
                                    regime: MarketRegimeAnalysis) -> str:
        """Calculate overall market assessment"""
        try:
            # Weight different factors
            news_weight = 0.3
            events_weight = 0.3
            regime_weight = 0.4
            
            # News contribution
            news_score = news.sentiment_score * news.confidence
            
            # Events contribution
            events_score = 0.0
            if events:
                for event in events:
                    event_score = {'bullish': 1, 'bearish': -1, 'neutral': 0}.get(event.expected_direction, 0)
                    impact_multiplier = {'high': 1.0, 'medium': 0.6, 'low': 0.3}.get(event.impact_level, 0.5)
                    events_score += event_score * impact_multiplier * event.confidence
                events_score /= len(events)
            
            # Regime contribution
            regime_score = {
                'bull_market': 0.8,
                'bear_market': -0.8,
                'sideways': 0.0,
                'transition': -0.2
            }.get(regime.regime_type, 0.0) * regime.confidence
            
            # Calculate weighted overall score
            overall_score = (
                news_score * news_weight +
                events_score * events_weight +
                regime_score * regime_weight
            )
            
            # Convert to assessment
            if overall_score > 0.3:
                return 'bullish'
            elif overall_score < -0.3:
                return 'bearish'
            else:
                return 'neutral'
                
        except Exception:
            return 'neutral'
    
    def clear_cache(self):
        """Clear all cached responses"""
        self.news_cache.clear()
        self.regime_cache.clear()
        logger.info("OpenAI cache cleared")
    
    def get_cache_stats(self) -> Dict:
        """Get cache statistics"""
        return {
            'news_cache_size': len(self.news_cache),
            'regime_cache_size': len(self.regime_cache),
            'cache_duration_hours': self.cache_duration.total_seconds() / 3600,
            'last_api_call': self.last_api_call.isoformat() if self.last_api_call else None
        }
