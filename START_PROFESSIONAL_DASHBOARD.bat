@echo off
title TTM Squeeze Professional Dashboard Launcher
color 0A

echo.
echo ========================================================
echo          TTM SQUEEZE PROFESSIONAL DASHBOARD
echo ========================================================
echo.
echo 🚀 Launching Professional Trading Dashboard...
echo.

REM Change to the correct directory
cd /d "C:\Users\<USER>\Desktop\ttm_squeeze_trading_system"

echo 📂 Current directory: %CD%
echo.

REM Check if HTML file exists
if exist "professional_dashboard_demo.html" (
    echo ✅ Professional dashboard file found!
    echo.
    echo 🌐 Opening Professional Dashboard in your browser...
    echo.
    
    REM Open the HTML file in default browser
    start "" "professional_dashboard_demo.html"
    
    echo ========================================================
    echo    PROFESSIONAL DASHBOARD SUCCESSFULLY LAUNCHED!
    echo ========================================================
    echo.
    echo 🎯 Features Available:
    echo    • Bloomberg Terminal-style interface
    echo    • Real-time market data display  
    echo    • TTM Squeeze alerts with AI confidence
    echo    • Market analytics and insights
    echo    • Professional order entry panel
    echo    • Live CST clock with market status
    echo.
    echo 📊 Dashboard Components:
    echo    • Market Overview (SPY, VIX, P&L, Alerts, AI)
    echo    • TTM Squeeze Alerts (AAPL, TSLA signals)
    echo    • Market Analytics (Sentiment, Risk, Performance)
    echo    • Order Entry (Buy/Sell with professional styling)
    echo.
    echo 🎨 Professional Features:
    echo    • Dark theme with professional colors
    echo    • Animated real-time updates
    echo    • AI confidence gauges
    echo    • Interactive trading buttons
    echo    • Responsive design
    echo.
    echo ⌨️  Try These Actions:
    echo    • Click BUY/SELL buttons on alerts
    echo    • Enter symbols in order panel
    echo    • Watch live clock updates
    echo    • Observe animated metrics
    echo.
    echo 🎯 The dashboard is now LIVE and ready for trading!
    echo.
    
) else (
    echo ❌ Dashboard file not found!
    echo.
    echo 🔍 Looking for: professional_dashboard_demo.html
    echo 📂 In directory: %CD%
    echo.
    echo 🛠️  Please ensure the file exists in this directory.
    echo.
)

echo ========================================================
echo Press any key to exit...
echo ========================================================
pause >nul
