"""
Reinforcement Learning Optimizer for Target Achievement
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
import logging
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
import json
import pickle
import os
from collections import deque
import random

logger = logging.getLogger(__name__)

@dataclass
class TradingState:
    """Current trading state for RL agent"""
    target_progress: float          # 0-1, progress toward target
    time_remaining_ratio: float     # 0-1, time remaining
    market_regime: str             # 'trending', 'ranging', 'volatile', 'calm'
    volatility_percentile: float   # 0-100
    signal_strength: float         # 0-100
    recent_win_rate: float         # 0-1, recent win rate
    consecutive_losses: int        # Number of consecutive losses
    risk_budget_used: float        # 0-1, risk budget utilization
    portfolio_correlation: float   # 0-1, portfolio correlation
    market_breadth: float          # 0-100, market breadth

@dataclass
class TradingAction:
    """Trading action for RL agent"""
    position_size_multiplier: float  # 0.5-2.0, multiply base position size
    signal_threshold: float          # 70-95, minimum signal score to trade
    risk_tolerance: float            # 0.5-2.0, risk tolerance multiplier
    time_urgency: float             # 0.5-2.0, urgency multiplier

@dataclass
class Experience:
    """Experience tuple for RL learning"""
    state: TradingState
    action: TradingAction
    reward: float
    next_state: TradingState
    done: bool
    timestamp: datetime

class QLearningAgent:
    """Q-Learning agent for target achievement optimization"""

    def __init__(self, learning_rate: float = 0.1, discount_factor: float = 0.95,
                 epsilon: float = 0.1, epsilon_decay: float = 0.995):

        # Q-Learning parameters
        self.learning_rate = learning_rate
        self.discount_factor = discount_factor
        self.epsilon = epsilon  # Exploration rate
        self.epsilon_decay = epsilon_decay
        self.min_epsilon = 0.01

        # Q-table (state -> action -> value)
        self.q_table = {}

        # Experience replay
        self.experience_buffer = deque(maxlen=10000)
        self.batch_size = 32

        # Action space discretization
        self.action_space = self._create_action_space()

        # Performance tracking
        self.training_history = []
        self.episode_rewards = []

        logger.info("Q-Learning Agent initialized")

    def _create_action_space(self) -> List[TradingAction]:
        """Create discrete action space"""
        actions = []

        # Discretize action parameters
        position_multipliers = [0.5, 0.75, 1.0, 1.25, 1.5, 2.0]
        signal_thresholds = [70, 75, 80, 85, 90, 95]
        risk_tolerances = [0.5, 0.75, 1.0, 1.25, 1.5, 2.0]
        time_urgencies = [0.5, 0.75, 1.0, 1.25, 1.5, 2.0]

        # Create all combinations (reduced for computational efficiency)
        for pos_mult in position_multipliers:
            for sig_thresh in signal_thresholds:
                for risk_tol in risk_tolerances:
                    for time_urg in time_urgencies:
                        action = TradingAction(
                            position_size_multiplier=pos_mult,
                            signal_threshold=sig_thresh,
                            risk_tolerance=risk_tol,
                            time_urgency=time_urg
                        )
                        actions.append(action)

        logger.info(f"Created action space with {len(actions)} actions")
        return actions

    def _state_to_key(self, state: TradingState) -> str:
        """Convert state to string key for Q-table"""
        # Discretize continuous values
        progress_bin = int(state.target_progress * 10)  # 0-10
        time_bin = int(state.time_remaining_ratio * 10)  # 0-10
        vol_bin = int(state.volatility_percentile / 20)  # 0-5
        signal_bin = int(state.signal_strength / 20)  # 0-5
        win_rate_bin = int(state.recent_win_rate * 10)  # 0-10

        return f"{progress_bin}_{time_bin}_{state.market_regime}_{vol_bin}_{signal_bin}_{win_rate_bin}_{state.consecutive_losses}"

    def get_action(self, state: TradingState, training: bool = True) -> TradingAction:
        """Get action using epsilon-greedy policy"""
        state_key = self._state_to_key(state)

        # Exploration vs exploitation
        if training and random.random() < self.epsilon:
            # Explore: random action
            action_idx = random.randint(0, len(self.action_space) - 1)
        else:
            # Exploit: best known action
            if state_key not in self.q_table:
                self.q_table[state_key] = [0.0] * len(self.action_space)

            action_idx = np.argmax(self.q_table[state_key])

        return self.action_space[action_idx], action_idx

    def update_q_value(self, state: TradingState, action_idx: int,
                      reward: float, next_state: TradingState, done: bool):
        """Update Q-value using Q-learning update rule"""
        state_key = self._state_to_key(state)
        next_state_key = self._state_to_key(next_state)

        # Initialize Q-values if not exists
        if state_key not in self.q_table:
            self.q_table[state_key] = [0.0] * len(self.action_space)
        if next_state_key not in self.q_table:
            self.q_table[next_state_key] = [0.0] * len(self.action_space)

        # Q-learning update
        current_q = self.q_table[state_key][action_idx]

        if done:
            target_q = reward
        else:
            max_next_q = max(self.q_table[next_state_key])
            target_q = reward + self.discount_factor * max_next_q

        # Update Q-value
        self.q_table[state_key][action_idx] += self.learning_rate * (target_q - current_q)

        # Decay epsilon
        if self.epsilon > self.min_epsilon:
            self.epsilon *= self.epsilon_decay

    def add_experience(self, experience: Experience):
        """Add experience to replay buffer"""
        self.experience_buffer.append(experience)

    def replay_training(self):
        """Perform experience replay training"""
        if len(self.experience_buffer) < self.batch_size:
            return

        # Sample random batch
        batch = random.sample(self.experience_buffer, self.batch_size)

        for exp in batch:
            # Find action index
            action_idx = None
            for i, action in enumerate(self.action_space):
                if (action.position_size_multiplier == exp.action.position_size_multiplier and
                    action.signal_threshold == exp.action.signal_threshold and
                    action.risk_tolerance == exp.action.risk_tolerance and
                    action.time_urgency == exp.action.time_urgency):
                    action_idx = i
                    break

            if action_idx is not None:
                self.update_q_value(exp.state, action_idx, exp.reward, exp.next_state, exp.done)

    def calculate_reward(self, target_progress_before: float, target_progress_after: float,
                        trade_result: Dict, target_achieved: bool, time_penalty: float = 0.0) -> float:
        """Calculate reward for RL agent"""
        try:
            reward = 0.0

            # Progress reward (main component)
            progress_improvement = target_progress_after - target_progress_before
            reward += progress_improvement * 100  # Scale to reasonable range

            # Target achievement bonus
            if target_achieved:
                reward += 50.0

            # Trade outcome reward
            if trade_result.get('success', False):
                pnl = trade_result.get('pnl', 0)
                if pnl > 0:
                    reward += min(10.0, pnl / 100)  # Cap at 10 points
                else:
                    reward += max(-10.0, pnl / 100)  # Cap at -10 points
            else:
                reward -= 5.0  # Penalty for failed execution

            # Time penalty (encourage efficiency)
            reward -= time_penalty

            # Risk penalty
            risk_taken = trade_result.get('risk_taken', 0)
            if risk_taken > 0.05:  # Penalty for excessive risk
                reward -= (risk_taken - 0.05) * 20

            return reward

        except Exception as e:
            logger.error(f"Error calculating reward: {e}")
            return 0.0

    def save_model(self, filepath: str):
        """Save Q-table and parameters"""
        try:
            model_data = {
                'q_table': self.q_table,
                'epsilon': self.epsilon,
                'training_history': self.training_history,
                'episode_rewards': self.episode_rewards,
                'action_space': [asdict(action) for action in self.action_space]
            }

            with open(filepath, 'wb') as f:
                pickle.dump(model_data, f)

            logger.info(f"RL model saved to {filepath}")

        except Exception as e:
            logger.error(f"Error saving RL model: {e}")

    def load_model(self, filepath: str):
        """Load Q-table and parameters"""
        try:
            with open(filepath, 'rb') as f:
                model_data = pickle.load(f)

            self.q_table = model_data.get('q_table', {})
            self.epsilon = model_data.get('epsilon', self.epsilon)
            self.training_history = model_data.get('training_history', [])
            self.episode_rewards = model_data.get('episode_rewards', [])

            # Reconstruct action space
            action_dicts = model_data.get('action_space', [])
            self.action_space = [TradingAction(**action_dict) for action_dict in action_dicts]

            logger.info(f"RL model loaded from {filepath}")

        except Exception as e:
            logger.error(f"Error loading RL model: {e}")

    def get_training_stats(self) -> Dict:
        """Get training statistics"""
        return {
            'q_table_size': len(self.q_table),
            'experience_buffer_size': len(self.experience_buffer),
            'epsilon': self.epsilon,
            'total_episodes': len(self.episode_rewards),
            'avg_reward_last_100': np.mean(self.episode_rewards[-100:]) if self.episode_rewards else 0,
            'action_space_size': len(self.action_space)
        }

class ReinforcementLearningOptimizer:
    """Main RL optimizer for target achievement"""

    def __init__(self):
        self.agent = QLearningAgent()
        self.current_episode_data = {}
        self.training_enabled = True

        # Episode tracking
        self.episode_count = 0
        self.current_episode_reward = 0.0

        # Model persistence
        self.model_file = "models/rl_target_optimizer.pkl"

        # Load existing model if available
        self._load_model()

        logger.info("Reinforcement Learning Optimizer initialized")

    def optimize_trading_decision(self, current_state: Dict,
                                target_info: Dict) -> Dict:
        """
        Optimize trading decision using RL agent

        Args:
            current_state: Current market and portfolio state
            target_info: Target progress and configuration

        Returns:
            Optimized trading parameters
        """
        try:
            # Convert to RL state
            rl_state = self._convert_to_rl_state(current_state, target_info)

            # Get action from agent
            action, action_idx = self.agent.get_action(rl_state, training=self.training_enabled)

            # Store for potential learning
            if self.training_enabled:
                self.current_episode_data = {
                    'state': rl_state,
                    'action': action,
                    'action_idx': action_idx,
                    'timestamp': datetime.now()
                }

            # Convert action to trading parameters
            trading_params = {
                'position_size_multiplier': action.position_size_multiplier,
                'signal_threshold': action.signal_threshold,
                'risk_tolerance': action.risk_tolerance,
                'time_urgency': action.time_urgency,
                'rl_confidence': self._calculate_action_confidence(rl_state, action_idx)
            }

            return trading_params

        except Exception as e:
            logger.error(f"Error optimizing trading decision: {e}")
            return self._get_default_params()

    def update_from_trade_result(self, trade_result: Dict,
                               new_state: Dict, target_info: Dict):
        """Update RL agent based on trade result"""
        try:
            if not self.training_enabled or not self.current_episode_data:
                return

            # Convert new state
            new_rl_state = self._convert_to_rl_state(new_state, target_info)

            # Calculate reward
            old_progress = self.current_episode_data['state'].target_progress
            new_progress = new_rl_state.target_progress
            target_achieved = target_info.get('is_achieved', False)

            reward = self.agent.calculate_reward(
                old_progress, new_progress, trade_result, target_achieved
            )

            # Create experience
            experience = Experience(
                state=self.current_episode_data['state'],
                action=self.current_episode_data['action'],
                reward=reward,
                next_state=new_rl_state,
                done=target_achieved,
                timestamp=datetime.now()
            )

            # Add to experience buffer
            self.agent.add_experience(experience)

            # Update Q-value
            self.agent.update_q_value(
                experience.state,
                self.current_episode_data['action_idx'],
                reward,
                new_rl_state,
                target_achieved
            )

            # Track episode reward
            self.current_episode_reward += reward

            # End episode if target achieved
            if target_achieved:
                self._end_episode()

            # Periodic replay training
            if len(self.agent.experience_buffer) >= self.agent.batch_size:
                self.agent.replay_training()

            logger.debug(f"RL update: reward={reward:.2f}, progress={new_progress:.3f}")

        except Exception as e:
            logger.error(f"Error updating from trade result: {e}")

    def _convert_to_rl_state(self, current_state: Dict, target_info: Dict) -> TradingState:
        """Convert current state to RL state representation"""
        try:
            return TradingState(
                target_progress=target_info.get('completion_percentage', 0) / 100,
                time_remaining_ratio=self._calculate_time_remaining_ratio(target_info),
                market_regime=current_state.get('regime', 'unknown'),
                volatility_percentile=current_state.get('volatility_percentile', 50),
                signal_strength=current_state.get('signal_strength', 50),
                recent_win_rate=current_state.get('recent_win_rate', 0.5),
                consecutive_losses=current_state.get('consecutive_losses', 0),
                risk_budget_used=current_state.get('risk_budget_used', 0),
                portfolio_correlation=current_state.get('portfolio_correlation', 0),
                market_breadth=current_state.get('market_breadth', 50)
            )

        except Exception as e:
            logger.error(f"Error converting to RL state: {e}")
            return TradingState(
                target_progress=0.0, time_remaining_ratio=1.0, market_regime='unknown',
                volatility_percentile=50, signal_strength=50, recent_win_rate=0.5,
                consecutive_losses=0, risk_budget_used=0, portfolio_correlation=0,
                market_breadth=50
            )

    def _calculate_time_remaining_ratio(self, target_info: Dict) -> float:
        """Calculate time remaining ratio (0-1)"""
        try:
            time_remaining = target_info.get('time_remaining')
            if not time_remaining or not isinstance(time_remaining, timedelta):
                return 1.0

            # Assume daily target for simplicity
            total_time = timedelta(hours=6.5)  # Trading day
            remaining_ratio = time_remaining.total_seconds() / total_time.total_seconds()

            return max(0.0, min(1.0, remaining_ratio))

        except Exception:
            return 1.0

    def _calculate_action_confidence(self, state: TradingState, action_idx: int) -> float:
        """Calculate confidence in the selected action"""
        try:
            state_key = self.agent._state_to_key(state)

            if state_key not in self.agent.q_table:
                return 0.5  # Neutral confidence for unseen states

            q_values = self.agent.q_table[state_key]
            max_q = max(q_values)
            min_q = min(q_values)

            if max_q == min_q:
                return 0.5  # All actions equally valued

            # Normalize confidence based on Q-value spread
            action_q = q_values[action_idx]
            confidence = (action_q - min_q) / (max_q - min_q)

            return max(0.0, min(1.0, confidence))

        except Exception:
            return 0.5

    def _end_episode(self):
        """End current episode and update statistics"""
        try:
            self.agent.episode_rewards.append(self.current_episode_reward)
            self.episode_count += 1

            # Log episode completion
            logger.info(f"Episode {self.episode_count} completed. "
                       f"Reward: {self.current_episode_reward:.2f}")

            # Reset episode data
            self.current_episode_reward = 0.0
            self.current_episode_data = {}

            # Periodic model saving
            if self.episode_count % 10 == 0:
                self._save_model()

        except Exception as e:
            logger.error(f"Error ending episode: {e}")

    def _get_default_params(self) -> Dict:
        """Get default trading parameters"""
        return {
            'position_size_multiplier': 1.0,
            'signal_threshold': 80.0,
            'risk_tolerance': 1.0,
            'time_urgency': 1.0,
            'rl_confidence': 0.5
        }

    def _save_model(self):
        """Save RL model"""
        try:
            import os
            os.makedirs(os.path.dirname(self.model_file), exist_ok=True)
            self.agent.save_model(self.model_file)
        except Exception as e:
            logger.error(f"Error saving RL model: {e}")

    def _load_model(self):
        """Load existing RL model"""
        try:
            import os
            if os.path.exists(self.model_file):
                self.agent.load_model(self.model_file)
                logger.info("Existing RL model loaded")
        except Exception as e:
            logger.warning(f"Could not load existing RL model: {e}")

    def set_training_mode(self, enabled: bool):
        """Enable or disable training mode"""
        self.training_enabled = enabled
        logger.info(f"RL training mode: {'enabled' if enabled else 'disabled'}")

    def get_performance_metrics(self) -> Dict:
        """Get RL performance metrics"""
        try:
            stats = self.agent.get_training_stats()

            # Add optimizer-specific metrics
            stats.update({
                'episode_count': self.episode_count,
                'current_episode_reward': self.current_episode_reward,
                'training_enabled': self.training_enabled,
                'model_file_exists': os.path.exists(self.model_file) if hasattr(os, 'path') else False
            })

            return stats

        except Exception as e:
            logger.error(f"Error getting performance metrics: {e}")
            return {}

    def reset_learning(self):
        """Reset learning progress"""
        try:
            self.agent = QLearningAgent()
            self.episode_count = 0
            self.current_episode_reward = 0.0
            self.current_episode_data = {}

            logger.info("RL learning progress reset")

        except Exception as e:
            logger.error(f"Error resetting learning: {e}")