"""
Multi-Factor Signal Confirmation - AI analysis of additional technical indicators
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
import logging
from datetime import datetime, timedelta
from dataclasses import dataclass

from ..indicators.multi_timeframe import MultiTimeframeSignal

logger = logging.getLogger(__name__)

@dataclass
class TechnicalIndicators:
    """Additional technical indicators for confirmation"""
    rsi: float
    macd_signal: str  # 'bullish', 'bearish', 'neutral'
    stochastic: float
    williams_r: float
    cci: float
    adx: float  # Trend strength
    volume_sma_ratio: float
    price_vs_vwap: float

@dataclass
class VolumeProfile:
    """Volume profile analysis"""
    poc: float  # Point of Control (highest volume price)
    value_area_high: float
    value_area_low: float
    volume_at_price: Dict[float, float]
    volume_trend: str  # 'increasing', 'decreasing', 'stable'
    unusual_volume: bool

@dataclass
class ConfirmationScore:
    """Multi-factor confirmation scoring"""
    technical_score: float  # 0-100
    volume_score: float     # 0-100
    momentum_score: float   # 0-100
    trend_score: float      # 0-100
    overall_score: float    # 0-100
    confidence: float       # 0-100
    factors_aligned: int    # Number of confirming factors

class MultiFactorConfirmation:
    """AI-powered multi-factor signal confirmation system"""
    
    def __init__(self, data_manager):
        self.data_manager = data_manager
        self.indicator_cache = {}
        self.cache_duration = timedelta(minutes=30)
        
        # Confirmation weights
        self.factor_weights = {
            'technical': 0.25,
            'volume': 0.25,
            'momentum': 0.25,
            'trend': 0.25
        }
        
        # Technical indicator thresholds
        self.thresholds = {
            'rsi_oversold': 30,
            'rsi_overbought': 70,
            'stoch_oversold': 20,
            'stoch_overbought': 80,
            'williams_oversold': -80,
            'williams_overbought': -20,
            'cci_oversold': -100,
            'cci_overbought': 100,
            'adx_trending': 25,
            'volume_unusual': 1.5  # 1.5x average volume
        }
        
        logger.info("Multi-Factor Confirmation system initialized")
    
    def analyze_confirmation(self, signal: MultiTimeframeSignal,
                           market_data: Dict) -> ConfirmationScore:
        """
        Analyze multi-factor confirmation for a TTM Squeeze signal
        
        Args:
            signal: TTM Squeeze signal to confirm
            market_data: Current market data
            
        Returns:
            ConfirmationScore with detailed analysis
        """
        try:
            # Get additional technical indicators
            technical_indicators = self._calculate_technical_indicators(
                signal.symbol, signal.primary_timeframe
            )
            
            # Analyze volume profile
            volume_profile = self._analyze_volume_profile(
                signal.symbol, signal.primary_timeframe
            )
            
            # Calculate individual factor scores
            technical_score = self._score_technical_factors(
                technical_indicators, signal
            )
            
            volume_score = self._score_volume_factors(
                volume_profile, technical_indicators, signal
            )
            
            momentum_score = self._score_momentum_factors(
                technical_indicators, signal
            )
            
            trend_score = self._score_trend_factors(
                technical_indicators, signal
            )
            
            # Calculate overall confirmation score
            overall_score = (
                technical_score * self.factor_weights['technical'] +
                volume_score * self.factor_weights['volume'] +
                momentum_score * self.factor_weights['momentum'] +
                trend_score * self.factor_weights['trend']
            )
            
            # Count aligned factors
            factors_aligned = self._count_aligned_factors(
                technical_score, volume_score, momentum_score, trend_score
            )
            
            # Calculate confidence
            confidence = self._calculate_confirmation_confidence(
                technical_indicators, volume_profile, factors_aligned
            )
            
            return ConfirmationScore(
                technical_score=technical_score,
                volume_score=volume_score,
                momentum_score=momentum_score,
                trend_score=trend_score,
                overall_score=overall_score,
                confidence=confidence,
                factors_aligned=factors_aligned
            )
            
        except Exception as e:
            logger.error(f"Error analyzing confirmation for {signal.symbol}: {e}")
            return self._get_default_confirmation()
    
    def _calculate_technical_indicators(self, symbol: str, 
                                      timeframe: str) -> TechnicalIndicators:
        """Calculate additional technical indicators"""
        try:
            # Check cache
            cache_key = f"{symbol}_{timeframe}"
            if cache_key in self.indicator_cache:
                cached_data, timestamp = self.indicator_cache[cache_key]
                if datetime.now() - timestamp < self.cache_duration:
                    return cached_data
            
            # Get historical data
            data = self.data_manager.get_historical_data(symbol, timeframe, periods=100)
            
            if data is None or len(data) < 50:
                return self._get_default_indicators()
            
            # Calculate indicators
            indicators = TechnicalIndicators(
                rsi=self._calculate_rsi(data),
                macd_signal=self._calculate_macd_signal(data),
                stochastic=self._calculate_stochastic(data),
                williams_r=self._calculate_williams_r(data),
                cci=self._calculate_cci(data),
                adx=self._calculate_adx(data),
                volume_sma_ratio=self._calculate_volume_ratio(data),
                price_vs_vwap=self._calculate_vwap_position(data)
            )
            
            # Cache the result
            self.indicator_cache[cache_key] = (indicators, datetime.now())
            
            return indicators
            
        except Exception as e:
            logger.warning(f"Error calculating technical indicators: {e}")
            return self._get_default_indicators()
    
    def _calculate_rsi(self, data: pd.DataFrame, period: int = 14) -> float:
        """Calculate RSI"""
        try:
            delta = data['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            return rsi.iloc[-1] if not rsi.empty else 50.0
        except:
            return 50.0
    
    def _calculate_macd_signal(self, data: pd.DataFrame) -> str:
        """Calculate MACD signal"""
        try:
            exp1 = data['close'].ewm(span=12).mean()
            exp2 = data['close'].ewm(span=26).mean()
            macd = exp1 - exp2
            signal = macd.ewm(span=9).mean()
            
            current_macd = macd.iloc[-1]
            current_signal = signal.iloc[-1]
            prev_macd = macd.iloc[-2]
            prev_signal = signal.iloc[-2]
            
            # Check for bullish/bearish crossover
            if current_macd > current_signal and prev_macd <= prev_signal:
                return 'bullish'
            elif current_macd < current_signal and prev_macd >= prev_signal:
                return 'bearish'
            else:
                return 'neutral'
        except:
            return 'neutral'
    
    def _calculate_stochastic(self, data: pd.DataFrame, k_period: int = 14) -> float:
        """Calculate Stochastic %K"""
        try:
            low_min = data['low'].rolling(window=k_period).min()
            high_max = data['high'].rolling(window=k_period).max()
            k_percent = 100 * ((data['close'] - low_min) / (high_max - low_min))
            return k_percent.iloc[-1] if not k_percent.empty else 50.0
        except:
            return 50.0
    
    def _calculate_williams_r(self, data: pd.DataFrame, period: int = 14) -> float:
        """Calculate Williams %R"""
        try:
            high_max = data['high'].rolling(window=period).max()
            low_min = data['low'].rolling(window=period).min()
            williams_r = -100 * ((high_max - data['close']) / (high_max - low_min))
            return williams_r.iloc[-1] if not williams_r.empty else -50.0
        except:
            return -50.0
    
    def _calculate_cci(self, data: pd.DataFrame, period: int = 20) -> float:
        """Calculate Commodity Channel Index"""
        try:
            typical_price = (data['high'] + data['low'] + data['close']) / 3
            sma = typical_price.rolling(window=period).mean()
            mad = typical_price.rolling(window=period).apply(
                lambda x: np.mean(np.abs(x - x.mean()))
            )
            cci = (typical_price - sma) / (0.015 * mad)
            return cci.iloc[-1] if not cci.empty else 0.0
        except:
            return 0.0
    
    def _calculate_adx(self, data: pd.DataFrame, period: int = 14) -> float:
        """Calculate Average Directional Index (simplified)"""
        try:
            high_diff = data['high'].diff()
            low_diff = data['low'].diff()
            
            plus_dm = np.where((high_diff > low_diff) & (high_diff > 0), high_diff, 0)
            minus_dm = np.where((low_diff > high_diff) & (low_diff > 0), low_diff, 0)
            
            tr = np.maximum(
                data['high'] - data['low'],
                np.maximum(
                    abs(data['high'] - data['close'].shift(1)),
                    abs(data['low'] - data['close'].shift(1))
                )
            )
            
            plus_dm_smooth = pd.Series(plus_dm).rolling(window=period).mean()
            minus_dm_smooth = pd.Series(minus_dm).rolling(window=period).mean()
            tr_smooth = pd.Series(tr).rolling(window=period).mean()
            
            plus_di = 100 * (plus_dm_smooth / tr_smooth)
            minus_di = 100 * (minus_dm_smooth / tr_smooth)
            
            dx = 100 * abs(plus_di - minus_di) / (plus_di + minus_di)
            adx = dx.rolling(window=period).mean()
            
            return adx.iloc[-1] if not adx.empty else 20.0
        except:
            return 20.0
    
    def _calculate_volume_ratio(self, data: pd.DataFrame, period: int = 20) -> float:
        """Calculate current volume vs SMA ratio"""
        try:
            if 'volume' not in data.columns:
                return 1.0
            
            volume_sma = data['volume'].rolling(window=period).mean()
            current_volume = data['volume'].iloc[-1]
            avg_volume = volume_sma.iloc[-1]
            
            return current_volume / avg_volume if avg_volume > 0 else 1.0
        except:
            return 1.0
    
    def _calculate_vwap_position(self, data: pd.DataFrame) -> float:
        """Calculate price position relative to VWAP"""
        try:
            if 'volume' not in data.columns:
                return 0.0
            
            typical_price = (data['high'] + data['low'] + data['close']) / 3
            vwap = (typical_price * data['volume']).cumsum() / data['volume'].cumsum()
            
            current_price = data['close'].iloc[-1]
            current_vwap = vwap.iloc[-1]
            
            return (current_price - current_vwap) / current_vwap
        except:
            return 0.0
    
    def _analyze_volume_profile(self, symbol: str, timeframe: str) -> VolumeProfile:
        """Analyze volume profile (simplified)"""
        try:
            data = self.data_manager.get_historical_data(symbol, timeframe, periods=50)
            
            if data is None or 'volume' not in data.columns:
                return self._get_default_volume_profile()
            
            # Simplified volume profile analysis
            volume_trend = self._determine_volume_trend(data)
            unusual_volume = self._detect_unusual_volume(data)
            
            # Simplified POC and value area
            price_range = data['close'].max() - data['close'].min()
            poc = data['close'].iloc[-1]  # Simplified
            value_area_high = poc + price_range * 0.1
            value_area_low = poc - price_range * 0.1
            
            return VolumeProfile(
                poc=poc,
                value_area_high=value_area_high,
                value_area_low=value_area_low,
                volume_at_price={},  # Simplified
                volume_trend=volume_trend,
                unusual_volume=unusual_volume
            )
            
        except Exception as e:
            logger.warning(f"Error analyzing volume profile: {e}")
            return self._get_default_volume_profile()
    
    def _determine_volume_trend(self, data: pd.DataFrame) -> str:
        """Determine volume trend"""
        try:
            recent_volume = data['volume'].tail(5).mean()
            older_volume = data['volume'].head(10).mean()
            
            if recent_volume > older_volume * 1.2:
                return 'increasing'
            elif recent_volume < older_volume * 0.8:
                return 'decreasing'
            else:
                return 'stable'
        except:
            return 'stable'
    
    def _detect_unusual_volume(self, data: pd.DataFrame) -> bool:
        """Detect unusual volume"""
        try:
            current_volume = data['volume'].iloc[-1]
            avg_volume = data['volume'].rolling(20).mean().iloc[-1]
            
            return current_volume > avg_volume * self.thresholds['volume_unusual']
        except:
            return False
    
    def _score_technical_factors(self, indicators: TechnicalIndicators,
                               signal: MultiTimeframeSignal) -> float:
        """Score technical indicator confirmation"""
        score = 50.0  # Base score
        
        # RSI confirmation
        if signal.primary_signal.momentum > 0:  # Bullish signal
            if indicators.rsi < self.thresholds['rsi_overbought']:
                score += 15  # Not overbought is good for bullish
            if indicators.rsi > 50:
                score += 10  # Above midline
        else:  # Bearish signal
            if indicators.rsi > self.thresholds['rsi_oversold']:
                score += 15  # Not oversold is good for bearish
            if indicators.rsi < 50:
                score += 10  # Below midline
        
        # MACD confirmation
        if signal.primary_signal.momentum > 0 and indicators.macd_signal == 'bullish':
            score += 20
        elif signal.primary_signal.momentum < 0 and indicators.macd_signal == 'bearish':
            score += 20
        elif indicators.macd_signal == 'neutral':
            score += 5
        
        # Stochastic confirmation
        if signal.primary_signal.momentum > 0:
            if indicators.stochastic < self.thresholds['stoch_overbought']:
                score += 10
        else:
            if indicators.stochastic > self.thresholds['stoch_oversold']:
                score += 10
        
        return max(0.0, min(100.0, score))
    
    def _score_volume_factors(self, volume_profile: VolumeProfile,
                            indicators: TechnicalIndicators,
                            signal: MultiTimeframeSignal) -> float:
        """Score volume confirmation factors"""
        score = 50.0  # Base score
        
        # Volume trend confirmation
        if signal.primary_signal.momentum > 0:  # Bullish
            if volume_profile.volume_trend == 'increasing':
                score += 25
            elif volume_profile.volume_trend == 'stable':
                score += 10
        else:  # Bearish
            if volume_profile.volume_trend == 'increasing':
                score += 20  # Volume on breakdown
            elif volume_profile.volume_trend == 'stable':
                score += 10
        
        # Unusual volume confirmation
        if volume_profile.unusual_volume:
            score += 15
        
        # Volume ratio confirmation
        if indicators.volume_sma_ratio > 1.2:
            score += 10
        elif indicators.volume_sma_ratio > 1.0:
            score += 5
        
        return max(0.0, min(100.0, score))
    
    def _score_momentum_factors(self, indicators: TechnicalIndicators,
                              signal: MultiTimeframeSignal) -> float:
        """Score momentum confirmation factors"""
        score = 50.0  # Base score
        
        # Williams %R confirmation
        signal_direction = 1 if signal.primary_signal.momentum > 0 else -1
        
        if signal_direction > 0:  # Bullish
            if indicators.williams_r > self.thresholds['williams_oversold']:
                score += 15
        else:  # Bearish
            if indicators.williams_r < self.thresholds['williams_overbought']:
                score += 15
        
        # CCI confirmation
        if signal_direction > 0 and indicators.cci > 0:
            score += 15
        elif signal_direction < 0 and indicators.cci < 0:
            score += 15
        
        # VWAP position confirmation
        if signal_direction > 0 and indicators.price_vs_vwap > 0:
            score += 10
        elif signal_direction < 0 and indicators.price_vs_vwap < 0:
            score += 10
        
        return max(0.0, min(100.0, score))
    
    def _score_trend_factors(self, indicators: TechnicalIndicators,
                           signal: MultiTimeframeSignal) -> float:
        """Score trend confirmation factors"""
        score = 50.0  # Base score
        
        # ADX trend strength
        if indicators.adx > self.thresholds['adx_trending']:
            score += 20  # Strong trend
        elif indicators.adx > 20:
            score += 10  # Moderate trend
        
        # Trend alignment with signal
        if signal.trend_alignment:
            score += 20
        
        # Multi-timeframe confirmation
        confirmation_ratio = signal.confirmations_count / max(signal.required_confirmations, 1)
        if confirmation_ratio >= 1.5:
            score += 15
        elif confirmation_ratio >= 1.0:
            score += 10
        
        return max(0.0, min(100.0, score))
    
    def _count_aligned_factors(self, technical: float, volume: float,
                             momentum: float, trend: float) -> int:
        """Count how many factors are aligned (above 60)"""
        threshold = 60.0
        factors = [technical, volume, momentum, trend]
        return sum(1 for factor in factors if factor >= threshold)
    
    def _calculate_confirmation_confidence(self, indicators: TechnicalIndicators,
                                         volume_profile: VolumeProfile,
                                         factors_aligned: int) -> float:
        """Calculate confidence in the confirmation analysis"""
        base_confidence = 60.0
        
        # More aligned factors = higher confidence
        base_confidence += factors_aligned * 10
        
        # Strong trend increases confidence
        if indicators.adx > 30:
            base_confidence += 15
        
        # Unusual volume increases confidence
        if volume_profile.unusual_volume:
            base_confidence += 10
        
        # Clear MACD signal increases confidence
        if indicators.macd_signal in ['bullish', 'bearish']:
            base_confidence += 10
        
        return max(0.0, min(100.0, base_confidence))
    
    def _get_default_indicators(self) -> TechnicalIndicators:
        """Get default technical indicators"""
        return TechnicalIndicators(
            rsi=50.0,
            macd_signal='neutral',
            stochastic=50.0,
            williams_r=-50.0,
            cci=0.0,
            adx=20.0,
            volume_sma_ratio=1.0,
            price_vs_vwap=0.0
        )
    
    def _get_default_volume_profile(self) -> VolumeProfile:
        """Get default volume profile"""
        return VolumeProfile(
            poc=0.0,
            value_area_high=0.0,
            value_area_low=0.0,
            volume_at_price={},
            volume_trend='stable',
            unusual_volume=False
        )
    
    def _get_default_confirmation(self) -> ConfirmationScore:
        """Get default confirmation score"""
        return ConfirmationScore(
            technical_score=50.0,
            volume_score=50.0,
            momentum_score=50.0,
            trend_score=50.0,
            overall_score=50.0,
            confidence=50.0,
            factors_aligned=0
        )
    
    def get_confirmation_summary(self) -> Dict:
        """Get confirmation system summary"""
        return {
            'cached_indicators': len(self.indicator_cache),
            'factor_weights': self.factor_weights,
            'thresholds': self.thresholds,
            'cache_duration_minutes': self.cache_duration.total_seconds() / 60
        }
