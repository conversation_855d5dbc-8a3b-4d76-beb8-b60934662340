#!/usr/bin/env python3
"""
TTM Squeeze Trading System - Web Interface Launcher
"""

from src.ui.app import create_app

def main():
    """Start the web interface"""
    print("=" * 60)
    print("TTM Squeeze Trading System - Web Interface")
    print("=" * 60)
    print("Starting Flask web server...")
    print("Access the interface at: http://127.0.0.1:5000")
    print("=" * 60)
    
    # Create and run the Flask app
    app = create_app()
    app.run(
        host='127.0.0.1',
        port=5000,
        debug=False,
        use_reloader=False
    )

if __name__ == "__main__":
    main()
