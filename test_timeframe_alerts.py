#!/usr/bin/env python3
"""
Test script to verify timeframe information is properly displayed in alerts
"""

import sys
from pathlib import Path

# Add src directory to Python path
sys.path.append(str(Path(__file__).parent / 'src'))

from src.scanner.real_time_scanner import RealTimeScanner
from src.utils.logger import setup_logging

def test_manual_scan_with_timeframes():
    """Test manual scanning to see timeframe information"""
    print("🎯 Testing Manual Scan with Timeframe Information")
    print("=" * 60)
    
    # Setup logging
    setup_logging()
    
    # Initialize scanner
    scanner = RealTimeScanner()
    
    # Test symbols that are likely to have some activity
    test_symbols = ['AAPL', 'MSFT', 'TSLA', 'NVDA', 'SPY']
    
    for symbol in test_symbols:
        print(f"\n📊 Scanning {symbol}:")
        
        try:
            # Manual scan
            signal = scanner.manual_scan_symbol(symbol)
            
            if signal:
                print(f"  ✅ Signal found!")
                print(f"     Entry Recommendation: {signal.entry_recommendation}")
                print(f"     Overall Strength: {signal.overall_strength:.2f}")
                print(f"     Primary Signal (15Min): Squeeze={signal.primary_signal.is_squeeze}, Entry={signal.primary_signal.entry_signal}")
                
                # Show confirmation signals
                print(f"     Confirmation Signals:")
                for tf, conf_signal in signal.confirmation_signals.items():
                    print(f"       {tf}: Squeeze={conf_signal.is_squeeze}, Entry={conf_signal.entry_signal}, Momentum={conf_signal.momentum:.2f}")
                
                # Test alert generation
                alert = scanner._check_alert_conditions(signal)
                if alert:
                    print(f"  🚨 ALERT GENERATED:")
                    print(f"     Type: {alert.alert_type}")
                    print(f"     Message: {alert.message}")
                    print(f"     Timeframes Triggered: {alert.timeframes_triggered}")
                    print(f"     Timeframe Details: {alert.timeframe_details}")
                else:
                    print(f"  ⚪ Signal found but no alert generated (strength too low)")
            else:
                print(f"  ❌ No signal detected")
                
        except Exception as e:
            print(f"  ❌ Error: {e}")
    
    print(f"\n✅ Manual scan test completed!")

if __name__ == "__main__":
    test_manual_scan_with_timeframes()
