"""
Intelligent Dashboard Enhancements for TTM Squeeze Trading System
AI-driven dashboard personalization and conversational chat interface
"""

import asyncio
import json
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from collections import defaultdict, Counter
import logging

from .chatgpt_assistant import ChatGPTTradingAssistant, TradingContextManager

logger = logging.getLogger(__name__)

@dataclass
class UserBehaviorPattern:
    """User behavior pattern analysis"""
    user_id: str
    session_duration_avg: float
    most_used_features: List[str]
    preferred_timeframes: List[str]
    trading_style: str  # 'conservative', 'moderate', 'aggressive'
    alert_preferences: Dict[str, Any]
    dashboard_layout_preferences: Dict[str, Any]
    interaction_frequency: Dict[str, int]
    performance_focus: List[str]  # What metrics user looks at most

@dataclass
class DashboardPersonalization:
    """Personalized dashboard configuration"""
    user_id: str
    layout_config: Dict[str, Any]
    widget_priorities: Dict[str, int]
    alert_settings: Dict[str, Any]
    color_scheme: str
    refresh_intervals: Dict[str, int]
    default_views: Dict[str, str]
    quick_actions: List[str]

@dataclass
class ChatMessage:
    """Chat message structure"""
    message_id: str
    user_id: str
    message: str
    timestamp: datetime
    message_type: str  # 'user', 'assistant', 'system'
    context: Optional[Dict[str, Any]] = None
    response: Optional[str] = None
    actions_taken: Optional[List[str]] = None

class UserBehaviorAnalyzer:
    """Analyze user behavior patterns for personalization"""
    
    def __init__(self):
        self.user_sessions = defaultdict(list)
        self.user_interactions = defaultdict(list)
        self.feature_usage = defaultdict(Counter)
        self.performance_tracking = defaultdict(list)
    
    def track_user_interaction(self, user_id: str, interaction_data: Dict[str, Any]):
        """Track user interaction for behavior analysis"""
        interaction = {
            'timestamp': datetime.now(),
            'feature': interaction_data.get('feature'),
            'action': interaction_data.get('action'),
            'duration': interaction_data.get('duration', 0),
            'context': interaction_data.get('context', {})
        }
        
        self.user_interactions[user_id].append(interaction)
        
        # Update feature usage counter
        feature = interaction_data.get('feature')
        if feature:
            self.feature_usage[user_id][feature] += 1
    
    def start_user_session(self, user_id: str):
        """Start tracking a user session"""
        session = {
            'start_time': datetime.now(),
            'interactions': [],
            'features_used': set(),
            'alerts_viewed': 0,
            'trades_executed': 0
        }
        
        self.user_sessions[user_id].append(session)
    
    def end_user_session(self, user_id: str):
        """End current user session"""
        if user_id in self.user_sessions and self.user_sessions[user_id]:
            current_session = self.user_sessions[user_id][-1]
            if 'end_time' not in current_session:
                current_session['end_time'] = datetime.now()
                current_session['duration'] = (
                    current_session['end_time'] - current_session['start_time']
                ).total_seconds()
    
    def analyze_user_behavior(self, user_id: str, days_back: int = 30) -> UserBehaviorPattern:
        """Analyze user behavior patterns"""
        cutoff_date = datetime.now() - timedelta(days=days_back)
        
        # Filter recent interactions
        recent_interactions = [
            interaction for interaction in self.user_interactions[user_id]
            if interaction['timestamp'] > cutoff_date
        ]
        
        recent_sessions = [
            session for session in self.user_sessions[user_id]
            if session['start_time'] > cutoff_date and 'duration' in session
        ]
        
        # Calculate session duration average
        session_durations = [session['duration'] for session in recent_sessions]
        avg_session_duration = np.mean(session_durations) if session_durations else 0
        
        # Most used features
        feature_counts = Counter()
        for interaction in recent_interactions:
            if interaction['feature']:
                feature_counts[interaction['feature']] += 1
        
        most_used_features = [feature for feature, count in feature_counts.most_common(5)]
        
        # Preferred timeframes (from context analysis)
        timeframe_usage = Counter()
        for interaction in recent_interactions:
            context = interaction.get('context', {})
            timeframe = context.get('timeframe')
            if timeframe:
                timeframe_usage[timeframe] += 1
        
        preferred_timeframes = [tf for tf, count in timeframe_usage.most_common(3)]
        
        # Trading style analysis
        trading_style = self._analyze_trading_style(recent_interactions)
        
        # Alert preferences
        alert_preferences = self._analyze_alert_preferences(recent_interactions)
        
        # Dashboard layout preferences
        layout_preferences = self._analyze_layout_preferences(recent_interactions)
        
        # Interaction frequency
        interaction_frequency = self._calculate_interaction_frequency(recent_interactions)
        
        # Performance focus
        performance_focus = self._analyze_performance_focus(recent_interactions)
        
        return UserBehaviorPattern(
            user_id=user_id,
            session_duration_avg=avg_session_duration,
            most_used_features=most_used_features,
            preferred_timeframes=preferred_timeframes,
            trading_style=trading_style,
            alert_preferences=alert_preferences,
            dashboard_layout_preferences=layout_preferences,
            interaction_frequency=interaction_frequency,
            performance_focus=performance_focus
        )
    
    def _analyze_trading_style(self, interactions: List[Dict[str, Any]]) -> str:
        """Analyze trading style from interactions"""
        risk_indicators = {
            'conservative': 0,
            'moderate': 0,
            'aggressive': 0
        }
        
        for interaction in interactions:
            feature = interaction.get('feature', '')
            action = interaction.get('action', '')
            context = interaction.get('context', {})
            
            # Conservative indicators
            if 'risk' in feature.lower() or 'safety' in action.lower():
                risk_indicators['conservative'] += 2
            
            if context.get('position_size', 0) < 0.02:  # Small position sizes
                risk_indicators['conservative'] += 1
            
            # Aggressive indicators
            if 'leverage' in feature.lower() or 'margin' in action.lower():
                risk_indicators['aggressive'] += 2
            
            if context.get('position_size', 0) > 0.05:  # Large position sizes
                risk_indicators['aggressive'] += 1
            
            # Moderate indicators (default)
            risk_indicators['moderate'] += 1
        
        return max(risk_indicators, key=risk_indicators.get)
    
    def _analyze_alert_preferences(self, interactions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze alert preferences"""
        alert_interactions = [
            i for i in interactions 
            if 'alert' in i.get('feature', '').lower()
        ]
        
        preferences = {
            'frequency': 'medium',  # high, medium, low
            'types': [],
            'timeframes': [],
            'confidence_threshold': 0.7
        }
        
        if len(alert_interactions) > 50:
            preferences['frequency'] = 'high'
        elif len(alert_interactions) < 10:
            preferences['frequency'] = 'low'
        
        # Extract preferred alert types and timeframes
        for interaction in alert_interactions:
            context = interaction.get('context', {})
            alert_type = context.get('alert_type')
            timeframe = context.get('timeframe')
            
            if alert_type:
                preferences['types'].append(alert_type)
            if timeframe:
                preferences['timeframes'].append(timeframe)
        
        # Get most common preferences
        preferences['types'] = [t for t, c in Counter(preferences['types']).most_common(3)]
        preferences['timeframes'] = [t for t, c in Counter(preferences['timeframes']).most_common(3)]
        
        return preferences
    
    def _analyze_layout_preferences(self, interactions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze dashboard layout preferences"""
        layout_interactions = [
            i for i in interactions 
            if 'dashboard' in i.get('feature', '').lower() or 'panel' in i.get('feature', '').lower()
        ]
        
        preferences = {
            'preferred_panels': [],
            'panel_sizes': {},
            'layout_style': 'default'
        }
        
        panel_usage = Counter()
        for interaction in layout_interactions:
            context = interaction.get('context', {})
            panel = context.get('panel')
            if panel:
                panel_usage[panel] += 1
        
        preferences['preferred_panels'] = [p for p, c in panel_usage.most_common(5)]
        
        return preferences
    
    def _calculate_interaction_frequency(self, interactions: List[Dict[str, Any]]) -> Dict[str, int]:
        """Calculate interaction frequency by hour of day"""
        hourly_frequency = defaultdict(int)
        
        for interaction in interactions:
            hour = interaction['timestamp'].hour
            hourly_frequency[str(hour)] += 1
        
        return dict(hourly_frequency)
    
    def _analyze_performance_focus(self, interactions: List[Dict[str, Any]]) -> List[str]:
        """Analyze which performance metrics user focuses on"""
        performance_interactions = [
            i for i in interactions 
            if 'performance' in i.get('feature', '').lower() or 'metric' in i.get('feature', '').lower()
        ]
        
        metric_focus = Counter()
        for interaction in performance_interactions:
            context = interaction.get('context', {})
            metric = context.get('metric')
            if metric:
                metric_focus[metric] += 1
        
        return [metric for metric, count in metric_focus.most_common(5)]

class DashboardPersonalizationEngine:
    """Generate personalized dashboard configurations"""
    
    def __init__(self, behavior_analyzer: UserBehaviorAnalyzer):
        self.behavior_analyzer = behavior_analyzer
        self.default_layouts = {
            'conservative': {
                'primary_panels': ['risk_management', 'portfolio_overview', 'alerts'],
                'secondary_panels': ['performance', 'news'],
                'refresh_rate': 30,
                'color_scheme': 'blue'
            },
            'moderate': {
                'primary_panels': ['scanner', 'alerts', 'portfolio_overview'],
                'secondary_panels': ['performance', 'trading', 'news'],
                'refresh_rate': 15,
                'color_scheme': 'green'
            },
            'aggressive': {
                'primary_panels': ['trading', 'scanner', 'alerts'],
                'secondary_panels': ['performance', 'risk_management'],
                'refresh_rate': 5,
                'color_scheme': 'red'
            }
        }
    
    def generate_personalization(self, user_id: str) -> DashboardPersonalization:
        """Generate personalized dashboard configuration"""
        # Analyze user behavior
        behavior_pattern = self.behavior_analyzer.analyze_user_behavior(user_id)
        
        # Get base layout for trading style
        base_layout = self.default_layouts.get(
            behavior_pattern.trading_style, 
            self.default_layouts['moderate']
        )
        
        # Customize based on user preferences
        layout_config = self._customize_layout(behavior_pattern, base_layout)
        widget_priorities = self._calculate_widget_priorities(behavior_pattern)
        alert_settings = self._customize_alert_settings(behavior_pattern)
        refresh_intervals = self._optimize_refresh_intervals(behavior_pattern)
        default_views = self._set_default_views(behavior_pattern)
        quick_actions = self._generate_quick_actions(behavior_pattern)
        
        return DashboardPersonalization(
            user_id=user_id,
            layout_config=layout_config,
            widget_priorities=widget_priorities,
            alert_settings=alert_settings,
            color_scheme=base_layout['color_scheme'],
            refresh_intervals=refresh_intervals,
            default_views=default_views,
            quick_actions=quick_actions
        )
    
    def _customize_layout(self, behavior: UserBehaviorPattern, base_layout: Dict[str, Any]) -> Dict[str, Any]:
        """Customize layout based on user behavior"""
        layout = base_layout.copy()
        
        # Prioritize most used features
        if behavior.most_used_features:
            # Move most used features to primary panels
            primary_panels = []
            for feature in behavior.most_used_features[:3]:
                if feature not in primary_panels:
                    primary_panels.append(feature)
            
            # Add remaining base primary panels
            for panel in base_layout['primary_panels']:
                if panel not in primary_panels and len(primary_panels) < 4:
                    primary_panels.append(panel)
            
            layout['primary_panels'] = primary_panels
        
        # Adjust based on session duration
        if behavior.session_duration_avg > 3600:  # Long sessions
            layout['detailed_view'] = True
            layout['panel_count'] = 6
        else:  # Short sessions
            layout['detailed_view'] = False
            layout['panel_count'] = 4
        
        return layout
    
    def _calculate_widget_priorities(self, behavior: UserBehaviorPattern) -> Dict[str, int]:
        """Calculate widget priorities based on usage"""
        priorities = {}
        
        # Base priorities
        base_priorities = {
            'scanner': 5,
            'alerts': 5,
            'portfolio': 4,
            'performance': 3,
            'trading': 3,
            'news': 2,
            'risk': 2
        }
        
        # Adjust based on most used features
        for i, feature in enumerate(behavior.most_used_features):
            boost = 5 - i  # First feature gets +5, second +4, etc.
            if feature in base_priorities:
                priorities[feature] = base_priorities[feature] + boost
            else:
                priorities[feature] = boost
        
        # Fill in remaining with base priorities
        for feature, priority in base_priorities.items():
            if feature not in priorities:
                priorities[feature] = priority
        
        return priorities
    
    def _customize_alert_settings(self, behavior: UserBehaviorPattern) -> Dict[str, Any]:
        """Customize alert settings"""
        settings = behavior.alert_preferences.copy()
        
        # Add intelligent defaults
        if not settings.get('types'):
            settings['types'] = ['ttm_squeeze', 'breakout', 'volume_spike']
        
        if not settings.get('timeframes'):
            settings['timeframes'] = behavior.preferred_timeframes or ['15Min', '1Hour']
        
        # Adjust confidence threshold based on trading style
        if behavior.trading_style == 'conservative':
            settings['confidence_threshold'] = 0.8
        elif behavior.trading_style == 'aggressive':
            settings['confidence_threshold'] = 0.6
        else:
            settings['confidence_threshold'] = 0.7
        
        return settings
    
    def _optimize_refresh_intervals(self, behavior: UserBehaviorPattern) -> Dict[str, int]:
        """Optimize refresh intervals based on user behavior"""
        base_intervals = {
            'scanner': 10,
            'alerts': 5,
            'portfolio': 30,
            'performance': 60,
            'news': 300
        }
        
        # Adjust based on trading style
        if behavior.trading_style == 'aggressive':
            # Faster updates for aggressive traders
            multiplier = 0.5
        elif behavior.trading_style == 'conservative':
            # Slower updates for conservative traders
            multiplier = 2.0
        else:
            multiplier = 1.0
        
        # Adjust based on session duration
        if behavior.session_duration_avg > 3600:  # Long sessions
            multiplier *= 0.8  # Slightly faster updates
        
        intervals = {}
        for component, interval in base_intervals.items():
            intervals[component] = max(1, int(interval * multiplier))
        
        return intervals
    
    def _set_default_views(self, behavior: UserBehaviorPattern) -> Dict[str, str]:
        """Set default views for different components"""
        views = {}
        
        # Scanner default view
        if 'scanner' in behavior.most_used_features:
            views['scanner'] = 'detailed'
        else:
            views['scanner'] = 'summary'
        
        # Performance default view
        if behavior.performance_focus:
            if 'returns' in behavior.performance_focus:
                views['performance'] = 'returns'
            elif 'risk' in behavior.performance_focus:
                views['performance'] = 'risk'
            else:
                views['performance'] = 'overview'
        else:
            views['performance'] = 'overview'
        
        # Alerts default view
        views['alerts'] = 'recent' if behavior.alert_preferences.get('frequency') == 'high' else 'filtered'
        
        return views
    
    def _generate_quick_actions(self, behavior: UserBehaviorPattern) -> List[str]:
        """Generate quick action buttons based on user behavior"""
        actions = []
        
        # Add actions based on most used features
        feature_actions = {
            'trading': ['quick_buy', 'quick_sell', 'close_all'],
            'scanner': ['run_scan', 'filter_alerts', 'export_results'],
            'alerts': ['mark_read', 'create_alert', 'alert_settings'],
            'portfolio': ['rebalance', 'add_position', 'risk_check'],
            'performance': ['generate_report', 'export_data', 'compare_benchmark']
        }
        
        for feature in behavior.most_used_features[:3]:
            if feature in feature_actions:
                actions.extend(feature_actions[feature][:2])  # Top 2 actions per feature
        
        # Add trading style specific actions
        if behavior.trading_style == 'aggressive':
            actions.extend(['leverage_check', 'margin_status'])
        elif behavior.trading_style == 'conservative':
            actions.extend(['risk_analysis', 'diversification_check'])
        
        return actions[:6]  # Limit to 6 quick actions

class ConversationalDashboard:
    """Conversational interface for dashboard interaction"""
    
    def __init__(self, context_manager: TradingContextManager):
        self.context_manager = context_manager
        self.chat_assistant = ChatGPTTradingAssistant(context_manager)
        self.conversation_history = defaultdict(list)
        self.active_sessions = {}
    
    async def process_chat_message(self, user_id: str, message: str) -> Dict[str, Any]:
        """Process chat message and return response"""
        try:
            # Create chat message object
            chat_message = ChatMessage(
                message_id=f"{user_id}_{datetime.now().timestamp()}",
                user_id=user_id,
                message=message,
                timestamp=datetime.now(),
                message_type='user'
            )
            
            # Add to conversation history
            self.conversation_history[user_id].append(chat_message)
            
            # Process with ChatGPT assistant
            response_data = await self.chat_assistant.process_trading_message(message)
            
            # Create response message
            response_message = ChatMessage(
                message_id=f"{user_id}_response_{datetime.now().timestamp()}",
                user_id=user_id,
                message=response_data.get('response', ''),
                timestamp=datetime.now(),
                message_type='assistant',
                context=response_data.get('context'),
                actions_taken=response_data.get('actions', [])
            )
            
            self.conversation_history[user_id].append(response_message)
            
            # Limit conversation history
            if len(self.conversation_history[user_id]) > 50:
                self.conversation_history[user_id] = self.conversation_history[user_id][-50:]
            
            return {
                'success': True,
                'response': response_data.get('response'),
                'message_id': response_message.message_id,
                'actions': response_data.get('actions', []),
                'timestamp': response_message.timestamp.isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error processing chat message: {e}")
            return {
                'success': False,
                'error': str(e),
                'response': "I'm sorry, I encountered an error processing your message. Please try again."
            }
    
    async def get_conversation_history(self, user_id: str, limit: int = 20) -> List[Dict[str, Any]]:
        """Get conversation history for a user"""
        history = self.conversation_history[user_id][-limit:]
        
        return [
            {
                'message_id': msg.message_id,
                'message': msg.message,
                'type': msg.message_type,
                'timestamp': msg.timestamp.isoformat(),
                'actions': msg.actions_taken or []
            }
            for msg in history
        ]
    
    async def generate_proactive_insights(self, user_id: str) -> Optional[str]:
        """Generate proactive insights based on current market conditions"""
        try:
            # Get current market context
            portfolio = await self.context_manager.get_portfolio_summary()
            alerts = await self.context_manager.get_recent_alerts(5)
            market_status = await self.context_manager.get_market_status()
            
            # Generate insights if there are interesting developments
            if len(alerts) > 3:  # Multiple alerts
                insight = f"I notice {len(alerts)} new TTM Squeeze alerts. "
                top_symbols = [alert.get('symbol') for alert in alerts[:3]]
                insight += f"Top opportunities: {', '.join(top_symbols)}. Would you like me to analyze any of these?"
                return insight
            
            # Market volatility insight
            vix_level = market_status.get('vix_level', 20)
            if vix_level > 30:
                return f"Market volatility is elevated (VIX: {vix_level:.1f}). Consider reducing position sizes or tightening stops."
            elif vix_level < 15:
                return f"Market volatility is low (VIX: {vix_level:.1f}). This could be a good time for new positions."
            
            return None
            
        except Exception as e:
            logger.error(f"Error generating proactive insights: {e}")
            return None

class IntelligentDashboardManager:
    """Main manager for intelligent dashboard features"""
    
    def __init__(self, context_manager: TradingContextManager):
        self.context_manager = context_manager
        self.behavior_analyzer = UserBehaviorAnalyzer()
        self.personalization_engine = DashboardPersonalizationEngine(self.behavior_analyzer)
        self.conversational_dashboard = ConversationalDashboard(context_manager)
        self.user_personalizations = {}
    
    async def get_personalized_dashboard(self, user_id: str) -> Dict[str, Any]:
        """Get personalized dashboard configuration for user"""
        # Generate or retrieve personalization
        if user_id not in self.user_personalizations:
            personalization = self.personalization_engine.generate_personalization(user_id)
            self.user_personalizations[user_id] = personalization
        else:
            personalization = self.user_personalizations[user_id]
        
        # Get proactive insights
        insights = await self.conversational_dashboard.generate_proactive_insights(user_id)
        
        return {
            'layout_config': personalization.layout_config,
            'widget_priorities': personalization.widget_priorities,
            'alert_settings': personalization.alert_settings,
            'color_scheme': personalization.color_scheme,
            'refresh_intervals': personalization.refresh_intervals,
            'default_views': personalization.default_views,
            'quick_actions': personalization.quick_actions,
            'proactive_insights': insights
        }
    
    def track_user_interaction(self, user_id: str, interaction_data: Dict[str, Any]):
        """Track user interaction for behavior analysis"""
        self.behavior_analyzer.track_user_interaction(user_id, interaction_data)
        
        # Regenerate personalization periodically
        interaction_count = len(self.behavior_analyzer.user_interactions[user_id])
        if interaction_count % 100 == 0:  # Every 100 interactions
            personalization = self.personalization_engine.generate_personalization(user_id)
            self.user_personalizations[user_id] = personalization
    
    async def process_chat_message(self, user_id: str, message: str) -> Dict[str, Any]:
        """Process chat message"""
        return await self.conversational_dashboard.process_chat_message(user_id, message)
    
    async def get_chat_history(self, user_id: str, limit: int = 20) -> List[Dict[str, Any]]:
        """Get chat history"""
        return await self.conversational_dashboard.get_conversation_history(user_id, limit)
    
    def get_user_behavior_summary(self, user_id: str) -> Dict[str, Any]:
        """Get user behavior summary"""
        try:
            behavior = self.behavior_analyzer.analyze_user_behavior(user_id)
            return {
                'trading_style': behavior.trading_style,
                'session_duration_avg': behavior.session_duration_avg,
                'most_used_features': behavior.most_used_features,
                'preferred_timeframes': behavior.preferred_timeframes,
                'interaction_count': len(self.behavior_analyzer.user_interactions[user_id])
            }
        except Exception as e:
            logger.error(f"Error getting user behavior summary: {e}")
            return {'error': str(e)}
