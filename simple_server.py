#!/usr/bin/env python3
"""
Simple HTTP Server for TTM Squeeze Professional Dashboard
This creates a minimal web server to serve the professional dashboard
"""

import http.server
import socketserver
import webbrowser
import os
import threading
import time

PORT = 8000

class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/':
            self.path = '/professional_dashboard_demo.html'
        elif self.path == '/pro':
            self.path = '/professional_dashboard_demo.html'
        return http.server.SimpleHTTPRequestHandler.do_GET(self)

def start_server():
    """Start the HTTP server"""
    try:
        with socketserver.TCPServer(("", PORT), CustomHTTPRequestHandler) as httpd:
            print("=" * 60)
            print("🚀 TTM SQUEEZE PROFESSIONAL DASHBOARD SERVER")
            print("=" * 60)
            print(f"📊 Server running at: http://localhost:{PORT}")
            print(f"🎯 Professional Dashboard: http://localhost:{PORT}/pro")
            print(f"🌐 Direct Access: http://localhost:{PORT}")
            print("=" * 60)
            print("✅ Server is ready! Opening dashboard in browser...")
            print("❌ Press Ctrl+C to stop the server")
            print("=" * 60)
            
            # Open browser after a short delay
            def open_browser():
                time.sleep(2)
                webbrowser.open(f'http://localhost:{PORT}/pro')
            
            browser_thread = threading.Thread(target=open_browser)
            browser_thread.daemon = True
            browser_thread.start()
            
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        print("🔧 Trying alternative method...")
        
        # Fallback: open HTML file directly
        html_file = os.path.join(os.getcwd(), 'professional_dashboard_demo.html')
        if os.path.exists(html_file):
            print(f"📂 Opening HTML file directly: {html_file}")
            webbrowser.open(f'file://{html_file}')
        else:
            print("❌ HTML file not found")

if __name__ == "__main__":
    # Change to the script directory
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    start_server()
