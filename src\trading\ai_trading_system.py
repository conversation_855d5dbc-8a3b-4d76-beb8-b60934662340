"""
Comprehensive AI-Enhanced Automated Trading System
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional
import logging
import asyncio
from datetime import datetime, timedelta

from .automated_trading_engine import AutomatedTradingEngine, TradingMode
from .ai_stop_loss_manager import AIStopLossManager
from .safety_controls import TradingSafetyControls
from .performance_tracker import PerformanceTracker
from ..ai.signal_enhancer import AISignalEnhancer, AIEnhancedSignal
from ..ai.openai_market_intelligence import OpenAIMarketIntelligence

logger = logging.getLogger(__name__)

class AITradingSystem:
    """
    Comprehensive AI-Enhanced Automated Trading System
    
    Integrates all AI-powered trading components:
    - AI Signal Enhancement
    - Automated Trading Engine
    - Dynamic Stop Loss Management
    - Safety Controls & Circuit Breakers
    - Performance Tracking
    - OpenAI Market Intelligence
    """
    
    def __init__(self, data_manager):
        self.data_manager = data_manager
        
        # Initialize core components
        self.ai_enhancer = AISignalEnhancer(data_manager)
        self.trading_engine = AutomatedTradingEngine(data_manager, self.ai_enhancer)
        self.safety_controls = TradingSafetyControls(data_manager, self.trading_engine.trader)
        self.performance_tracker = PerformanceTracker()
        self.openai_intelligence = OpenAIMarketIntelligence()
        
        # System state
        self.system_enabled = False
        self.monitoring_active = False
        self.last_health_check = None
        
        # Configuration
        self.config = {
            'auto_trading_enabled': False,
            'paper_trading_mode': True,
            'min_signal_score': 85.0,
            'min_confirmation_score': 80.0,
            'max_daily_trades': 10,
            'monitoring_interval_seconds': 30,
            'health_check_interval_minutes': 5
        }
        
        logger.info("AI Trading System initialized")
    
    async def start_system(self):
        """Start the complete AI trading system"""
        try:
            logger.info("Starting AI Trading System...")
            
            # Perform system health check
            health_status = await self.perform_health_check()
            if not health_status['overall_healthy']:
                logger.error("System health check failed - cannot start")
                return False
            
            # Enable system components
            self.system_enabled = True
            self.monitoring_active = True
            
            # Configure trading engine
            self.trading_engine.set_config({
                'enabled': self.config['auto_trading_enabled'],
                'mode': TradingMode.PAPER if self.config['paper_trading_mode'] else TradingMode.LIVE,
                'min_signal_score': self.config['min_signal_score'],
                'min_confirmation_score': self.config['min_confirmation_score'],
                'max_daily_trades': self.config['max_daily_trades']
            })
            
            # Start monitoring loop
            asyncio.create_task(self._monitoring_loop())
            
            logger.info("AI Trading System started successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error starting AI Trading System: {e}")
            return False
    
    async def stop_system(self):
        """Stop the AI trading system"""
        try:
            logger.info("Stopping AI Trading System...")
            
            # Disable system
            self.system_enabled = False
            self.monitoring_active = False
            
            # Emergency stop trading
            self.trading_engine.emergency_stop_all()
            
            # Export final performance report
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            self.performance_tracker.export_performance_report(
                f"reports/final_performance_{timestamp}.json"
            )
            
            logger.info("AI Trading System stopped")
            
        except Exception as e:
            logger.error(f"Error stopping AI Trading System: {e}")
    
    async def process_ttm_squeeze_signal(self, signal, market_data: Dict) -> Optional[Dict]:
        """
        Process a TTM Squeeze signal through the complete AI pipeline
        
        Args:
            signal: Original TTM Squeeze signal
            market_data: Current market data
            
        Returns:
            Processing result with trade execution details
        """
        try:
            if not self.system_enabled:
                return None
            
            # Step 1: AI Enhancement
            enhanced_signal = await self.ai_enhancer.enhance_signal(signal, market_data)
            
            # Step 2: Safety Checks
            current_positions = self.trading_engine.trader.get_positions()
            safety_status = self.safety_controls.check_all_safety_conditions(
                current_positions, market_data
            )
            
            if not safety_status['overall_safe']:
                logger.warning(f"Safety checks failed for {signal.symbol} - trade blocked")
                return {
                    'symbol': signal.symbol,
                    'action': 'blocked',
                    'reason': 'safety_checks_failed',
                    'safety_status': safety_status
                }
            
            # Step 3: OpenAI Market Intelligence (if enabled)
            market_intelligence = await self._get_market_intelligence(
                signal.symbol, market_data
            )
            
            # Step 4: Final Signal Assessment
            final_assessment = self._assess_final_signal(
                enhanced_signal, market_intelligence, safety_status
            )
            
            if not final_assessment['trade_approved']:
                return {
                    'symbol': signal.symbol,
                    'action': 'rejected',
                    'reason': final_assessment['rejection_reason'],
                    'enhanced_signal': enhanced_signal,
                    'market_intelligence': market_intelligence
                }
            
            # Step 5: Execute Trade (if auto-trading enabled)
            trade_result = None
            if self.config['auto_trading_enabled']:
                trade_result = await self.trading_engine.process_signal(enhanced_signal)
            
            # Step 6: Record for Performance Tracking
            self._record_signal_processing(
                enhanced_signal, market_intelligence, final_assessment, trade_result
            )
            
            return {
                'symbol': signal.symbol,
                'action': 'processed',
                'enhanced_signal': enhanced_signal,
                'market_intelligence': market_intelligence,
                'final_assessment': final_assessment,
                'trade_result': trade_result,
                'auto_trading_enabled': self.config['auto_trading_enabled']
            }
            
        except Exception as e:
            logger.error(f"Error processing TTM Squeeze signal: {e}")
            return {
                'symbol': signal.symbol if hasattr(signal, 'symbol') else 'unknown',
                'action': 'error',
                'error': str(e)
            }
    
    async def _monitoring_loop(self):
        """Main monitoring loop for the AI trading system"""
        try:
            while self.monitoring_active:
                # Monitor active positions
                await self.trading_engine.monitor_positions()
                
                # Perform periodic health checks
                if (not self.last_health_check or 
                    datetime.now() - self.last_health_check > 
                    timedelta(minutes=self.config['health_check_interval_minutes'])):
                    
                    await self.perform_health_check()
                
                # Update performance metrics
                self._update_performance_metrics()
                
                # Sleep until next monitoring cycle
                await asyncio.sleep(self.config['monitoring_interval_seconds'])
                
        except Exception as e:
            logger.error(f"Error in monitoring loop: {e}")
            # Emergency stop on monitoring failure
            await self.stop_system()
    
    async def perform_health_check(self) -> Dict:
        """Perform comprehensive system health check"""
        try:
            health_status = {
                'timestamp': datetime.now().isoformat(),
                'overall_healthy': True,
                'components': {}
            }
            
            # Check AI enhancer
            ai_status = self.ai_enhancer.get_model_status()
            health_status['components']['ai_enhancer'] = {
                'healthy': ai_status['models_trained'],
                'details': ai_status
            }
            
            # Check trading engine
            trading_status = self.trading_engine.get_status()
            health_status['components']['trading_engine'] = {
                'healthy': not trading_status['emergency_stop'],
                'details': trading_status
            }
            
            # Check safety controls
            safety_status = self.safety_controls.get_safety_status()
            health_status['components']['safety_controls'] = {
                'healthy': len(safety_status['circuit_breakers_active']) == 0,
                'details': safety_status
            }
            
            # Check data connectivity
            try:
                test_data = self.data_manager.get_historical_data('SPY', '1Day', periods=1)
                data_healthy = test_data is not None and len(test_data) > 0
            except Exception:
                data_healthy = False
            
            health_status['components']['data_manager'] = {
                'healthy': data_healthy,
                'details': {'connectivity': data_healthy}
            }
            
            # Check OpenAI connectivity
            openai_status = self.openai_intelligence.get_cache_stats()
            health_status['components']['openai_intelligence'] = {
                'healthy': True,  # Always healthy (has fallbacks)
                'details': openai_status
            }
            
            # Overall health assessment
            component_health = [comp['healthy'] for comp in health_status['components'].values()]
            health_status['overall_healthy'] = all(component_health)
            
            self.last_health_check = datetime.now()
            
            if not health_status['overall_healthy']:
                logger.warning("System health check detected issues")
            
            return health_status
            
        except Exception as e:
            logger.error(f"Error performing health check: {e}")
            return {
                'timestamp': datetime.now().isoformat(),
                'overall_healthy': False,
                'error': str(e)
            }
    
    async def _get_market_intelligence(self, symbol: str, market_data: Dict) -> Dict:
        """Get comprehensive market intelligence for symbol"""
        try:
            # Get sample news headlines (in production, integrate with news APIs)
            sample_headlines = [
                f"{symbol} quarterly earnings report",
                f"Market analysis for {symbol}",
                f"Technical analysis update for {symbol}"
            ]
            
            # Get comprehensive intelligence
            intelligence = await self.openai_intelligence.get_comprehensive_market_intelligence(
                symbol=symbol,
                market_data=market_data,
                news_headlines=sample_headlines,
                economic_events=[]  # Would be populated with actual events
            )
            
            return intelligence
            
        except Exception as e:
            logger.warning(f"Error getting market intelligence: {e}")
            return {
                'symbol': symbol,
                'error': str(e),
                'overall_assessment': 'neutral'
            }
    
    def _assess_final_signal(self, enhanced_signal: AIEnhancedSignal,
                           market_intelligence: Dict, safety_status: Dict) -> Dict:
        """Make final assessment on whether to trade the signal"""
        try:
            # Base approval on enhanced signal score
            trade_approved = enhanced_signal.final_score >= self.config['min_signal_score']
            rejection_reason = None
            
            # Additional checks
            if not trade_approved:
                rejection_reason = f"Signal score too low: {enhanced_signal.final_score:.1f}"
            
            # Market intelligence check
            elif market_intelligence.get('overall_assessment') == 'bearish':
                trade_approved = False
                rejection_reason = "Bearish market intelligence assessment"
            
            # Safety status check
            elif not safety_status['overall_safe']:
                trade_approved = False
                rejection_reason = "Safety controls triggered"
            
            return {
                'trade_approved': trade_approved,
                'rejection_reason': rejection_reason,
                'final_score': enhanced_signal.final_score,
                'market_assessment': market_intelligence.get('overall_assessment', 'neutral'),
                'safety_cleared': safety_status['overall_safe']
            }
            
        except Exception as e:
            logger.error(f"Error assessing final signal: {e}")
            return {
                'trade_approved': False,
                'rejection_reason': f"Assessment error: {str(e)}"
            }
    
    def _record_signal_processing(self, enhanced_signal: AIEnhancedSignal,
                                market_intelligence: Dict, final_assessment: Dict,
                                trade_result: Optional[Dict]):
        """Record signal processing for performance tracking"""
        try:
            # This would be used to update AI model performance
            # when actual trade outcomes are available
            
            signal_data = {
                'symbol': enhanced_signal.original_signal.symbol,
                'timestamp': datetime.now().isoformat(),
                'ai_signal_score': enhanced_signal.final_score,
                'ai_confidence': enhanced_signal.ai_confidence,
                'market_regime': enhanced_signal.market_regime,
                'trade_approved': final_assessment['trade_approved'],
                'trade_executed': trade_result is not None,
                'market_intelligence': market_intelligence.get('overall_assessment', 'neutral')
            }
            
            # Store for later performance analysis
            # This would be enhanced to track actual outcomes
            
        except Exception as e:
            logger.error(f"Error recording signal processing: {e}")
    
    def _update_performance_metrics(self):
        """Update performance metrics"""
        try:
            # Get completed trades from trading engine
            completed_trades = [
                trade for trade in self.trading_engine.trade_history
                if trade.exit_price is not None
            ]
            
            # Record any new completed trades
            for trade in completed_trades:
                if not hasattr(trade, '_recorded'):
                    self.performance_tracker.record_trade({
                        'trade_id': trade.trade_id,
                        'symbol': trade.symbol,
                        'entry_date': trade.timestamp,
                        'exit_date': trade.exit_timestamp or datetime.now(),
                        'entry_price': trade.entry_price,
                        'exit_price': trade.exit_price,
                        'quantity': trade.quantity,
                        'ai_signal_score': trade.signal.final_score,
                        'ai_confidence': trade.signal.ai_confidence,
                        'market_regime': trade.signal.market_regime,
                        'stop_loss_triggered': 'stop' in trade.trade_id.lower(),
                        'max_favorable_excursion': 0.0,  # Would be calculated
                        'max_adverse_excursion': 0.0     # Would be calculated
                    })
                    
                    # Mark as recorded
                    trade._recorded = True
                    
                    # Record outcome for safety controls
                    self.safety_controls.record_trade_outcome(trade.symbol, trade.pnl)
            
        except Exception as e:
            logger.error(f"Error updating performance metrics: {e}")
    
    def get_system_status(self) -> Dict:
        """Get comprehensive system status"""
        try:
            return {
                'system_enabled': self.system_enabled,
                'monitoring_active': self.monitoring_active,
                'auto_trading_enabled': self.config['auto_trading_enabled'],
                'paper_trading_mode': self.config['paper_trading_mode'],
                'last_health_check': self.last_health_check.isoformat() if self.last_health_check else None,
                'trading_engine_status': self.trading_engine.get_status(),
                'safety_status': self.safety_controls.get_safety_status(),
                'performance_summary': self.performance_tracker.get_summary_stats(),
                'ai_model_effectiveness': self.performance_tracker.get_ai_model_effectiveness(),
                'configuration': self.config
            }
        except Exception as e:
            logger.error(f"Error getting system status: {e}")
            return {'error': str(e)}
    
    def update_configuration(self, new_config: Dict):
        """Update system configuration"""
        try:
            self.config.update(new_config)
            
            # Update trading engine config
            if any(key in new_config for key in ['auto_trading_enabled', 'paper_trading_mode', 
                                                'min_signal_score', 'max_daily_trades']):
                self.trading_engine.set_config({
                    'enabled': self.config['auto_trading_enabled'],
                    'mode': TradingMode.PAPER if self.config['paper_trading_mode'] else TradingMode.LIVE,
                    'min_signal_score': self.config['min_signal_score'],
                    'max_daily_trades': self.config['max_daily_trades']
                })
            
            logger.info(f"System configuration updated: {new_config}")
            
        except Exception as e:
            logger.error(f"Error updating configuration: {e}")
    
    async def emergency_shutdown(self, reason: str):
        """Emergency shutdown of the entire system"""
        try:
            logger.critical(f"EMERGENCY SHUTDOWN INITIATED: {reason}")
            
            # Stop all trading
            self.trading_engine.emergency_stop_all()
            
            # Trigger safety controls
            self.safety_controls.emergency_shutdown(reason)
            
            # Stop system
            await self.stop_system()
            
        except Exception as e:
            logger.error(f"Error during emergency shutdown: {e}")
    
    def export_comprehensive_report(self, filepath: str):
        """Export comprehensive system report"""
        try:
            report = {
                'generated_at': datetime.now().isoformat(),
                'system_status': self.get_system_status(),
                'health_check': asyncio.run(self.perform_health_check()),
                'performance_metrics': self.performance_tracker.calculate_performance_metrics(),
                'ai_effectiveness': self.performance_tracker.get_ai_model_effectiveness(),
                'safety_alerts': self.safety_controls.get_recent_alerts(hours=24)
            }
            
            import json
            with open(filepath, 'w') as f:
                json.dump(report, f, indent=2, default=str)
            
            logger.info(f"Comprehensive report exported to {filepath}")
            
        except Exception as e:
            logger.error(f"Error exporting comprehensive report: {e}")
