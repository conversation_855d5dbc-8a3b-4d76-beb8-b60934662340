"""
Automated Target-Based Execution Engine
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
import logging
import asyncio
from datetime import datetime, timed<PERSON><PERSON>
from dataclasses import dataclass
from enum import Enum

from .profit_target_config import ProfitTarget, TargetStatus, ProfitTargetManager
from .ai_target_strategy import AITargetAchievementStrategy, TargetOptimizedTrade
from .automated_trading_engine import AutomatedTradingEngine
from .safety_controls import TradingSafetyControls
from ..ai.signal_enhancer import AIEnhancedSignal

logger = logging.getLogger(__name__)

class ExecutionMode(Enum):
    NORMAL = "normal"
    CONSERVATIVE = "conservative"
    AGGRESSIVE = "aggressive"
    PROFIT_PROTECTION = "profit_protection"
    HALTED = "halted"

@dataclass
class TargetExecutionState:
    """Current execution state for a target"""
    target_id: str
    mode: ExecutionMode
    trades_today: int
    current_progress: float
    risk_budget_used: float
    last_trade_time: Optional[datetime]
    consecutive_losses: int
    protection_mode_triggered: bool

class TargetBasedExecutor:
    """Automated execution engine optimized for profit target achievement"""
    
    def __init__(self, data_manager, ai_enhancer, trading_engine: AutomatedTradingEngine):
        self.data_manager = data_manager
        self.ai_enhancer = ai_enhancer
        self.trading_engine = trading_engine
        
        # Target management
        self.target_manager = ProfitTargetManager()
        self.ai_strategy = AITargetAchievementStrategy(data_manager, ai_enhancer.risk_engine)
        
        # Execution state tracking
        self.execution_states = {}  # target_id -> TargetExecutionState
        
        # Configuration
        self.config = {
            'enabled': False,
            'max_trades_per_target_per_day': 20,
            'conservative_threshold': 0.8,  # Switch to conservative at 80% of target
            'protection_threshold': 0.9,    # Switch to protection at 90% of target
            'max_consecutive_losses': 3,
            'min_time_between_trades_minutes': 15,
            'risk_budget_per_target': 0.05,  # 5% of account per target
        }
        
        # Performance tracking
        self.target_trades = []
        self.achievement_stats = {}
        
        logger.info("Target-Based Executor initialized")
    
    async def process_signal_for_targets(self, signal: AIEnhancedSignal,
                                       market_data: Dict) -> List[Dict]:
        """
        Process a signal against all active targets
        
        Args:
            signal: Enhanced TTM Squeeze signal
            market_data: Current market data
            
        Returns:
            List of execution results for each target
        """
        try:
            if not self.config['enabled']:
                return []
            
            results = []
            active_targets = self.target_manager.get_active_targets()
            
            for target in active_targets:
                try:
                    result = await self._process_signal_for_target(
                        signal, target, market_data
                    )
                    if result:
                        results.append(result)
                        
                except Exception as e:
                    logger.error(f"Error processing signal for target {target.target_id}: {e}")
                    continue
            
            return results
            
        except Exception as e:
            logger.error(f"Error processing signal for targets: {e}")
            return []
    
    async def _process_signal_for_target(self, signal: AIEnhancedSignal,
                                       target: ProfitTarget,
                                       market_data: Dict) -> Optional[Dict]:
        """Process signal for specific target"""
        try:
            # Get or create execution state
            exec_state = self._get_execution_state(target.target_id)
            
            # Check if execution is allowed
            if not self._is_execution_allowed(target, exec_state):
                return None
            
            # Calculate current progress
            current_progress = self.target_manager.calculate_target_progress(
                target.target_id,
                self._get_current_account_value(),
                self._get_session_pnl()
            )
            
            # Update execution mode based on progress
            self._update_execution_mode(target, exec_state, current_progress)
            
            # Check if target is already achieved
            if current_progress.get('is_achieved', False):
                await self._handle_target_achievement(target, current_progress)
                return None
            
            # Optimize signal for target
            optimized_trade = self.ai_strategy.optimize_for_target(
                signal, target, current_progress, market_data
            )
            
            # Apply execution mode adjustments
            optimized_trade = self._apply_execution_mode_adjustments(
                optimized_trade, exec_state, current_progress
            )
            
            # Final execution checks
            if not self._final_execution_checks(optimized_trade, target, exec_state):
                return None
            
            # Execute trade
            execution_result = await self._execute_target_trade(
                optimized_trade, target, exec_state
            )
            
            # Update state
            self._update_execution_state(target.target_id, execution_result)
            
            return {
                'target_id': target.target_id,
                'target_name': target.name,
                'symbol': signal.original_signal.symbol,
                'execution_mode': exec_state.mode.value,
                'optimized_trade': optimized_trade,
                'execution_result': execution_result,
                'current_progress': current_progress
            }
            
        except Exception as e:
            logger.error(f"Error processing signal for target {target.target_id}: {e}")
            return None
    
    def _get_execution_state(self, target_id: str) -> TargetExecutionState:
        """Get or create execution state for target"""
        if target_id not in self.execution_states:
            self.execution_states[target_id] = TargetExecutionState(
                target_id=target_id,
                mode=ExecutionMode.NORMAL,
                trades_today=0,
                current_progress=0.0,
                risk_budget_used=0.0,
                last_trade_time=None,
                consecutive_losses=0,
                protection_mode_triggered=False
            )
        
        return self.execution_states[target_id]
    
    def _is_execution_allowed(self, target: ProfitTarget, 
                            exec_state: TargetExecutionState) -> bool:
        """Check if execution is allowed for target"""
        
        # Check if target is active
        if target.status != TargetStatus.ACTIVE:
            return False
        
        # Check if execution is halted
        if exec_state.mode == ExecutionMode.HALTED:
            return False
        
        # Check daily trade limit
        if exec_state.trades_today >= self.config['max_trades_per_target_per_day']:
            logger.debug(f"Daily trade limit reached for target {target.target_id}")
            return False
        
        # Check time between trades
        if exec_state.last_trade_time:
            time_since_last = datetime.now() - exec_state.last_trade_time
            min_interval = timedelta(minutes=self.config['min_time_between_trades_minutes'])
            if time_since_last < min_interval:
                return False
        
        # Check consecutive losses
        if exec_state.consecutive_losses >= self.config['max_consecutive_losses']:
            logger.debug(f"Too many consecutive losses for target {target.target_id}")
            return False
        
        # Check risk budget
        if exec_state.risk_budget_used >= self.config['risk_budget_per_target']:
            logger.debug(f"Risk budget exhausted for target {target.target_id}")
            return False
        
        # Check target trading hours
        if target.trading_start_time and target.trading_end_time:
            current_time = datetime.now().time()
            if not (target.trading_start_time <= current_time <= target.trading_end_time):
                return False
        
        return True
    
    def _update_execution_mode(self, target: ProfitTarget,
                             exec_state: TargetExecutionState,
                             current_progress: Dict):
        """Update execution mode based on progress"""
        try:
            completion_pct = current_progress.get('completion_percentage', 0) / 100
            
            # Check for profit protection mode
            if completion_pct >= self.config['protection_threshold']:
                if not exec_state.protection_mode_triggered:
                    exec_state.mode = ExecutionMode.PROFIT_PROTECTION
                    exec_state.protection_mode_triggered = True
                    logger.info(f"Profit protection mode activated for target {target.target_id}")
            
            # Check for conservative mode
            elif completion_pct >= self.config['conservative_threshold']:
                if exec_state.mode == ExecutionMode.NORMAL:
                    exec_state.mode = ExecutionMode.CONSERVATIVE
                    logger.info(f"Conservative mode activated for target {target.target_id}")
            
            # Check for aggressive mode (if behind schedule)
            elif self._is_behind_schedule(target, current_progress):
                if exec_state.mode == ExecutionMode.NORMAL:
                    exec_state.mode = ExecutionMode.AGGRESSIVE
                    logger.info(f"Aggressive mode activated for target {target.target_id}")
            
            # Update progress
            exec_state.current_progress = completion_pct
            
        except Exception as e:
            logger.error(f"Error updating execution mode: {e}")
    
    def _is_behind_schedule(self, target: ProfitTarget, current_progress: Dict) -> bool:
        """Check if target is behind schedule"""
        try:
            completion_pct = current_progress.get('completion_percentage', 0)
            time_remaining = current_progress.get('time_remaining')
            
            if not time_remaining or not isinstance(time_remaining, timedelta):
                return False
            
            # Calculate expected progress based on time elapsed
            if target.timeframe.value == 'daily':
                total_time = timedelta(hours=6.5)  # Trading day
            elif target.timeframe.value == 'weekly':
                total_time = timedelta(days=5)  # Trading week
            elif target.timeframe.value == 'monthly':
                total_time = timedelta(days=22)  # Trading month
            else:
                return False
            
            elapsed_time = total_time - time_remaining
            expected_progress = (elapsed_time.total_seconds() / total_time.total_seconds()) * 100
            
            # Behind if actual progress is 20% less than expected
            return completion_pct < (expected_progress - 20)
            
        except Exception as e:
            logger.warning(f"Error checking schedule: {e}")
            return False
    
    def _apply_execution_mode_adjustments(self, optimized_trade: TargetOptimizedTrade,
                                        exec_state: TargetExecutionState,
                                        current_progress: Dict) -> TargetOptimizedTrade:
        """Apply execution mode specific adjustments"""
        try:
            if exec_state.mode == ExecutionMode.CONSERVATIVE:
                # Reduce position size and increase selectivity
                optimized_trade.optimized_position_size *= 0.7
                optimized_trade.priority_score *= 0.8  # Be more selective
                
            elif exec_state.mode == ExecutionMode.AGGRESSIVE:
                # Increase position size and reduce selectivity
                optimized_trade.optimized_position_size *= 1.3
                optimized_trade.priority_score *= 1.1  # Be less selective
                
            elif exec_state.mode == ExecutionMode.PROFIT_PROTECTION:
                # Very conservative - only take highest quality signals
                optimized_trade.optimized_position_size *= 0.5
                if optimized_trade.signal.final_score < 90:  # Only 90%+ signals
                    optimized_trade.priority_score = 0  # Reject
                
            # Ensure position size stays within bounds
            target = self.target_manager.get_target_by_id(exec_state.target_id)
            if target:
                optimized_trade.optimized_position_size = max(
                    target.min_trade_size_pct,
                    min(target.max_trade_size_pct, optimized_trade.optimized_position_size)
                )
            
            return optimized_trade
            
        except Exception as e:
            logger.error(f"Error applying execution mode adjustments: {e}")
            return optimized_trade
    
    def _final_execution_checks(self, optimized_trade: TargetOptimizedTrade,
                              target: ProfitTarget,
                              exec_state: TargetExecutionState) -> bool:
        """Final checks before execution"""
        
        # Check minimum priority score
        min_priority = {
            ExecutionMode.NORMAL: 70,
            ExecutionMode.CONSERVATIVE: 80,
            ExecutionMode.AGGRESSIVE: 60,
            ExecutionMode.PROFIT_PROTECTION: 90,
            ExecutionMode.HALTED: 100  # Never execute
        }
        
        required_priority = min_priority.get(exec_state.mode, 70)
        if optimized_trade.priority_score < required_priority:
            return False
        
        # Check if within execution window
        now = datetime.now()
        if not (optimized_trade.execution_window[0] <= now <= optimized_trade.execution_window[1]):
            return False
        
        # Check risk limits
        position_risk = optimized_trade.optimized_position_size * optimized_trade.signal.max_risk_per_trade
        if exec_state.risk_budget_used + position_risk > self.config['risk_budget_per_target']:
            return False
        
        return True
    
    async def _execute_target_trade(self, optimized_trade: TargetOptimizedTrade,
                                  target: ProfitTarget,
                                  exec_state: TargetExecutionState) -> Dict:
        """Execute the target-optimized trade"""
        try:
            # Create modified signal with optimized position size
            modified_signal = optimized_trade.signal
            modified_signal.suggested_position_size = optimized_trade.optimized_position_size
            
            # Execute through trading engine
            trade_result = await self.trading_engine.process_signal(modified_signal)
            
            if trade_result:
                # Record target-specific trade data
                target_trade_data = {
                    'target_id': target.target_id,
                    'trade_id': trade_result.trade_id,
                    'symbol': optimized_trade.symbol,
                    'execution_mode': exec_state.mode.value,
                    'optimized_size': optimized_trade.optimized_position_size,
                    'expected_contribution': optimized_trade.target_contribution,
                    'priority_score': optimized_trade.priority_score,
                    'timestamp': datetime.now()
                }
                
                self.target_trades.append(target_trade_data)
                
                logger.info(f"Target trade executed: {optimized_trade.symbol} for target {target.name}")
                
                return {
                    'success': True,
                    'trade_result': trade_result,
                    'target_data': target_trade_data
                }
            else:
                return {
                    'success': False,
                    'reason': 'Trading engine rejected trade'
                }
                
        except Exception as e:
            logger.error(f"Error executing target trade: {e}")
            return {
                'success': False,
                'reason': str(e)
            }
    
    def _update_execution_state(self, target_id: str, execution_result: Dict):
        """Update execution state after trade"""
        try:
            exec_state = self.execution_states.get(target_id)
            if not exec_state:
                return
            
            if execution_result.get('success'):
                exec_state.trades_today += 1
                exec_state.last_trade_time = datetime.now()
                
                # Update risk budget
                trade_data = execution_result.get('target_data', {})
                position_size = trade_data.get('optimized_size', 0)
                risk_per_trade = 0.02  # Estimate 2% risk per trade
                exec_state.risk_budget_used += position_size * risk_per_trade
                
                # Reset consecutive losses on successful execution
                exec_state.consecutive_losses = 0
            else:
                # Increment consecutive losses on failed execution
                exec_state.consecutive_losses += 1
                
        except Exception as e:
            logger.error(f"Error updating execution state: {e}")
    
    async def _handle_target_achievement(self, target: ProfitTarget, 
                                       current_progress: Dict):
        """Handle target achievement"""
        try:
            # Calculate achievement metrics
            trades_count = len([t for t in self.target_trades if t['target_id'] == target.target_id])
            
            # Calculate win rate (simplified)
            win_rate = 0.65  # Would calculate from actual trade results
            
            # Calculate max drawdown (simplified)
            max_drawdown = 0.03  # Would calculate from actual performance
            
            # Calculate AI efficiency score
            ai_score = self._calculate_ai_efficiency_score(target.target_id)
            
            # Mark target as achieved
            final_value = current_progress.get('current_progress', 0)
            success = self.target_manager.mark_target_achieved(
                target.target_id, final_value, trades_count, win_rate, max_drawdown, ai_score
            )
            
            if success:
                # Halt trading for this target
                exec_state = self._get_execution_state(target.target_id)
                exec_state.mode = ExecutionMode.HALTED
                
                # Record achievement stats
                self.achievement_stats[target.target_id] = {
                    'achieved_at': datetime.now(),
                    'trades_count': trades_count,
                    'final_value': final_value,
                    'ai_efficiency': ai_score
                }
                
                logger.info(f"Target achieved: {target.name} - ${final_value:.2f}")
                
                # Auto-halt if configured
                if target.halt_on_achievement:
                    await self._halt_target_trading(target.target_id)
            
        except Exception as e:
            logger.error(f"Error handling target achievement: {e}")
    
    def _calculate_ai_efficiency_score(self, target_id: str) -> float:
        """Calculate AI efficiency score for target"""
        try:
            target_trades = [t for t in self.target_trades if t['target_id'] == target_id]
            
            if not target_trades:
                return 50.0
            
            # Calculate average priority score
            avg_priority = sum(t.get('priority_score', 50) for t in target_trades) / len(target_trades)
            
            # Calculate execution efficiency (trades vs target achievement)
            efficiency = max(0, 100 - len(target_trades) * 2)  # Penalty for more trades
            
            # Combine scores
            ai_score = (avg_priority * 0.6 + efficiency * 0.4)
            
            return min(100, max(0, ai_score))
            
        except Exception as e:
            logger.warning(f"Error calculating AI efficiency score: {e}")
            return 50.0
    
    async def _halt_target_trading(self, target_id: str):
        """Halt trading for specific target"""
        try:
            exec_state = self._get_execution_state(target_id)
            exec_state.mode = ExecutionMode.HALTED
            
            logger.info(f"Trading halted for target {target_id}")
            
        except Exception as e:
            logger.error(f"Error halting target trading: {e}")
    
    def _get_current_account_value(self) -> float:
        """Get current account value"""
        try:
            account = self.trading_engine.trader.get_account()
            if account:
                return float(account.portfolio_value)
            return 100000.0  # Default fallback
        except Exception:
            return 100000.0
    
    def _get_session_pnl(self) -> float:
        """Get current session P&L"""
        try:
            account = self.trading_engine.trader.get_account()
            if account:
                return float(account.portfolio_value) - float(account.last_equity)
            return 0.0
        except Exception:
            return 0.0
    
    async def monitor_targets(self):
        """Monitor all active targets"""
        try:
            active_targets = self.target_manager.get_active_targets()
            
            for target in active_targets:
                # Calculate progress
                current_progress = self.target_manager.calculate_target_progress(
                    target.target_id,
                    self._get_current_account_value(),
                    self._get_session_pnl()
                )
                
                # Check for achievement
                if current_progress.get('is_achieved', False):
                    await self._handle_target_achievement(target, current_progress)
                
                # Update execution state
                exec_state = self._get_execution_state(target.target_id)
                self._update_execution_mode(target, exec_state, current_progress)
            
            # Cleanup expired targets
            self.target_manager.cleanup_expired_targets()
            
        except Exception as e:
            logger.error(f"Error monitoring targets: {e}")
    
    def reset_daily_counters(self):
        """Reset daily counters for all targets"""
        try:
            for exec_state in self.execution_states.values():
                exec_state.trades_today = 0
                exec_state.risk_budget_used = 0.0
                
                # Reset consecutive losses if it's a new day
                if exec_state.consecutive_losses < self.config['max_consecutive_losses']:
                    exec_state.consecutive_losses = 0
            
            logger.info("Daily counters reset for all targets")
            
        except Exception as e:
            logger.error(f"Error resetting daily counters: {e}")
    
    def get_target_execution_status(self) -> Dict:
        """Get execution status for all targets"""
        try:
            status = {}
            
            for target_id, exec_state in self.execution_states.items():
                target = self.target_manager.get_target_by_id(target_id)
                if target:
                    current_progress = self.target_manager.calculate_target_progress(
                        target_id,
                        self._get_current_account_value(),
                        self._get_session_pnl()
                    )
                    
                    status[target_id] = {
                        'target_name': target.name,
                        'execution_mode': exec_state.mode.value,
                        'trades_today': exec_state.trades_today,
                        'current_progress': current_progress,
                        'risk_budget_used': exec_state.risk_budget_used,
                        'consecutive_losses': exec_state.consecutive_losses,
                        'protection_mode': exec_state.protection_mode_triggered
                    }
            
            return status
            
        except Exception as e:
            logger.error(f"Error getting target execution status: {e}")
            return {}
    
    def get_target_performance_summary(self) -> Dict:
        """Get performance summary for target-based trading"""
        try:
            total_targets = len(self.target_manager.active_targets)
            achieved_targets = len(self.achievement_stats)
            total_trades = len(self.target_trades)
            
            # Calculate average trades per target
            avg_trades_per_target = total_trades / max(total_targets, 1)
            
            # Calculate achievement rate
            achievement_rate = achieved_targets / max(total_targets, 1) * 100
            
            return {
                'total_targets': total_targets,
                'achieved_targets': achieved_targets,
                'achievement_rate': achievement_rate,
                'total_target_trades': total_trades,
                'avg_trades_per_target': avg_trades_per_target,
                'execution_modes_active': {
                    mode.value: len([s for s in self.execution_states.values() if s.mode == mode])
                    for mode in ExecutionMode
                },
                'recent_achievements': list(self.achievement_stats.values())[-5:]
            }
            
        except Exception as e:
            logger.error(f"Error getting performance summary: {e}")
            return {}
    
    def update_config(self, new_config: Dict):
        """Update executor configuration"""
        try:
            self.config.update(new_config)
            logger.info(f"Target executor config updated: {new_config}")
        except Exception as e:
            logger.error(f"Error updating config: {e}")
    
    def emergency_halt_all_targets(self):
        """Emergency halt all target trading"""
        try:
            for exec_state in self.execution_states.values():
                exec_state.mode = ExecutionMode.HALTED
            
            logger.critical("EMERGENCY HALT: All target trading stopped")
            
        except Exception as e:
            logger.error(f"Error during emergency halt: {e}")
