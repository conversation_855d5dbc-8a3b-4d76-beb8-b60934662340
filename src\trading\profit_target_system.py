"""
Comprehensive AI-Powered Profit Target Trading System
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional
import logging
import asyncio
from datetime import datetime, timedelta

from .profit_target_config import ProfitTargetManager, ProfitTarget, TargetStatus
from .ai_target_strategy import AITargetAchievementStrategy
from .target_based_executor import TargetBasedExecutor
from .target_safety_controls import TargetSpecificSafetyControls
from .automated_trading_engine import AutomatedTradingEngine
from .safety_controls import TradingSafetyControls
from ..ai.signal_enhancer import AISignalEnhancer, AIEnhancedSignal
from ..ai.reinforcement_learning_optimizer import ReinforcementLearningOptimizer

logger = logging.getLogger(__name__)

class ProfitTargetTradingSystem:
    """
    Comprehensive AI-Powered Profit Target Trading System
    
    Integrates all profit target components:
    - Target Configuration & Management
    - AI-Enhanced Strategy Optimization
    - Automated Execution with Progressive Risk Management
    - Reinforcement Learning Optimization
    - Target-Specific Safety Controls
    - Real-time Performance Tracking
    """
    
    def __init__(self, data_manager, ai_enhancer: AISignalEnhancer, 
                 trading_engine: AutomatedTradingEngine,
                 base_safety_controls: TradingSafetyControls):
        
        self.data_manager = data_manager
        self.ai_enhancer = ai_enhancer
        self.trading_engine = trading_engine
        
        # Initialize core components
        self.target_manager = ProfitTargetManager()
        self.ai_strategy = AITargetAchievementStrategy(data_manager, ai_enhancer.risk_engine)
        self.executor = TargetBasedExecutor(data_manager, ai_enhancer, trading_engine)
        self.safety_controls = TargetSpecificSafetyControls(base_safety_controls)
        self.rl_optimizer = ReinforcementLearningOptimizer()
        
        # System configuration
        self.config = {
            'enabled': False,
            'ai_optimization_enabled': True,
            'rl_learning_enabled': True,
            'auto_target_creation': False,
            'max_concurrent_targets': 5,
            'default_target_timeframe': 'daily',
            'monitoring_interval_seconds': 15,
            'performance_update_interval_minutes': 5
        }
        
        # System state
        self.system_active = False
        self.monitoring_task = None
        self.last_performance_update = None
        
        # Performance tracking
        self.session_stats = {
            'targets_created': 0,
            'targets_achieved': 0,
            'total_trades': 0,
            'total_pnl': 0.0,
            'ai_decisions': 0,
            'rl_optimizations': 0
        }
        
        logger.info("Profit Target Trading System initialized")
    
    async def start_system(self):
        """Start the complete profit target trading system"""
        try:
            logger.info("Starting Profit Target Trading System...")
            
            # Validate system readiness
            if not await self._validate_system_readiness():
                logger.error("System readiness validation failed")
                return False
            
            # Configure components
            self.executor.update_config({'enabled': self.config['enabled']})
            self.rl_optimizer.set_training_mode(self.config['rl_learning_enabled'])
            
            # Start monitoring
            self.system_active = True
            self.monitoring_task = asyncio.create_task(self._monitoring_loop())
            
            logger.info("Profit Target Trading System started successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error starting profit target system: {e}")
            return False
    
    async def stop_system(self):
        """Stop the profit target trading system"""
        try:
            logger.info("Stopping Profit Target Trading System...")
            
            # Stop monitoring
            self.system_active = False
            if self.monitoring_task:
                self.monitoring_task.cancel()
                try:
                    await self.monitoring_task
                except asyncio.CancelledError:
                    pass
            
            # Emergency halt all targets
            self.executor.emergency_halt_all_targets()
            
            # Save RL model
            if self.config['rl_learning_enabled']:
                self.rl_optimizer._save_model()
            
            logger.info("Profit Target Trading System stopped")
            
        except Exception as e:
            logger.error(f"Error stopping profit target system: {e}")
    
    async def process_ttm_squeeze_signal(self, signal, market_data: Dict) -> Dict:
        """
        Process TTM Squeeze signal through profit target system
        
        Args:
            signal: Original TTM Squeeze signal
            market_data: Current market data
            
        Returns:
            Processing result with target-optimized execution
        """
        try:
            if not self.system_active or not self.config['enabled']:
                return {'action': 'system_disabled'}
            
            # Enhance signal with AI
            enhanced_signal = await self.ai_enhancer.enhance_signal(signal, market_data)
            
            # Get active targets
            active_targets = self.target_manager.get_active_targets()
            if not active_targets:
                return {'action': 'no_active_targets'}
            
            results = []
            
            for target in active_targets:
                try:
                    result = await self._process_signal_for_target(
                        enhanced_signal, target, market_data
                    )
                    if result:
                        results.append(result)
                        
                except Exception as e:
                    logger.error(f"Error processing signal for target {target.target_id}: {e}")
                    continue
            
            # Update session stats
            if results:
                self.session_stats['ai_decisions'] += len(results)
                executed_trades = sum(1 for r in results if r.get('executed', False))
                self.session_stats['total_trades'] += executed_trades
            
            return {
                'action': 'processed',
                'symbol': signal.symbol,
                'targets_processed': len(results),
                'results': results,
                'enhanced_signal': enhanced_signal
            }
            
        except Exception as e:
            logger.error(f"Error processing TTM signal in target system: {e}")
            return {'action': 'error', 'error': str(e)}
    
    async def _process_signal_for_target(self, enhanced_signal: AIEnhancedSignal,
                                       target: ProfitTarget, market_data: Dict) -> Optional[Dict]:
        """Process signal for specific target with full AI optimization"""
        try:
            # Calculate current progress
            current_progress = self.target_manager.calculate_target_progress(
                target.target_id,
                self._get_current_account_value(),
                self._get_session_pnl()
            )
            
            # Safety checks
            trading_allowed, safety_reason = self.safety_controls.check_target_trading_allowed(
                target, current_progress, market_data
            )
            
            if not trading_allowed:
                return {
                    'target_id': target.target_id,
                    'action': 'blocked',
                    'reason': safety_reason,
                    'executed': False
                }
            
            # AI strategy optimization
            optimized_trade = self.ai_strategy.optimize_for_target(
                enhanced_signal, target, current_progress, market_data
            )
            
            # Reinforcement learning optimization
            if self.config['rl_learning_enabled']:
                rl_params = self.rl_optimizer.optimize_trading_decision(
                    self._create_rl_state(current_progress, market_data),
                    {'target_id': target.target_id, **current_progress}
                )
                
                # Apply RL optimizations
                optimized_trade = self._apply_rl_optimizations(optimized_trade, rl_params)
                self.session_stats['rl_optimizations'] += 1
            
            # Execute through target-based executor
            execution_results = await self.executor._process_signal_for_target(
                enhanced_signal, target, market_data
            )
            
            # Update RL if trade was executed
            if (execution_results and execution_results.get('execution_result', {}).get('success') 
                and self.config['rl_learning_enabled']):
                
                # Create new state after trade
                new_progress = self.target_manager.calculate_target_progress(
                    target.target_id,
                    self._get_current_account_value(),
                    self._get_session_pnl()
                )
                
                new_rl_state = self._create_rl_state(new_progress, market_data)
                
                # Update RL optimizer
                self.rl_optimizer.update_from_trade_result(
                    execution_results['execution_result'],
                    new_rl_state,
                    {'target_id': target.target_id, **new_progress}
                )
            
            return {
                'target_id': target.target_id,
                'target_name': target.name,
                'action': 'processed',
                'optimized_trade': optimized_trade,
                'execution_result': execution_results,
                'current_progress': current_progress,
                'executed': execution_results is not None
            }
            
        except Exception as e:
            logger.error(f"Error processing signal for target {target.target_id}: {e}")
            return None
    
    async def _monitoring_loop(self):
        """Main monitoring loop for the profit target system"""
        try:
            while self.system_active:
                # Monitor targets
                await self._monitor_targets()
                
                # Update performance metrics
                if self._should_update_performance():
                    await self._update_performance_metrics()
                
                # Check system health
                await self._check_system_health()
                
                # Sleep until next cycle
                await asyncio.sleep(self.config['monitoring_interval_seconds'])
                
        except asyncio.CancelledError:
            logger.info("Monitoring loop cancelled")
        except Exception as e:
            logger.error(f"Error in monitoring loop: {e}")
    
    async def _monitor_targets(self):
        """Monitor all active targets"""
        try:
            active_targets = self.target_manager.get_active_targets()
            
            for target in active_targets:
                # Calculate progress
                current_progress = self.target_manager.calculate_target_progress(
                    target.target_id,
                    self._get_current_account_value(),
                    self._get_session_pnl()
                )
                
                # Check for achievement
                if current_progress.get('is_achieved', False):
                    await self._handle_target_achievement(target, current_progress)
                
                # Update risk assessment
                market_data = await self._get_current_market_data()
                self.safety_controls.assess_target_risk(
                    target, current_progress, market_data, []
                )
            
            # Cleanup expired targets
            self.target_manager.cleanup_expired_targets()
            
        except Exception as e:
            logger.error(f"Error monitoring targets: {e}")
    
    async def _handle_target_achievement(self, target: ProfitTarget, current_progress: Dict):
        """Handle target achievement"""
        try:
            # Get target trades for metrics
            target_trades = [t for t in self.executor.target_trades 
                           if t['target_id'] == target.target_id]
            
            # Calculate achievement metrics
            trades_count = len(target_trades)
            win_rate = 0.65  # Would calculate from actual results
            max_drawdown = 0.03  # Would calculate from actual performance
            ai_score = self._calculate_target_ai_score(target.target_id)
            
            # Mark as achieved
            final_value = current_progress.get('current_progress', 0)
            success = self.target_manager.mark_target_achieved(
                target.target_id, final_value, trades_count, win_rate, max_drawdown, ai_score
            )
            
            if success:
                self.session_stats['targets_achieved'] += 1
                logger.info(f"Target achieved: {target.name} - ${final_value:.2f}")
                
                # Auto-create new target if enabled
                if self.config['auto_target_creation']:
                    await self._auto_create_follow_up_target(target, final_value)
            
        except Exception as e:
            logger.error(f"Error handling target achievement: {e}")
    
    def _create_rl_state(self, current_progress: Dict, market_data: Dict) -> Dict:
        """Create RL state representation"""
        return {
            'completion_percentage': current_progress.get('completion_percentage', 0),
            'time_remaining': current_progress.get('time_remaining'),
            'regime': market_data.get('regime', 'unknown'),
            'volatility_percentile': market_data.get('volatility_percentile', 50),
            'signal_strength': market_data.get('signal_strength', 50),
            'recent_win_rate': 0.6,  # Would calculate from recent performance
            'consecutive_losses': 0,  # Would track actual losses
            'risk_budget_used': 0.3,  # Would calculate from actual usage
            'portfolio_correlation': 0.2,  # Would calculate actual correlation
            'market_breadth': market_data.get('market_breadth', 50)
        }
    
    def _apply_rl_optimizations(self, optimized_trade, rl_params: Dict):
        """Apply reinforcement learning optimizations to trade"""
        try:
            # Adjust position size
            optimized_trade.optimized_position_size *= rl_params.get('position_size_multiplier', 1.0)
            
            # Adjust priority based on signal threshold
            signal_threshold = rl_params.get('signal_threshold', 80)
            if optimized_trade.signal.final_score < signal_threshold:
                optimized_trade.priority_score *= 0.5  # Reduce priority
            
            # Adjust risk tolerance
            risk_multiplier = rl_params.get('risk_tolerance', 1.0)
            optimized_trade.risk_score /= risk_multiplier
            
            # Adjust timing based on urgency
            urgency = rl_params.get('time_urgency', 1.0)
            if urgency > 1.5:
                # Shorten execution window for urgent trades
                start_time, end_time = optimized_trade.execution_window
                duration = end_time - start_time
                new_duration = duration / urgency
                optimized_trade.execution_window = (start_time, start_time + new_duration)
            
            return optimized_trade
            
        except Exception as e:
            logger.error(f"Error applying RL optimizations: {e}")
            return optimized_trade
    
    async def _validate_system_readiness(self) -> bool:
        """Validate system readiness before starting"""
        try:
            # Check data connectivity
            test_data = self.data_manager.get_historical_data('SPY', '1Day', periods=1)
            if test_data is None or len(test_data) == 0:
                logger.error("Data connectivity check failed")
                return False
            
            # Check trading engine
            if not self.trading_engine:
                logger.error("Trading engine not available")
                return False
            
            # Check AI enhancer
            if not self.ai_enhancer:
                logger.error("AI enhancer not available")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"System readiness validation failed: {e}")
            return False
    
    def _should_update_performance(self) -> bool:
        """Check if performance metrics should be updated"""
        if not self.last_performance_update:
            return True
        
        elapsed = datetime.now() - self.last_performance_update
        return elapsed >= timedelta(minutes=self.config['performance_update_interval_minutes'])
    
    async def _update_performance_metrics(self):
        """Update performance metrics"""
        try:
            # Update AI strategy performance
            # This would use actual trade results
            
            # Update RL optimizer performance
            if self.config['rl_learning_enabled']:
                # Periodic replay training
                self.rl_optimizer.agent.replay_training()
            
            self.last_performance_update = datetime.now()
            
        except Exception as e:
            logger.error(f"Error updating performance metrics: {e}")
    
    async def _check_system_health(self):
        """Check overall system health"""
        try:
            # Check for critical alerts
            safety_summary = self.safety_controls.get_all_target_safety_summary()
            critical_alerts = safety_summary.get('critical_alerts', 0)
            
            if critical_alerts > 0:
                logger.warning(f"System has {critical_alerts} critical safety alerts")
            
            # Check RL performance
            if self.config['rl_learning_enabled']:
                rl_stats = self.rl_optimizer.get_performance_metrics()
                if rl_stats.get('epsilon', 1.0) < 0.05:
                    logger.info("RL agent has converged (low exploration)")
            
        except Exception as e:
            logger.error(f"Error checking system health: {e}")
    
    async def _get_current_market_data(self) -> Dict:
        """Get current market data"""
        try:
            # This would fetch real market data
            return {
                'regime': 'trending',
                'volatility_percentile': 45,
                'vix': 22.5,
                'market_breadth': 55,
                'sector_rotation_intensity': 0.3
            }
        except Exception:
            return {}
    
    def _get_current_account_value(self) -> float:
        """Get current account value"""
        try:
            account = self.trading_engine.trader.get_account()
            if account:
                return float(account.portfolio_value)
            return 100000.0
        except Exception:
            return 100000.0
    
    def _get_session_pnl(self) -> float:
        """Get current session P&L"""
        try:
            account = self.trading_engine.trader.get_account()
            if account:
                return float(account.portfolio_value) - float(account.last_equity)
            return 0.0
        except Exception:
            return 0.0
    
    def _calculate_target_ai_score(self, target_id: str) -> float:
        """Calculate AI efficiency score for target"""
        try:
            # This would calculate based on actual AI performance
            return 75.0  # Placeholder
        except Exception:
            return 50.0
    
    async def _auto_create_follow_up_target(self, completed_target: ProfitTarget, achieved_value: float):
        """Auto-create follow-up target after achievement"""
        try:
            if len(self.target_manager.get_active_targets()) >= self.config['max_concurrent_targets']:
                return
            
            # Create similar target with adjusted parameters
            new_config = {
                'name': f"Follow-up to {completed_target.name}",
                'target_type': completed_target.target_type.value,
                'target_value': completed_target.target_value,
                'timeframe': completed_target.timeframe.value,
                'max_drawdown_pct': completed_target.max_drawdown_pct,
                'max_loss_limit': completed_target.max_loss_limit
            }
            
            new_target_id = self.target_manager.create_target(new_config)
            self.session_stats['targets_created'] += 1
            
            logger.info(f"Auto-created follow-up target: {new_target_id}")
            
        except Exception as e:
            logger.error(f"Error auto-creating follow-up target: {e}")
    
    # Public API methods
    
    def create_target(self, target_config: Dict) -> str:
        """Create new profit target"""
        try:
            target_id = self.target_manager.create_target(target_config)
            self.session_stats['targets_created'] += 1
            return target_id
        except Exception as e:
            logger.error(f"Error creating target: {e}")
            raise
    
    def get_system_status(self) -> Dict:
        """Get comprehensive system status"""
        try:
            target_summary = self.target_manager.get_target_summary()
            executor_status = self.executor.get_target_execution_status()
            safety_summary = self.safety_controls.get_all_target_safety_summary()
            rl_stats = self.rl_optimizer.get_performance_metrics() if self.config['rl_learning_enabled'] else {}
            
            return {
                'system_active': self.system_active,
                'config': self.config,
                'session_stats': self.session_stats,
                'target_summary': target_summary,
                'executor_status': executor_status,
                'safety_summary': safety_summary,
                'rl_performance': rl_stats,
                'last_update': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error getting system status: {e}")
            return {'error': str(e)}
    
    def update_config(self, new_config: Dict):
        """Update system configuration"""
        try:
            self.config.update(new_config)
            
            # Propagate config changes
            if 'enabled' in new_config:
                self.executor.update_config({'enabled': new_config['enabled']})
            
            if 'rl_learning_enabled' in new_config:
                self.rl_optimizer.set_training_mode(new_config['rl_learning_enabled'])
            
            logger.info(f"Profit target system config updated: {new_config}")
            
        except Exception as e:
            logger.error(f"Error updating config: {e}")
    
    async def emergency_halt_all(self, reason: str):
        """Emergency halt all profit target trading"""
        try:
            logger.critical(f"EMERGENCY HALT ALL TARGETS: {reason}")
            
            # Halt executor
            self.executor.emergency_halt_all_targets()
            
            # Halt all targets in safety controls
            for target_id in self.target_manager.active_targets.keys():
                self.safety_controls.emergency_halt_target(target_id, reason)
            
            # Disable system
            self.config['enabled'] = False
            
        except Exception as e:
            logger.error(f"Error during emergency halt: {e}")
