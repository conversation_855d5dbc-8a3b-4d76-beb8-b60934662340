#!/usr/bin/env python3
"""
Simple Python test to verify environment
"""
import sys
import os

print("Python version:", sys.version)
print("Python executable:", sys.executable)
print("Current working directory:", os.getcwd())
print("Python path:", sys.path[:3])  # First 3 entries

# Test basic imports
try:
    import pandas
    print("✓ pandas imported successfully")
except ImportError as e:
    print("✗ pandas import failed:", e)

try:
    import flask
    print("✓ flask imported successfully")
except ImportError as e:
    print("✗ flask import failed:", e)

try:
    import numpy
    print("✓ numpy imported successfully")
except ImportError as e:
    print("✗ numpy import failed:", e)

print("Python environment test completed.")
