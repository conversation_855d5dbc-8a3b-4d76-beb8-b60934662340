"""
TTM Squeeze Algorithm Implementation
"""
import pandas as pd
import numpy as np
from typing import Dict, Tuple, Optional
import logging
from dataclasses import dataclass

from .technical_indicators import TechnicalIndicators
from config import Config

logger = logging.getLogger(__name__)

@dataclass
class SqueezeSignal:
    """Data class for TTM Squeeze signals"""
    timestamp: pd.Timestamp
    symbol: str
    timeframe: str
    is_squeeze: bool
    momentum: float
    momentum_color: str  # 'red', 'yellow', 'green'
    bb_upper: float
    bb_lower: float
    kc_upper: float
    kc_lower: float
    signal_strength: float
    entry_signal: bool

class TTMSqueeze:
    """TTM Squeeze indicator implementation"""
    
    def __init__(self, config: Dict = None):
        """Initialize TTM Squeeze with configuration"""
        self.config = config or Config.TTM_SQUEEZE_CONFIG
        self.bb_period = self.config['bb_period']
        self.bb_std_dev = self.config['bb_std_dev']
        self.kc_period = self.config['kc_period']
        self.kc_atr_multiplier = self.config['kc_atr_multiplier']
        self.momentum_period = self.config['momentum_period']
        
    def calculate_squeeze(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate TTM Squeeze indicators for given data
        
        Args:
            data: DataFrame with OHLCV columns
            
        Returns:
            DataFrame with squeeze indicators added
        """
        try:
            df = data.copy()
            
            # Calculate Bollinger Bands
            bb_upper, bb_middle, bb_lower = TechnicalIndicators.bollinger_bands(
                df['close'], self.bb_period, self.bb_std_dev
            )
            
            # Calculate Keltner Channels
            kc_upper, kc_middle, kc_lower = TechnicalIndicators.keltner_channels(
                df['high'], df['low'], df['close'], 
                self.kc_period, self.kc_atr_multiplier
            )
            
            # Calculate momentum histogram
            momentum = TechnicalIndicators.momentum_histogram(
                df['high'], df['low'], df['close'], self.momentum_period
            )
            
            # Add indicators to dataframe
            df['bb_upper'] = bb_upper
            df['bb_middle'] = bb_middle
            df['bb_lower'] = bb_lower
            df['kc_upper'] = kc_upper
            df['kc_middle'] = kc_middle
            df['kc_lower'] = kc_lower
            df['momentum'] = momentum
            
            # Determine squeeze condition
            df['is_squeeze'] = (bb_lower > kc_lower) & (bb_upper < kc_upper)
            
            # Calculate momentum color
            df['momentum_color'] = self._calculate_momentum_color(momentum)
            
            # Calculate signal strength
            df['signal_strength'] = self._calculate_signal_strength(df)
            
            # Identify entry signals
            df['entry_signal'] = self._identify_entry_signals(df)
            
            return df
            
        except Exception as e:
            logger.error(f"Error calculating TTM Squeeze: {e}")
            raise
    
    def _calculate_momentum_color(self, momentum: pd.Series) -> pd.Series:
        """
        Calculate momentum histogram color based on momentum values
        """
        colors = pd.Series(index=momentum.index, dtype='object')
        
        # Initialize with neutral
        colors[:] = 'yellow'
        
        # Calculate momentum changes
        momentum_diff = momentum.diff()
        momentum_prev = momentum.shift(1)
        
        # Red: decreasing momentum (negative slope)
        red_condition = (momentum < momentum_prev) & (momentum < 0)
        colors[red_condition] = 'red'
        
        # Green: increasing momentum (positive slope)
        green_condition = (momentum > momentum_prev) & (momentum > 0)
        colors[green_condition] = 'green'
        
        # Yellow: transitional states
        yellow_condition = (
            ((momentum > momentum_prev) & (momentum < 0)) |  # Improving but still negative
            ((momentum < momentum_prev) & (momentum > 0)) |  # Declining but still positive
            (abs(momentum_diff) < 0.001)  # Very small changes
        )
        colors[yellow_condition] = 'yellow'
        
        return colors
    
    def _calculate_signal_strength(self, df: pd.DataFrame) -> pd.Series:
        """
        Calculate signal strength based on multiple factors
        """


        # Base strength from squeeze condition (30% weight)
        squeeze_strength = df['is_squeeze'].astype(float) * 0.3

        # Momentum strength (30% weight) - improved calculation
        momentum_abs = abs(df['momentum'])
        # Use rolling standard deviation for better normalization
        momentum_std = df['momentum'].rolling(20, min_periods=5).std()
        momentum_normalized = (momentum_abs / (momentum_std * 2)).fillna(0).clip(0, 1)
        momentum_strength = momentum_normalized * 0.3

        # Band compression strength (40% weight) - improved calculation
        bb_width = df['bb_upper'] - df['bb_lower']
        kc_width = df['kc_upper'] - df['kc_lower']

        # Avoid division by zero
        compression_ratio = np.where(bb_width > 0, kc_width / bb_width, 1.0)

        # Higher compression ratio means tighter squeeze (stronger signal)
        # When KC is inside BB (compression_ratio < 1), we have a squeeze
        compression_strength = np.where(
            compression_ratio < 1.0,
            (1 - compression_ratio) * 0.4,  # Squeeze condition
            0.0  # No squeeze
        )
        compression_strength = pd.Series(compression_strength, index=df.index).clip(0, 0.4)

        # Volume confirmation (bonus up to 10%)
        volume_strength = 0.0
        if 'volume' in df.columns:
            volume_avg = df['volume'].rolling(20, min_periods=5).mean()
            volume_ratio = (df['volume'] / volume_avg).fillna(1.0)
            volume_strength = np.where(volume_ratio > 1.2, 0.1, 0.0)  # 20% above average
            volume_strength = pd.Series(volume_strength, index=df.index)

        # Combine all factors
        total_strength = squeeze_strength + momentum_strength + compression_strength + volume_strength

        # Add debugging for signal strength calculation
        latest_idx = df.index[-1] if len(df) > 0 else None
        if latest_idx is not None:
            logger.debug(f"Signal strength components for latest bar:")
            logger.debug(f"  Squeeze: {squeeze_strength.iloc[-1]:.3f}")
            logger.debug(f"  Momentum: {momentum_strength.iloc[-1]:.3f}")
            logger.debug(f"  Compression: {compression_strength.iloc[-1]:.3f}")
            logger.debug(f"  Volume: {volume_strength.iloc[-1] if hasattr(volume_strength, 'iloc') else volume_strength:.3f}")
            logger.debug(f"  Total: {total_strength.iloc[-1]:.3f}")

        return total_strength.clip(0, 1)
    
    def _identify_entry_signals(self, df: pd.DataFrame) -> pd.Series:
        """
        Identify entry signals based on TTM Squeeze rules
        """
        entry_signals = pd.Series(index=df.index, dtype=bool)
        entry_signals[:] = False
        
        # Look for squeeze release with momentum confirmation
        squeeze_release = df['is_squeeze'].shift(1) & ~df['is_squeeze']
        
        # First yellow bar after red decline
        momentum_color = df['momentum_color']
        momentum_color_prev = momentum_color.shift(1)
        
        # Check for red to yellow transition
        red_to_yellow = (momentum_color_prev == 'red') & (momentum_color == 'yellow')
        
        # Check for improving momentum
        momentum_improving = df['momentum'] > df['momentum'].shift(1)
        
        # Combine conditions for entry signal
        entry_condition = (
            squeeze_release |  # Squeeze release
            (red_to_yellow & momentum_improving)  # Red to yellow with improving momentum
        )
        
        entry_signals[entry_condition] = True
        
        return entry_signals
    
    def get_current_signal(self, data: pd.DataFrame, symbol: str, timeframe: str) -> Optional[SqueezeSignal]:
        """
        Get current TTM Squeeze signal for a symbol
        """
        try:
            if len(data) < max(self.bb_period, self.kc_period, self.momentum_period):
                return None
            
            df = self.calculate_squeeze(data)
            latest = df.iloc[-1]
            
            signal = SqueezeSignal(
                timestamp=latest.name,
                symbol=symbol,
                timeframe=timeframe,
                is_squeeze=latest['is_squeeze'],
                momentum=latest['momentum'],
                momentum_color=latest['momentum_color'],
                bb_upper=latest['bb_upper'],
                bb_lower=latest['bb_lower'],
                kc_upper=latest['kc_upper'],
                kc_lower=latest['kc_lower'],
                signal_strength=latest['signal_strength'],
                entry_signal=latest['entry_signal']
            )
            
            return signal
            
        except Exception as e:
            logger.error(f"Error getting current signal for {symbol}: {e}")
            return None
    
    def analyze_historical_signals(self, data: pd.DataFrame, symbol: str) -> pd.DataFrame:
        """
        Analyze historical TTM Squeeze signals for backtesting
        """
        try:
            df = self.calculate_squeeze(data)
            
            # Add additional analysis columns
            df['squeeze_duration'] = self._calculate_squeeze_duration(df['is_squeeze'])
            df['momentum_trend'] = self._calculate_momentum_trend(df['momentum'])
            df['signal_quality'] = self._assess_signal_quality(df)
            
            return df
            
        except Exception as e:
            logger.error(f"Error analyzing historical signals for {symbol}: {e}")
            raise
    
    def _calculate_squeeze_duration(self, is_squeeze: pd.Series) -> pd.Series:
        """Calculate how long the current squeeze has been active"""
        duration = pd.Series(index=is_squeeze.index, dtype=int)
        duration[:] = 0
        
        current_duration = 0
        for i in range(len(is_squeeze)):
            if is_squeeze.iloc[i]:
                current_duration += 1
            else:
                current_duration = 0
            duration.iloc[i] = current_duration
        
        return duration
    
    def _calculate_momentum_trend(self, momentum: pd.Series) -> pd.Series:
        """Calculate momentum trend direction"""
        trend = pd.Series(index=momentum.index, dtype='object')
        
        momentum_sma = momentum.rolling(5).mean()
        momentum_diff = momentum_sma.diff()
        
        trend[momentum_diff > 0] = 'up'
        trend[momentum_diff < 0] = 'down'
        trend[abs(momentum_diff) <= 0.001] = 'sideways'
        
        return trend
    
    def _assess_signal_quality(self, df: pd.DataFrame) -> pd.Series:
        """Assess the quality of TTM Squeeze signals"""
        quality = pd.Series(index=df.index, dtype='object')
        
        # High quality: Strong momentum with squeeze release
        high_quality = (
            df['entry_signal'] & 
            (df['signal_strength'] > 0.7) &
            (abs(df['momentum']) > df['momentum'].rolling(20).std())
        )
        
        # Medium quality: Moderate signals
        medium_quality = (
            df['entry_signal'] & 
            (df['signal_strength'] > 0.4) &
            ~high_quality
        )
        
        # Low quality: Weak signals
        low_quality = df['entry_signal'] & ~high_quality & ~medium_quality
        
        quality[high_quality] = 'high'
        quality[medium_quality] = 'medium'
        quality[low_quality] = 'low'
        quality[~df['entry_signal']] = 'none'
        
        return quality
