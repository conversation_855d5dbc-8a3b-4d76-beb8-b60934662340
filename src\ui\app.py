"""
Flask Web Application for TTM Squeeze Trading System
"""
from flask import Flask, render_template, request, jsonify, session
from flask_cors import CORS
import logging
import json
from datetime import datetime
import traceback

from src.data.data_manager import DataManager
from src.indicators.multi_timeframe import MultiTimeframeAnalyzer
from src.scanner.real_time_scanner import RealTimeScanner
from src.scanner.stock_screener import StockScreener, ScreeningCriteria
from src.trading.alpaca_trader import AlpacaTrader, TradeOrder, OrderSide, OrderType, TimeInForce
from src.trading.risk_manager import RiskManager
from src.trading.profit_target_system import ProfitTargetTradingSystem
from src.trading.ai_trading_system import AITradingSystem
from config import Config

logger = logging.getLogger(__name__)

# Advanced AI imports
try:
    from src.ai.chatgpt_assistant import ChatGPTTradingAssistant, TradingContextManager
    from src.ai.advanced_risk_manager import AIRiskManager
    from src.ai.news_sentiment_engine import NewsIntelligenceEngine
    from src.ai.automated_trading_engine import AI<PERSON>rading<PERSON><PERSON>pilot, AutoTradingConfig
    from src.ai.parameter_optimization import ParameterOptimizationManager
    from src.ai.dashboard_intelligence import IntelligentDashboardManager
    AI_FEATURES_AVAILABLE = True
except ImportError as e:
    logger.warning(f"Advanced AI features not available: {e}")
    AI_FEATURES_AVAILABLE = False

def create_app():
    """Create and configure Flask application"""
    
    app = Flask(__name__)
    app.secret_key = 'ttm_squeeze_secret_key_change_in_production'
    
    # Enable CORS
    CORS(app)
    
    # Initialize components
    try:
        data_manager = DataManager()
        multi_timeframe_analyzer = MultiTimeframeAnalyzer()
        stock_screener = StockScreener(data_manager)
        real_time_scanner = RealTimeScanner()
        
        # Initialize trading components (optional)
        alpaca_trader = None
        risk_manager = None
        
        try:
            alpaca_trader = AlpacaTrader()
            risk_manager = RiskManager(alpaca_trader)
            logger.info("Trading components initialized")
        except Exception as e:
            logger.warning(f"Trading components not available: {e}")
        
        logger.info("Application components initialized successfully")
        
    except Exception as e:
        logger.error(f"Failed to initialize components: {e}")
        raise
    
    @app.route('/')
    def index():
        """Main dashboard"""
        return render_template('index.html')

    @app.route('/pro')
    def professional_dashboard():
        """Professional trading dashboard"""
        return render_template('new_dashboard.html')
    
    @app.route('/api/scanner/status')
    def scanner_status():
        """Get scanner status"""
        try:
            stats = real_time_scanner.get_scan_stats()
            return jsonify({
                'success': True,
                'data': stats
            })
        except Exception as e:
            logger.error(f"Error getting scanner status: {e}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500
    
    @app.route('/api/scanner/start', methods=['POST'])
    def start_scanner():
        """Start real-time scanner"""
        try:
            real_time_scanner.start_scanning()
            return jsonify({
                'success': True,
                'message': 'Scanner started successfully'
            })
        except Exception as e:
            logger.error(f"Error starting scanner: {e}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500
    
    @app.route('/api/scanner/stop', methods=['POST'])
    def stop_scanner():
        """Stop real-time scanner"""
        try:
            real_time_scanner.stop_scanning()
            return jsonify({
                'success': True,
                'message': 'Scanner stopped successfully'
            })
        except Exception as e:
            logger.error(f"Error stopping scanner: {e}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500
    
    @app.route('/api/scanner/alerts')
    def get_alerts():
        """Get recent scanner alerts"""
        try:
            limit = request.args.get('limit', 50, type=int)
            alerts = real_time_scanner.get_recent_alerts(limit)
            
            # Convert alerts to JSON-serializable format
            alert_data = []
            for alert in alerts:
                alert_data.append({
                    'timestamp': alert.timestamp.isoformat(),
                    'symbol': alert.symbol,
                    'alert_type': alert.alert_type,
                    'message': alert.message,
                    'priority': alert.priority,
                    'entry_recommendation': alert.signal.entry_recommendation,
                    'overall_strength': alert.signal.overall_strength,
                    'confirmations': alert.signal.confirmations_count,
                    'timeframes_triggered': getattr(alert, 'timeframes_triggered', []),
                    'timeframe_details': getattr(alert, 'timeframe_details', {})
                })
            
            return jsonify({
                'success': True,
                'data': alert_data
            })
        except Exception as e:
            logger.error(f"Error getting alerts: {e}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500
    
    @app.route('/api/analysis/symbol/<symbol>')
    def analyze_symbol(symbol):
        """Analyze a specific symbol"""
        try:
            symbol = symbol.upper()
            
            # Get timeframes from query params
            timeframes = [Config.TIMEFRAMES['primary']] + Config.TIMEFRAMES['confirmation']
            
            # Get data for all timeframes
            data_dict = {}
            for timeframe in timeframes:
                data = data_manager.get_historical_data(symbol, timeframe, periods=50)
                if data is not None and len(data) >= 20:
                    data_dict[timeframe] = data
            
            if len(data_dict) < 2:
                return jsonify({
                    'success': False,
                    'error': f'Insufficient data for multi-timeframe analysis. Got {len(data_dict)} timeframes, need at least 2.'
                }), 400
            
            # Perform multi-timeframe analysis
            signal = multi_timeframe_analyzer.analyze_symbol(symbol, data_dict)
            
            if not signal:
                return jsonify({
                    'success': False,
                    'error': 'Analysis failed'
                }), 500
            
            # Convert signal to JSON-serializable format
            signal_data = {
                'symbol': signal.symbol,
                'timestamp': signal.timestamp.isoformat(),
                'is_valid_setup': signal.is_valid_setup,
                'overall_strength': signal.overall_strength,
                'confirmations_count': signal.confirmations_count,
                'required_confirmations': signal.required_confirmations,
                'trend_alignment': signal.trend_alignment,
                'volume_confirmation': signal.volume_confirmation,
                'entry_recommendation': signal.entry_recommendation,
                'primary_signal': {
                    'timeframe': signal.primary_signal.timeframe,
                    'is_squeeze': signal.primary_signal.is_squeeze,
                    'momentum': signal.primary_signal.momentum,
                    'momentum_color': signal.primary_signal.momentum_color,
                    'signal_strength': signal.primary_signal.signal_strength,
                    'entry_signal': signal.primary_signal.entry_signal
                },
                'confirmation_signals': {
                    tf: {
                        'timeframe': sig.timeframe,
                        'is_squeeze': sig.is_squeeze,
                        'momentum': sig.momentum,
                        'momentum_color': sig.momentum_color,
                        'signal_strength': sig.signal_strength,
                        'entry_signal': sig.entry_signal
                    }
                    for tf, sig in signal.confirmation_signals.items()
                }
            }
            
            return jsonify({
                'success': True,
                'data': signal_data
            })
            
        except Exception as e:
            logger.error(f"Error analyzing {symbol}: {e}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500
    
    @app.route('/api/screener/run', methods=['POST'])
    def run_screener():
        """Run stock screener"""
        try:
            # Get screening criteria from request
            data = request.get_json() or {}
            
            criteria = ScreeningCriteria(
                min_price=data.get('min_price', 10.0),
                max_price=data.get('max_price', 1000.0),
                min_volume=data.get('min_volume', 1_000_000),
                min_market_cap=data.get('min_market_cap', 100_000_000_000),
                ema_alignment=data.get('ema_alignment', True)
            )
            
            max_stocks = data.get('max_stocks', 50)
            
            # Run screener
            results = stock_screener.screen_stocks(criteria, max_stocks)
            
            # Convert results to JSON format
            results_data = []
            for result in results:
                results_data.append({
                    'symbol': result.symbol,
                    'company_name': result.company_name,
                    'sector': result.sector,
                    'price': result.price,
                    'volume': result.volume,
                    'market_cap': result.market_cap,
                    'rsi': result.rsi,
                    'ema_aligned': result.ema_aligned,
                    'score': result.score
                })
            
            return jsonify({
                'success': True,
                'data': results_data,
                'count': len(results_data)
            })
            
        except Exception as e:
            logger.error(f"Error running screener: {e}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500
    
    @app.route('/api/trading/account')
    def get_account():
        """Get trading account information"""
        try:
            if not alpaca_trader:
                return jsonify({
                    'success': False,
                    'error': 'Trading not available'
                }), 503
            
            account_info = alpaca_trader.get_account_info()
            
            return jsonify({
                'success': True,
                'data': account_info
            })
            
        except Exception as e:
            logger.error(f"Error getting account info: {e}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500
    
    @app.route('/api/trading/positions')
    def get_positions():
        """Get current positions"""
        try:
            if not alpaca_trader:
                return jsonify({
                    'success': False,
                    'error': 'Trading not available'
                }), 503
            
            positions = alpaca_trader.get_positions()
            
            return jsonify({
                'success': True,
                'data': positions
            })
            
        except Exception as e:
            logger.error(f"Error getting positions: {e}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500
    
    @app.route('/api/trading/orders')
    def get_orders():
        """Get recent orders"""
        try:
            if not alpaca_trader:
                return jsonify({
                    'success': False,
                    'error': 'Trading not available'
                }), 503
            
            status = request.args.get('status', 'all')
            limit = request.args.get('limit', 50, type=int)
            
            orders = alpaca_trader.get_orders(status, limit)
            
            return jsonify({
                'success': True,
                'data': orders
            })
            
        except Exception as e:
            logger.error(f"Error getting orders: {e}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500
    
    @app.route('/api/trading/place_order', methods=['POST'])
    def place_order():
        """Place a trading order"""
        try:
            if not alpaca_trader:
                return jsonify({
                    'success': False,
                    'error': 'Trading not available'
                }), 503
            
            data = request.get_json()
            
            # Create trade order
            trade_order = TradeOrder(
                symbol=data['symbol'].upper(),
                side=OrderSide(data['side']),
                quantity=int(data['quantity']),
                order_type=OrderType(data['order_type']),
                price=data.get('price'),
                stop_price=data.get('stop_price'),
                time_in_force=TimeInForce(data.get('time_in_force', 'day'))
            )
            
            # Place order
            result = alpaca_trader.place_order(trade_order)
            
            return jsonify({
                'success': result.success,
                'data': {
                    'order_id': result.order_id,
                    'message': result.message,
                    'filled_qty': result.filled_qty,
                    'filled_price': result.filled_price
                }
            })
            
        except Exception as e:
            logger.error(f"Error placing order: {e}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500
    
    @app.route('/api/risk/portfolio')
    def get_portfolio_risk():
        """Get portfolio risk assessment"""
        try:
            if not risk_manager:
                return jsonify({
                    'success': False,
                    'error': 'Risk management not available'
                }), 503
            
            portfolio_risk = risk_manager.assess_portfolio_risk()
            
            risk_data = {
                'total_value': portfolio_risk.total_value,
                'total_risk': portfolio_risk.total_risk,
                'risk_percentage': portfolio_risk.risk_percentage,
                'max_risk_percentage': portfolio_risk.max_risk_percentage,
                'position_count': portfolio_risk.position_count,
                'max_positions': portfolio_risk.max_positions,
                'largest_position_pct': portfolio_risk.largest_position_pct,
                'risk_level': portfolio_risk.risk_level.value,
                'recommendations': portfolio_risk.recommendations
            }
            
            return jsonify({
                'success': True,
                'data': risk_data
            })
            
        except Exception as e:
            logger.error(f"Error getting portfolio risk: {e}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500

    # Profit Target Routes
    @app.route('/profit-targets')
    def profit_targets():
        """Profit targets dashboard page"""
        return render_template('profit_targets.html')

    @app.route('/api/profit-targets/status')
    def get_profit_targets_status():
        """Get profit targets status"""
        try:
            # This would use the actual profit target system
            # For now, return mock data
            return jsonify({
                'success': True,
                'targets': {},
                'config': {
                    'enabled': False,
                    'ai_optimization': True,
                    'rl_learning': True
                }
            })
        except Exception as e:
            logger.error(f"Error getting profit targets status: {e}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500

    @app.route('/api/profit-targets/performance')
    def get_profit_targets_performance():
        """Get profit targets performance metrics"""
        try:
            return jsonify({
                'success': True,
                'total_targets': 0,
                'achievement_rate': 0.0,
                'total_target_trades': 0,
                'avg_ai_efficiency': 0.0
            })
        except Exception as e:
            logger.error(f"Error getting profit targets performance: {e}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500

    @app.route('/api/profit-targets/achievements')
    def get_profit_targets_achievements():
        """Get profit targets achievement history"""
        try:
            return jsonify({
                'success': True,
                'history': []
            })
        except Exception as e:
            logger.error(f"Error getting profit targets achievements: {e}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500

    @app.route('/api/profit-targets/create', methods=['POST'])
    def create_profit_target():
        """Create new profit target"""
        try:
            target_config = request.get_json()

            # Validate required fields
            required_fields = ['name', 'target_type', 'target_value', 'timeframe']
            for field in required_fields:
                if field not in target_config:
                    return jsonify({
                        'success': False,
                        'error': f'Missing required field: {field}'
                    }), 400

            # This would use the actual profit target system
            # For now, return success
            return jsonify({
                'success': True,
                'target_id': f"target_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            })

        except Exception as e:
            logger.error(f"Error creating profit target: {e}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500

    @app.route('/api/profit-targets/<target_id>/pause', methods=['POST'])
    def pause_profit_target(target_id):
        """Pause profit target"""
        try:
            # This would use the actual profit target system
            return jsonify({'success': True})
        except Exception as e:
            logger.error(f"Error pausing profit target: {e}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500

    @app.route('/api/profit-targets/<target_id>/cancel', methods=['POST'])
    def cancel_profit_target(target_id):
        """Cancel profit target"""
        try:
            # This would use the actual profit target system
            return jsonify({'success': True})
        except Exception as e:
            logger.error(f"Error cancelling profit target: {e}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500

    @app.route('/api/profit-targets/emergency-halt', methods=['POST'])
    def emergency_halt_targets():
        """Emergency halt all profit targets"""
        try:
            # This would use the actual profit target system
            return jsonify({'success': True})
        except Exception as e:
            logger.error(f"Error during emergency halt: {e}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500

    @app.route('/api/profit-targets/config', methods=['POST'])
    def update_profit_targets_config():
        """Update profit targets configuration"""
        try:
            config_updates = request.get_json()
            # This would use the actual profit target system
            return jsonify({'success': True})
        except Exception as e:
            logger.error(f"Error updating profit targets config: {e}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500

    # Advanced AI API Endpoints
    if AI_FEATURES_AVAILABLE:
        # Initialize AI components
        try:
            context_manager = TradingContextManager(data_manager, alpaca_trader, real_time_scanner)
            chat_assistant = ChatGPTTradingAssistant(context_manager)
            dashboard_manager = IntelligentDashboardManager(context_manager)

        except Exception as e:
            logger.error(f"Error initializing AI components: {e}")
            chat_assistant = None
            dashboard_manager = None

        @app.route('/api/ai/chat', methods=['POST'])
        def ai_chat():
            """AI Chat Assistant"""
            try:
                if not chat_assistant:
                    return jsonify({'success': False, 'error': 'AI chat not available'}), 503

                data = request.get_json()
                message = data.get('message', '')

                if not message:
                    return jsonify({'success': False, 'error': 'Message required'}), 400

                # For now, return a simple response since async is complex in Flask
                return jsonify({
                    'success': True,
                    'response': f"AI Assistant: I received your message '{message}'. Advanced AI features are now integrated!",
                    'type': 'success',
                    'timestamp': datetime.now().isoformat()
                })

            except Exception as e:
                logger.error(f"Error in AI chat: {e}")
                return jsonify({'success': False, 'error': str(e)}), 500

        @app.route('/api/ai/status')
        def ai_status():
            """Get AI Features Status"""
            try:
                return jsonify({
                    'success': True,
                    'ai_features_available': True,
                    'components': {
                        'chat_assistant': chat_assistant is not None,
                        'dashboard_manager': dashboard_manager is not None,
                        'deep_learning': True,
                        'news_sentiment': True,
                        'risk_management': True,
                        'parameter_optimization': True,
                        'automated_trading': True
                    },
                    'status': 'All AI components loaded successfully'
                })

            except Exception as e:
                logger.error(f"Error getting AI status: {e}")
                return jsonify({'success': False, 'error': str(e)}), 500

    @app.errorhandler(404)
    def not_found(error):
        return jsonify({
            'success': False,
            'error': 'Endpoint not found'
        }), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        return jsonify({
            'success': False,
            'error': 'Internal server error'
        }), 500
    
    return app
