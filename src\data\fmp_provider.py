"""
Financial Modeling Prep API Provider for TTM Squeeze Trading System
"""
import pandas as pd
import requests
from typing import Optional, Dict, List
import logging
from datetime import datetime, timedelta
import time

from config import Config

logger = logging.getLogger(__name__)

class FMPDataProvider:
    """Financial Modeling Prep API data provider"""
    
    def __init__(self):
        """Initialize FMP API connection"""
        self.api_key = Config.FMP_API_KEY
        self.base_url = "https://financialmodelingprep.com/api/v3"
        self.session = requests.Session()
        
        # Enhanced rate limiting for live data
        self.last_request_time = 0
        self.min_request_interval = 0.2  # 5 requests per second max (more conservative)
        self.request_count = 0
        self.request_window_start = time.time()
        self.max_requests_per_minute = 250  # Conservative limit
        
        # Test connection
        try:
            self._test_connection()
            logger.info("Connected to Financial Modeling Prep API")
        except Exception as e:
            logger.error(f"Failed to connect to FMP API: {e}")
    
    def _test_connection(self):
        """Test API connection"""
        url = f"{self.base_url}/profile/AAPL"
        params = {'apikey': self.api_key}
        
        response = self.session.get(url, params=params, timeout=10)
        response.raise_for_status()
    
    def _rate_limit(self):
        """Enhanced rate limiting with per-minute tracking"""
        current_time = time.time()

        # Reset request count every minute
        if current_time - self.request_window_start > 60:
            self.request_count = 0
            self.request_window_start = current_time

        # Check if we're approaching the per-minute limit
        if self.request_count >= self.max_requests_per_minute:
            sleep_time = 60 - (current_time - self.request_window_start)
            if sleep_time > 0:
                logger.warning(f"Rate limit approaching, sleeping for {sleep_time:.1f}s")
                time.sleep(sleep_time)
                self.request_count = 0
                self.request_window_start = time.time()

        # Standard per-request rate limiting
        time_since_last = current_time - self.last_request_time
        if time_since_last < self.min_request_interval:
            time.sleep(self.min_request_interval - time_since_last)

        self.last_request_time = time.time()
        self.request_count += 1
    
    def _make_request(self, endpoint: str, params: Dict = None, max_retries: int = 3) -> Optional[Dict]:
        """Make API request with rate limiting and error handling"""
        for attempt in range(max_retries):
            try:
                self._rate_limit()

                url = f"{self.base_url}/{endpoint}"
                request_params = {'apikey': self.api_key}

                if params:
                    request_params.update(params)

                response = self.session.get(url, params=request_params, timeout=30)

                # Handle rate limiting specifically
                if response.status_code == 429:
                    wait_time = (2 ** attempt) * 30  # Exponential backoff: 30s, 60s, 120s
                    logger.warning(f"Rate limit hit for {endpoint}, waiting {wait_time}s (attempt {attempt + 1}/{max_retries})")
                    time.sleep(wait_time)
                    continue

                response.raise_for_status()
                return response.json()

            except requests.exceptions.HTTPError as e:
                if e.response.status_code == 429:
                    # Already handled above
                    continue
                else:
                    logger.error(f"HTTP error for {endpoint}: {e}")
                    return None
            except requests.exceptions.RequestException as e:
                logger.error(f"API request failed for {endpoint}: {e}")
                if attempt == max_retries - 1:  # Last attempt
                    return None
                time.sleep(2 ** attempt)  # Brief retry delay
            except Exception as e:
                logger.error(f"Unexpected error in API request: {e}")
                return None

        logger.error(f"Failed to complete request for {endpoint} after {max_retries} attempts")
        return None
    
    def get_sp500_list(self) -> List[str]:
        """Get list of S&P 500 stocks"""
        try:
            data = self._make_request("sp500_constituent")
            
            if data is None:
                logger.error("Failed to get S&P 500 list")
                return []
            
            symbols = [item['symbol'] for item in data if 'symbol' in item]
            logger.info(f"Retrieved {len(symbols)} S&P 500 symbols")
            
            return symbols
            
        except Exception as e:
            logger.error(f"Error getting S&P 500 list: {e}")
            return []
    
    def get_company_profile(self, symbol: str) -> Optional[Dict]:
        """Get company profile information"""
        try:
            data = self._make_request(f"profile/{symbol}")
            
            if data and len(data) > 0:
                return data[0]
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting company profile for {symbol}: {e}")
            return None
    
    def get_market_cap_filter(self, min_market_cap: float) -> List[str]:
        """Get stocks with market cap above threshold"""
        try:
            # Get all available stocks
            data = self._make_request("stock/list")
            
            if data is None:
                return []
            
            filtered_symbols = []
            
            for stock in data:
                symbol = stock.get('symbol', '')
                
                # Skip if no symbol
                if not symbol:
                    continue
                
                # Get market cap
                profile = self.get_company_profile(symbol)
                if profile:
                    market_cap = profile.get('mktCap', 0)
                    if market_cap >= min_market_cap:
                        filtered_symbols.append(symbol)
                
                # Limit to prevent too many API calls
                if len(filtered_symbols) >= 1000:
                    break
            
            logger.info(f"Found {len(filtered_symbols)} stocks with market cap >= ${min_market_cap:,.0f}")
            return filtered_symbols
            
        except Exception as e:
            logger.error(f"Error filtering by market cap: {e}")
            return []
    
    def get_historical_data(self, symbol: str, timeframe: str, 
                          periods: int = 100) -> Optional[pd.DataFrame]:
        """
        Get historical data (limited timeframes available)
        Note: FMP has limited intraday data, mainly daily
        """
        try:
            # FMP uses historical-price-full for all timeframes with timeseries parameter
            if timeframe == '1Day':
                endpoint = f"historical-price-full/{symbol}"
                params = {'timeseries': periods}
            elif timeframe in ['5Min', '15Min', '30Min', '1Hour']:
                # For intraday data, use historical-price-full with timeseries
                timeframe_map = {
                    '5Min': '5',
                    '15Min': '15',
                    '30Min': '30',
                    '1Hour': '60'  # 1 hour = 60 minutes
                }
                endpoint = f"historical-price-full/{symbol}"
                params = {
                    'timeseries': periods,
                    'interval': f"{timeframe_map[timeframe]}min"
                }
            elif timeframe == '1Min':
                # 1Min data
                endpoint = f"historical-price-full/{symbol}"
                params = {
                    'timeseries': periods,
                    'interval': '1min'
                }
            else:
                logger.warning(f"Unsupported timeframe for FMP: {timeframe}")
                return None

            data = self._make_request(endpoint, params)
            
            if data is None:
                return None
            
            # Parse response - FMP returns data in 'historical' key for all timeframes
            if isinstance(data, dict):
                historical_data = data.get('historical', [])
            elif isinstance(data, list):
                historical_data = data
            else:
                logger.warning(f"Unexpected data format from FMP for {symbol}")
                return None
            
            if not historical_data:
                return None
            
            # Convert to DataFrame
            df_data = []
            for item in historical_data[:periods]:
                try:
                    # Handle different date formats (date vs datetime)
                    date_field = item.get('date') or item.get('datetime')
                    if not date_field:
                        continue

                    df_data.append({
                        'timestamp': pd.to_datetime(date_field),
                        'open': float(item['open']),
                        'high': float(item['high']),
                        'low': float(item['low']),
                        'close': float(item['close']),
                        'volume': int(item.get('volume', 0))
                    })
                except (KeyError, ValueError, TypeError) as e:
                    logger.debug(f"Skipping invalid data point for {symbol}: {e}")
                    continue
            
            df = pd.DataFrame(df_data)
            df.set_index('timestamp', inplace=True)
            df.sort_index(inplace=True)
            
            return df
            
        except Exception as e:
            logger.error(f"Error getting historical data for {symbol}: {e}")
            return None
    
    def get_real_time_quote(self, symbol: str) -> Optional[Dict]:
        """Get real-time quote"""
        try:
            data = self._make_request(f"quote-short/{symbol}")
            
            if data and len(data) > 0:
                quote = data[0]
                return {
                    'symbol': symbol,
                    'price': float(quote.get('price', 0)),
                    'volume': int(quote.get('volume', 0)),
                    'timestamp': datetime.now()
                }
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting real-time quote for {symbol}: {e}")
            return None
    
    def get_sector_performance(self) -> Optional[Dict]:
        """Get sector performance data"""
        try:
            data = self._make_request("sector-performance")
            
            if data:
                return {item['sector']: float(item['changesPercentage'].replace('%', '')) 
                       for item in data}
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting sector performance: {e}")
            return None
    
    def get_market_hours(self) -> Optional[Dict]:
        """Get market hours information"""
        try:
            data = self._make_request("market-hours")
            
            if data:
                return data
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting market hours: {e}")
            return None
    
    def search_stocks(self, query: str, limit: int = 10) -> List[Dict]:
        """Search for stocks by name or symbol"""
        try:
            params = {'query': query, 'limit': limit}
            data = self._make_request("search", params)
            
            if data:
                return data
            
            return []
            
        except Exception as e:
            logger.error(f"Error searching stocks: {e}")
            return []
