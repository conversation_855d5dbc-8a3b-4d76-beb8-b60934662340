"""
Stock Screener for TTM Squeeze Trading System
"""
import pandas as pd
from typing import List, Dict, Optional, Tuple
import logging
from datetime import datetime, timedelta
from dataclasses import dataclass

from src.data.data_manager import DataManager
from src.indicators.technical_indicators import TechnicalIndicators
from config import Config

logger = logging.getLogger(__name__)

@dataclass
class ScreeningCriteria:
    """Stock screening criteria"""
    min_price: float = 1.0  # Relaxed from 10.0
    max_price: float = 10000.0  # Increased from 1000.0
    min_volume: int = 100_000  # Relaxed from 1_000_000
    min_market_cap: float = 1_000_000_000  # Relaxed from 100_000_000_000 (1B instead of 100B)
    max_market_cap: float = float('inf')
    min_avg_volume_20d: int = 50_000  # Relaxed from 500_000
    exclude_sectors: List[str] = None
    include_sectors: List[str] = None
    min_rsi: float = 0.0  # Relaxed from 20.0
    max_rsi: float = 100.0  # Relaxed from 80.0
    ema_alignment: bool = False  # Disabled for full universe scan

@dataclass
class FullUniverseScreeningCriteria:
    """Minimal screening criteria for full S&P 500 universe scanning"""
    min_price: float = 0.50  # Very minimal price filter
    max_price: float = 50000.0  # Very high ceiling
    min_volume: int = 10_000  # Very low volume requirement
    min_market_cap: float = 100_000_000  # 100M minimum (much lower than before)
    max_market_cap: float = float('inf')
    min_avg_volume_20d: int = 5_000  # Very low average volume requirement
    exclude_sectors: List[str] = None
    include_sectors: List[str] = None
    min_rsi: float = 0.0  # No RSI filtering
    max_rsi: float = 100.0  # No RSI filtering
    ema_alignment: bool = False  # No EMA alignment requirement

@dataclass
class StockScreenResult:
    """Stock screening result"""
    symbol: str
    company_name: str
    sector: str
    price: float
    volume: int
    market_cap: float
    avg_volume_20d: float
    rsi: float
    ema8: float
    ema21: float
    ema_aligned: bool
    meets_criteria: bool
    score: float

class StockScreener:
    """Stock screener for TTM Squeeze system"""
    
    def __init__(self, data_manager: DataManager):
        """Initialize stock screener"""
        self.data_manager = data_manager
        self.universe_cache = None
        self.universe_cache_time = None
        self.cache_duration = timedelta(hours=24)  # Cache universe for 24 hours
    
    def get_stock_universe(self, force_refresh: bool = False) -> List[str]:
        """
        Get the stock universe with caching
        
        Args:
            force_refresh: Force refresh of cached universe
            
        Returns:
            List of stock symbols
        """
        # Check cache
        if (not force_refresh and 
            self.universe_cache is not None and 
            self.universe_cache_time is not None and
            datetime.now() - self.universe_cache_time < self.cache_duration):
            return self.universe_cache
        
        # Get fresh universe
        logger.info("Refreshing stock universe...")
        universe = self.data_manager.get_stock_universe()
        
        # Apply basic filters
        filtered_universe = self._apply_basic_filters(universe)
        
        # Cache the result
        self.universe_cache = filtered_universe
        self.universe_cache_time = datetime.now()
        
        logger.info(f"Stock universe contains {len(filtered_universe)} symbols")
        return filtered_universe
    
    def _apply_basic_filters(self, symbols: List[str]) -> List[str]:
        """Apply basic filters to stock universe"""
        filtered = []

        for symbol in symbols:
            try:
                # Allow symbols with dots (like BRK.A, BRK.B) and be more permissive
                if len(symbol) < 1 or len(symbol) > 6:
                    continue

                # Skip common ETFs but be more specific
                etf_patterns = ['SPY', 'QQQ', 'IWM', 'VTI', 'VOO', 'XLF', 'XLE', 'XLK', 'XLV', 'XLI']
                if symbol in etf_patterns:
                    continue

                # Skip obvious non-stock symbols
                if any(pattern in symbol for pattern in ['^', '=', '/', '\\']):
                    continue

                filtered.append(symbol)

            except Exception as e:
                logger.debug(f"Error filtering {symbol}: {e}")
                continue

        return filtered
    
    def screen_stocks(self, criteria: ScreeningCriteria = None, 
                     max_stocks: int = 100) -> List[StockScreenResult]:
        """
        Screen stocks based on criteria
        
        Args:
            criteria: Screening criteria
            max_stocks: Maximum number of stocks to return
            
        Returns:
            List of StockScreenResult objects
        """
        if criteria is None:
            criteria = ScreeningCriteria()
        
        universe = self.get_stock_universe()
        results = []
        
        logger.info(f"Screening {len(universe)} stocks...")
        
        # Process stocks in batches to avoid overwhelming APIs
        batch_size = 50
        for i in range(0, len(universe), batch_size):
            batch = universe[i:i + batch_size]
            batch_results = self._screen_batch(batch, criteria)
            results.extend(batch_results)
            
            # Stop if we have enough results
            if len(results) >= max_stocks * 2:  # Get extra for sorting
                break
        
        # Sort by score and return top results
        results.sort(key=lambda x: x.score, reverse=True)
        
        # Filter to only stocks that meet criteria
        filtered_results = [r for r in results if r.meets_criteria]
        
        logger.info(f"Found {len(filtered_results)} stocks meeting criteria")
        return filtered_results[:max_stocks]

    def get_full_universe_symbols(self) -> List[str]:
        """
        Get all S&P 500 symbols with minimal filtering for TTM Squeeze scanning

        Returns:
            List of symbols ready for TTM Squeeze analysis
        """
        # Get the raw universe
        universe = self.get_stock_universe()

        # Apply only basic symbol filtering (no fundamental criteria)
        filtered_symbols = self._apply_basic_filters(universe)

        logger.info(f"Full universe contains {len(filtered_symbols)} symbols after basic filtering")
        return filtered_symbols
    
    def _screen_batch(self, symbols: List[str], criteria: ScreeningCriteria) -> List[StockScreenResult]:
        """Screen a batch of stocks"""
        results = []
        
        # Get data for all symbols in batch
        data_dict = self.data_manager.get_multiple_symbols_data(symbols, '1Day', periods=30)
        
        for symbol in symbols:
            try:
                result = self._screen_single_stock(symbol, data_dict.get(symbol), criteria)
                if result:
                    results.append(result)
            except Exception as e:
                logger.debug(f"Error screening {symbol}: {e}")
                continue
        
        return results
    
    def _screen_single_stock(self, symbol: str, data: Optional[pd.DataFrame], 
                           criteria: ScreeningCriteria) -> Optional[StockScreenResult]:
        """Screen a single stock"""
        
        if data is None or len(data) < 21:  # Need at least 21 days for indicators
            return None
        
        try:
            # Get company info
            company_info = self.data_manager.get_company_info(symbol)
            company_name = company_info.get('companyName', symbol) if company_info else symbol
            sector = company_info.get('sector', 'Unknown') if company_info else 'Unknown'
            market_cap = company_info.get('mktCap', 0) if company_info else 0
            
            # Get current price and volume
            latest = data.iloc[-1]
            price = float(latest['close'])
            volume = int(latest['volume'])
            
            # Calculate technical indicators
            avg_volume_20d = float(data['volume'].tail(20).mean())
            rsi = TechnicalIndicators.rsi(data['close']).iloc[-1]
            ema8 = TechnicalIndicators.ema(data['close'], 8).iloc[-1]
            ema21 = TechnicalIndicators.ema(data['close'], 21).iloc[-1]
            
            # Check EMA alignment
            ema_aligned = ema8 > ema21 if criteria.ema_alignment else True
            
            # Check all criteria
            meets_criteria = self._check_criteria(
                price, volume, market_cap, avg_volume_20d, rsi, 
                ema_aligned, sector, criteria
            )
            
            # Calculate score
            score = self._calculate_score(
                price, volume, market_cap, avg_volume_20d, rsi, ema_aligned
            )
            
            return StockScreenResult(
                symbol=symbol,
                company_name=company_name,
                sector=sector,
                price=price,
                volume=volume,
                market_cap=market_cap,
                avg_volume_20d=avg_volume_20d,
                rsi=rsi,
                ema8=ema8,
                ema21=ema21,
                ema_aligned=ema_aligned,
                meets_criteria=meets_criteria,
                score=score
            )
            
        except Exception as e:
            logger.debug(f"Error processing {symbol}: {e}")
            return None
    
    def _check_criteria(self, price: float, volume: int, market_cap: float,
                       avg_volume_20d: float, rsi: float, ema_aligned: bool,
                       sector: str, criteria: ScreeningCriteria) -> bool:
        """Check if stock meets all criteria"""
        
        # Price criteria
        if not (criteria.min_price <= price <= criteria.max_price):
            return False
        
        # Volume criteria
        if volume < criteria.min_volume:
            return False
        
        if avg_volume_20d < criteria.min_avg_volume_20d:
            return False
        
        # Market cap criteria
        if not (criteria.min_market_cap <= market_cap <= criteria.max_market_cap):
            return False
        
        # RSI criteria
        if not (criteria.min_rsi <= rsi <= criteria.max_rsi):
            return False
        
        # EMA alignment
        if criteria.ema_alignment and not ema_aligned:
            return False
        
        # Sector filters
        if criteria.exclude_sectors and sector in criteria.exclude_sectors:
            return False
        
        if criteria.include_sectors and sector not in criteria.include_sectors:
            return False
        
        return True
    
    def _calculate_score(self, price: float, volume: int, market_cap: float,
                        avg_volume_20d: float, rsi: float, ema_aligned: bool) -> float:
        """Calculate a score for ranking stocks"""
        
        score = 0.0
        
        # Volume score (higher volume = higher score)
        volume_score = min(volume / 10_000_000, 1.0) * 20  # Max 20 points
        score += volume_score
        
        # Market cap score (prefer large caps)
        market_cap_score = min(market_cap / 1_000_000_000_000, 1.0) * 20  # Max 20 points
        score += market_cap_score
        
        # RSI score (prefer moderate RSI)
        rsi_distance_from_50 = abs(rsi - 50)
        rsi_score = (50 - rsi_distance_from_50) / 50 * 20  # Max 20 points
        score += max(rsi_score, 0)
        
        # EMA alignment bonus
        if ema_aligned:
            score += 20
        
        # Price stability (prefer stocks not at extremes)
        if 20 <= price <= 500:
            score += 20
        
        return min(score, 100.0)
    
    def get_sector_breakdown(self, results: List[StockScreenResult]) -> Dict[str, int]:
        """Get sector breakdown of screening results"""
        sector_counts = {}
        
        for result in results:
            sector = result.sector
            sector_counts[sector] = sector_counts.get(sector, 0) + 1
        
        return dict(sorted(sector_counts.items(), key=lambda x: x[1], reverse=True))
    
    def export_results(self, results: List[StockScreenResult], filename: str = None) -> str:
        """Export screening results to CSV"""
        
        if filename is None:
            filename = f"stock_screen_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        
        # Convert to DataFrame
        data = []
        for result in results:
            data.append({
                'Symbol': result.symbol,
                'Company': result.company_name,
                'Sector': result.sector,
                'Price': result.price,
                'Volume': result.volume,
                'Market Cap': result.market_cap,
                'Avg Volume 20D': result.avg_volume_20d,
                'RSI': result.rsi,
                'EMA8': result.ema8,
                'EMA21': result.ema21,
                'EMA Aligned': result.ema_aligned,
                'Meets Criteria': result.meets_criteria,
                'Score': result.score
            })
        
        df = pd.DataFrame(data)
        df.to_csv(filename, index=False)
        
        logger.info(f"Exported {len(results)} results to {filename}")
        return filename
