#!/usr/bin/env python3
"""
Test script to demonstrate AI features in TTM Squeeze Trading System
"""

import requests
import json
import time

BASE_URL = "http://127.0.0.1:5000"

def test_ai_status():
    """Test AI system status"""
    print("🤖 Testing AI System Status...")
    response = requests.get(f"{BASE_URL}/api/ai/status")
    if response.status_code == 200:
        data = response.json()
        print("✅ AI Status:", json.dumps(data, indent=2))
        return True
    else:
        print("❌ AI Status failed:", response.status_code)
        return False

def test_ai_chat():
    """Test AI chat assistant"""
    print("\n💬 Testing AI Chat Assistant...")
    
    messages = [
        "What are the current market conditions?",
        "Analyze the BKNG signal with 4 timeframe confirmations",
        "What's the risk level for trading right now?",
        "Show me the best TTM Squeeze opportunities"
    ]
    
    for message in messages:
        print(f"\n🗣️  User: {message}")
        
        payload = {"message": message}
        response = requests.post(
            f"{BASE_URL}/api/ai/chat",
            headers={"Content-Type": "application/json"},
            data=json.dumps(payload)
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"🤖 AI: {data.get('response', 'No response')}")
        else:
            print(f"❌ Chat failed: {response.status_code}")
        
        time.sleep(1)  # Be nice to the server

def test_scanner_with_ai():
    """Test scanner alerts (enhanced with AI)"""
    print("\n📊 Testing AI-Enhanced Scanner Alerts...")
    
    response = requests.get(f"{BASE_URL}/api/scanner/alerts?limit=10")
    if response.status_code == 200:
        data = response.json()
        alerts = data.get('alerts', [])
        
        print(f"✅ Found {len(alerts)} AI-enhanced alerts:")
        for alert in alerts[:5]:  # Show top 5
            symbol = alert.get('symbol', 'Unknown')
            timeframes = ', '.join(alert.get('timeframes', []))
            signal_type = alert.get('signal_type', 'Unknown')
            
            print(f"  🎯 {symbol}: {signal_type} | Timeframes: {timeframes}")
            
            # Show AI enhancement if available
            if 'ai_score' in alert:
                print(f"     🤖 AI Score: {alert['ai_score']}")
            if 'confidence' in alert:
                print(f"     📈 Confidence: {alert['confidence']}")
    else:
        print(f"❌ Scanner alerts failed: {response.status_code}")

def test_account_info():
    """Test trading account info"""
    print("\n💰 Testing Account Information...")
    
    response = requests.get(f"{BASE_URL}/api/trading/account")
    if response.status_code == 200:
        data = response.json()
        account = data.get('account', {})
        
        print("✅ Account Status:")
        print(f"  💵 Buying Power: ${account.get('buying_power', 'Unknown')}")
        print(f"  📊 Account Status: {account.get('status', 'Unknown')}")
        print(f"  🏦 Account ID: {account.get('account_number', 'Unknown')}")
    else:
        print(f"❌ Account info failed: {response.status_code}")

def main():
    """Run all AI feature tests"""
    print("🚀 TTM Squeeze AI Features Test Suite")
    print("=" * 50)
    
    # Test AI system
    if not test_ai_status():
        print("❌ AI system not available. Make sure the server is running.")
        return
    
    # Test AI features
    test_ai_chat()
    test_scanner_with_ai()
    test_account_info()
    
    print("\n" + "=" * 50)
    print("🎯 AI Feature Testing Complete!")
    print("\n🌐 Access your AI-enhanced dashboard at:")
    print("   http://127.0.0.1:5000/pro")
    print("\n🤖 Available AI Endpoints:")
    print("   • /api/ai/status - AI system status")
    print("   • /api/ai/chat - AI chat assistant")
    print("   • /api/scanner/alerts - AI-enhanced alerts")
    print("   • /api/trading/account - Account information")

if __name__ == "__main__":
    main()
