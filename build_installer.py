#!/usr/bin/env python3
"""
Build script to create standalone executable for TTM Squeeze Trading System
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def install_pyinstaller():
    """Install PyInstaller if not already installed"""
    try:
        import PyInstaller
        print("✓ PyInstaller already installed")
    except ImportError:
        print("Installing PyInstaller...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])

def create_spec_file():
    """Create PyInstaller spec file for the TTM Squeeze system"""
    spec_content = '''
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('src', 'src'),
        ('config.py', '.'),
        ('.env', '.'),
        ('requirements.txt', '.'),
        ('src/ui/templates', 'src/ui/templates'),
        ('src/ui/static', 'src/ui/static'),
    ],
    hiddenimports=[
        'alpaca_trade_api',
        'flask',
        'flask_cors',
        'pandas',
        'numpy',
        'requests',
        'python_dateutil',
        'pytz',
        'plotly',
        'matplotlib',
        'websocket',
        'src.data.alpaca_provider',
        'src.data.fmp_provider',
        'src.data.data_manager',
        'src.indicators.ttm_squeeze',
        'src.indicators.multi_timeframe',
        'src.scanner.real_time_scanner',
        'src.scanner.stock_screener',
        'src.trading.alpaca_trader',
        'src.trading.risk_manager',
        'src.ui.app',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='TTM_Squeeze_Trading_System',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='icon.ico'  # Add an icon file if you have one
)
'''
    
    with open('ttm_squeeze.spec', 'w') as f:
        f.write(spec_content.strip())
    
    print("✓ Created PyInstaller spec file")

def build_executable():
    """Build the standalone executable"""
    print("Building standalone executable...")
    
    # Clean previous builds
    if os.path.exists('dist'):
        shutil.rmtree('dist')
    if os.path.exists('build'):
        shutil.rmtree('build')
    
    # Build with PyInstaller
    subprocess.check_call([
        'pyinstaller',
        '--clean',
        '--noconfirm',
        'ttm_squeeze.spec'
    ])
    
    print("✓ Executable built successfully!")
    print(f"✓ Location: {os.path.abspath('dist/TTM_Squeeze_Trading_System.exe')}")

def create_installer_script():
    """Create NSIS installer script"""
    nsis_script = '''
!define APPNAME "TTM Squeeze Trading System"
!define COMPANYNAME "TTM Trading"
!define DESCRIPTION "Professional TTM Squeeze Trading System"
!define VERSIONMAJOR 1
!define VERSIONMINOR 0
!define VERSIONBUILD 0

!define HELPURL "https://github.com/your-repo/ttm-squeeze"
!define UPDATEURL "https://github.com/your-repo/ttm-squeeze"
!define ABOUTURL "https://github.com/your-repo/ttm-squeeze"

!define INSTALLSIZE 150000  # Size in KB

RequestExecutionLevel admin

InstallDir "$PROGRAMFILES\\${APPNAME}"

Name "${APPNAME}"
Icon "icon.ico"
outFile "TTM_Squeeze_Installer.exe"

!include LogicLib.nsh

page directory
page instfiles

!macro VerifyUserIsAdmin
UserInfo::GetAccountType
pop $0
${If} $0 != "admin"
    messageBox mb_iconstop "Administrator rights required!"
    setErrorLevel 740
    quit
${EndIf}
!macroend

function .onInit
    setShellVarContext all
    !insertmacro VerifyUserIsAdmin
functionEnd

section "install"
    setOutPath $INSTDIR
    
    # Copy files
    file /r "dist\\TTM_Squeeze_Trading_System.exe"
    file ".env.example"
    file "README.md"
    
    # Create uninstaller
    writeUninstaller "$INSTDIR\\uninstall.exe"
    
    # Start menu
    createDirectory "$SMPROGRAMS\\${APPNAME}"
    createShortCut "$SMPROGRAMS\\${APPNAME}\\${APPNAME}.lnk" "$INSTDIR\\TTM_Squeeze_Trading_System.exe"
    createShortCut "$SMPROGRAMS\\${APPNAME}\\Uninstall.lnk" "$INSTDIR\\uninstall.exe"
    
    # Desktop shortcut
    createShortCut "$DESKTOP\\${APPNAME}.lnk" "$INSTDIR\\TTM_Squeeze_Trading_System.exe"
    
    # Registry information for add/remove programs
    writeRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${APPNAME}" "DisplayName" "${APPNAME}"
    writeRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${APPNAME}" "UninstallString" "$INSTDIR\\uninstall.exe"
    writeRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${APPNAME}" "InstallLocation" "$INSTDIR"
    writeRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${APPNAME}" "DisplayIcon" "$INSTDIR\\TTM_Squeeze_Trading_System.exe"
    writeRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${APPNAME}" "Publisher" "${COMPANYNAME}"
    writeRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${APPNAME}" "HelpLink" "${HELPURL}"
    writeRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${APPNAME}" "URLUpdateInfo" "${UPDATEURL}"
    writeRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${APPNAME}" "URLInfoAbout" "${ABOUTURL}"
    writeRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${APPNAME}" "DisplayVersion" "${VERSIONMAJOR}.${VERSIONMINOR}.${VERSIONBUILD}"
    writeRegDWORD HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${APPNAME}" "VersionMajor" ${VERSIONMAJOR}
    writeRegDWORD HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${APPNAME}" "VersionMinor" ${VERSIONMINOR}
    writeRegDWORD HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${APPNAME}" "NoModify" 1
    writeRegDWORD HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${APPNAME}" "NoRepair" 1
    writeRegDWORD HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${APPNAME}" "EstimatedSize" ${INSTALLSIZE}
sectionEnd

section "uninstall"
    delete "$INSTDIR\\TTM_Squeeze_Trading_System.exe"
    delete "$INSTDIR\\uninstall.exe"
    rmDir "$INSTDIR"
    
    delete "$SMPROGRAMS\\${APPNAME}\\${APPNAME}.lnk"
    delete "$SMPROGRAMS\\${APPNAME}\\Uninstall.lnk"
    rmDir "$SMPROGRAMS\\${APPNAME}"
    
    delete "$DESKTOP\\${APPNAME}.lnk"
    
    deleteRegKey HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${APPNAME}"
sectionEnd
'''
    
    with open('installer.nsi', 'w') as f:
        f.write(nsis_script.strip())
    
    print("✓ Created NSIS installer script")
    print("To build installer: Install NSIS and run 'makensis installer.nsi'")

def create_setup_guide():
    """Create setup guide for users"""
    guide = '''
# TTM Squeeze Trading System - Installation Guide

## Option 1: Standalone Executable (Recommended)
1. Download TTM_Squeeze_Trading_System.exe
2. Create a folder (e.g., C:\\TTM_Squeeze)
3. Place the .exe file in the folder
4. Copy your .env file with API keys to the same folder
5. Double-click to run

## Option 2: Full Installer
1. Download TTM_Squeeze_Installer.exe
2. Run as Administrator
3. Follow installation wizard
4. Configure API keys in the installed directory

## API Keys Required
You need to set up these API keys in your .env file:

```
# Alpaca API (for trading and data)
ALPACA_API_KEY=your_alpaca_key
ALPACA_SECRET_KEY=your_alpaca_secret
ALPACA_BASE_URL=https://paper-api.alpaca.markets

# Financial Modeling Prep API (for additional data)
FMP_API_KEY=your_fmp_key
```

## Getting API Keys

### Alpaca (Free Paper Trading)
1. Go to https://alpaca.markets
2. Sign up for free paper trading account
3. Get API keys from dashboard

### Financial Modeling Prep (Free Tier Available)
1. Go to https://financialmodelingprep.com
2. Sign up for free account
3. Get API key from dashboard

## First Run
1. Start the application
2. Open web browser to http://127.0.0.1:5000
3. Click "Start Scanner" to begin scanning
4. Monitor alerts in real-time

## System Requirements
- Windows 10/11 (64-bit)
- Internet connection
- 4GB RAM minimum
- 1GB free disk space
'''
    
    with open('INSTALLATION_GUIDE.md', 'w') as f:
        f.write(guide.strip())
    
    print("✓ Created installation guide")

if __name__ == "__main__":
    print("TTM Squeeze Trading System - Build Script")
    print("=" * 50)
    
    # Install PyInstaller
    install_pyinstaller()
    
    # Create spec file
    create_spec_file()
    
    # Build executable
    build_executable()
    
    # Create installer script
    create_installer_script()
    
    # Create setup guide
    create_setup_guide()
    
    print("\n" + "=" * 50)
    print("Build Complete!")
    print("Files created:")
    print("- dist/TTM_Squeeze_Trading_System.exe (standalone executable)")
    print("- installer.nsi (NSIS installer script)")
    print("- INSTALLATION_GUIDE.md (user guide)")
    print("\nNext steps:")
    print("1. Test the executable")
    print("2. Install NSIS and run 'makensis installer.nsi' to create installer")
    print("3. Distribute the installer or standalone exe")
