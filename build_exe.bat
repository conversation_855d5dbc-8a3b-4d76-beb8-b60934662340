@echo off
echo TTM Squeeze Trading System - Build Executable
echo ================================================

echo Installing PyInstaller...
pip install pyinstaller

echo Creating executable...
pyinstaller --onefile --noconsole --name "TTM_Squeeze_Trading_System" ^
    --add-data "src;src" ^
    --add-data "config.py;." ^
    --add-data ".env;." ^
    --add-data "src/ui/templates;src/ui/templates" ^
    --add-data "src/ui/static;src/ui/static" ^
    --hidden-import "alpaca_trade_api" ^
    --hidden-import "flask" ^
    --hidden-import "pandas" ^
    --hidden-import "numpy" ^
    main.py

echo.
echo Build complete!
echo Executable location: dist\TTM_Squeeze_Trading_System.exe
echo.
echo To distribute:
echo 1. Copy the .exe file
echo 2. Include a .env file with API keys
echo 3. User just needs to run the .exe
echo.
pause
