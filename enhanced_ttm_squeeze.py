"""
Enhanced TTM Squeeze - Hybrid approach combining simplicity with key improvements
"""
import os
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple

import pandas as pd
import numpy as np
from alpaca.trading.client import TradingClient
from alpaca.data.historical import StockHistoricalDataClient
from alpaca.data.requests import StockBarsRequest
from alpaca.data.timeframe import TimeFrame
from fmp_python.fmp import FMP

# Configuration
API_KEY_ALPACA = os.getenv("APCA_API_KEY_ID", "PK2O4NB71EQDMMENX77L")
API_SECRET_ALPACA = os.getenv("APCA_API_SECRET_KEY", "HLv1oA7hH3Yah59LElAayj17pncG5KmMTNKEoW8j")
API_KEY_FMP = os.getenv("FMP_API_KEY", "K63wnAbUDHNPICjzeq3JcEG1vi8Q2oz7")

logging.basicConfig(level=logging.INFO, format="%(asctime)s %(levelname)s %(message)s")
logger = logging.getLogger(__name__)

# API Clients
alpaca_data = StockHistoricalDataClient(API_KEY_ALPACA, API_SECRET_ALPACA)
alpaca_trade = TradingClient(API_KEY_ALPACA, API_SECRET_ALPACA, paper=True)
fmp_client = FMP(API_KEY_FMP)

class EnhancedTTMSqueeze:
    """Enhanced TTM Squeeze with additional filters and risk management"""
    
    def __init__(self):
        self.bb_length = 20
        self.bb_std = 2.0
        self.kc_length = 20
        self.kc_atr_mul = 1.5
        self.mom_length = 20
        
    def calculate_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Calculate all indicators in one pass"""
        df = df.copy()
        
        # Bollinger Bands
        df['bb_mid'] = df['close'].rolling(self.bb_length).mean()
        df['bb_std'] = df['close'].rolling(self.bb_length).std()
        df['bb_upper'] = df['bb_mid'] + self.bb_std * df['bb_std']
        df['bb_lower'] = df['bb_mid'] - self.bb_std * df['bb_std']
        
        # Keltner Channels
        high_low = df['high'] - df['low']
        high_close = np.abs(df['high'] - df['close'].shift())
        low_close = np.abs(df['low'] - df['close'].shift())
        df['tr'] = np.maximum.reduce([high_low, high_close, low_close])
        df['atr'] = df['tr'].rolling(self.kc_length).mean()
        df['kc_mid'] = df['close'].rolling(self.kc_length).mean()
        df['kc_upper'] = df['kc_mid'] + self.kc_atr_mul * df['atr']
        df['kc_lower'] = df['kc_mid'] - self.kc_atr_mul * df['atr']
        
        # Squeeze detection
        df['in_squeeze'] = (
            (df['bb_lower'] > df['kc_lower']) &
            (df['bb_upper'] < df['kc_upper'])
        )
        
        # Enhanced momentum with color coding
        df = self._calculate_enhanced_momentum(df)
        
        # Additional filters
        df = self._calculate_additional_filters(df)
        
        return df
    
    def _calculate_enhanced_momentum(self, df: pd.DataFrame) -> pd.DataFrame:
        """Enhanced momentum calculation with color coding"""
        # Linear regression slope
        x = np.arange(self.mom_length)
        slopes = []
        
        for i in range(self.mom_length - 1, len(df)):
            y = df['close'].iloc[i-self.mom_length+1:i+1].values
            slope = np.polyfit(x, y, 1)[0]
            slopes.append(slope)
        
        df['momentum'] = np.nan
        df['momentum'].iloc[self.mom_length-1:] = slopes
        
        # Momentum color coding
        df['momentum_prev'] = df['momentum'].shift(1)
        df['momentum_color'] = 'yellow'  # default
        
        # Red: decreasing momentum (negative slope)
        red_condition = (df['momentum'] < df['momentum_prev']) & (df['momentum'] < 0)
        df.loc[red_condition, 'momentum_color'] = 'red'
        
        # Green: increasing momentum (positive slope)  
        green_condition = (df['momentum'] > df['momentum_prev']) & (df['momentum'] > 0)
        df.loc[green_condition, 'momentum_color'] = 'green'
        
        # Entry signal: first yellow/green after red sequence
        df['prev_color'] = df['momentum_color'].shift(1)
        df['entry_signal'] = (
            (df['prev_color'] == 'red') & 
            (df['momentum_color'].isin(['yellow', 'green'])) &
            (df['momentum'] > df['momentum_prev'])
        )
        
        return df
    
    def _calculate_additional_filters(self, df: pd.DataFrame) -> pd.DataFrame:
        """Calculate additional confirmation filters"""
        # EMA trend alignment
        df['ema8'] = df['close'].ewm(span=8).mean()
        df['ema21'] = df['close'].ewm(span=21).mean()
        df['trend_bullish'] = df['ema8'] > df['ema21']
        
        # Volume filter
        df['volume_avg'] = df['volume'].rolling(20).mean()
        df['volume_above_avg'] = df['volume'] > (df['volume_avg'] * 0.8)
        
        # MACD confirmation
        ema12 = df['close'].ewm(span=12).mean()
        ema26 = df['close'].ewm(span=26).mean()
        df['macd'] = ema12 - ema26
        df['macd_signal'] = df['macd'].ewm(span=9).mean()
        df['macd_hist'] = df['macd'] - df['macd_signal']
        df['macd_improving'] = df['macd_hist'] > df['macd_hist'].shift(1)
        
        # Price action confirmation
        df['price_rising'] = df['close'] > df['close'].shift(2)
        
        return df
    
    def analyze_symbol_enhanced(self, symbol: str, data_dict: Dict[TimeFrame, pd.DataFrame]) -> Optional[Dict]:
        """Enhanced multi-timeframe analysis with additional filters"""
        results = {}
        
        for tf, df in data_dict.items():
            if len(df) < 30:
                continue
                
            df_analyzed = self.calculate_indicators(df)
            latest = df_analyzed.iloc[-1]
            
            results[tf] = {
                'squeeze': latest['in_squeeze'],
                'momentum': latest['momentum'],
                'momentum_color': latest['momentum_color'],
                'entry_signal': latest['entry_signal'],
                'trend_bullish': latest['trend_bullish'],
                'volume_ok': latest['volume_above_avg'],
                'macd_improving': latest['macd_improving'],
                'price_rising': latest['price_rising']
            }
        
        # Enhanced entry logic
        if len(results) < 2:
            return None
            
        # Count squeeze confirmations
        squeeze_count = sum(1 for r in results.values() if r['squeeze'])
        
        # Check primary timeframe entry signal
        primary_tf = TimeFrame.Minute15
        if primary_tf not in results:
            return None
            
        primary = results[primary_tf]
        
        # Additional filter confirmations
        filter_score = sum([
            primary['trend_bullish'],
            primary['volume_ok'], 
            primary['macd_improving'],
            primary['price_rising']
        ])
        
        # Enhanced entry criteria
        entry_valid = (
            squeeze_count >= 2 and  # At least 2 timeframes in squeeze
            primary['entry_signal'] and  # Primary timeframe entry signal
            filter_score >= 3  # At least 3 out of 4 additional filters
        )
        
        if entry_valid:
            return {
                'symbol': symbol,
                'squeeze_confirmations': squeeze_count,
                'filter_score': filter_score,
                'primary_momentum': primary['momentum'],
                'signal_strength': (squeeze_count + filter_score) / 7.0,  # Normalized score
                'results': results
            }
        
        return None

class EnhancedRiskManager:
    """Enhanced risk management with dynamic position sizing"""
    
    def __init__(self, max_portfolio_risk: float = 0.02):
        self.max_portfolio_risk = max_portfolio_risk
        
    def calculate_position_size(self, account_value: float, entry_price: float, 
                              stop_loss_price: float) -> int:
        """Calculate position size based on risk management"""
        risk_per_share = abs(entry_price - stop_loss_price)
        max_risk_amount = account_value * self.max_portfolio_risk
        
        if risk_per_share <= 0:
            return 0
            
        position_size = int(max_risk_amount / risk_per_share)
        
        # Ensure position doesn't exceed 5% of portfolio
        max_position_value = account_value * 0.05
        max_shares_by_value = int(max_position_value / entry_price)
        
        return min(position_size, max_shares_by_value)

class EnhancedAlpacaTrader:
    """Enhanced trader with better risk management"""
    
    def __init__(self):
        self.client = alpaca_trade
        self.risk_manager = EnhancedRiskManager()
        
    def submit_smart_order(self, symbol: str, signal_data: Dict, 
                          sl_pct: float = 0.02, tp_pct: float = 0.04):
        """Submit order with dynamic position sizing"""
        try:
            # Get account info
            account = self.client.get_account()
            account_value = float(account.portfolio_value)
            
            # Get current price
            quote = self.client.get_latest_quote(symbol)
            entry_price = float(quote.ask_price)
            
            # Calculate stop loss and take profit
            stop_price = entry_price * (1 - sl_pct)
            take_profit_price = entry_price * (1 + tp_pct)
            
            # Calculate position size
            qty = self.risk_manager.calculate_position_size(
                account_value, entry_price, stop_price
            )
            
            if qty <= 0:
                logger.warning(f"Position size too small for {symbol}")
                return None
            
            # Submit bracket order
            order = self.client.submit_order(
                symbol=symbol,
                qty=qty,
                side='buy',
                type='market',
                time_in_force='gtc',
                order_class='bracket',
                take_profit={'limit_price': round(take_profit_price, 2)},
                stop_loss={'stop_price': round(stop_price, 2)}
            )
            
            logger.info(f"Order submitted for {symbol}: {qty} shares at ~${entry_price:.2f}")
            logger.info(f"Signal strength: {signal_data.get('signal_strength', 0):.2f}")
            
            return order
            
        except Exception as e:
            logger.error(f"Error submitting order for {symbol}: {e}")
            return None

# Enhanced scanner
async def fetch_bars_enhanced(symbol: str, timeframe: TimeFrame, limit: int = 100):
    """Fetch bars with error handling"""
    try:
        req = StockBarsRequest(
            symbol_or_symbols=symbol,
            timeframe=timeframe,
            start=datetime.now() - timedelta(days=5),  # More data for indicators
            limit=limit,
        )
        bars = alpaca_data.get_stock_bars(req)
        df = bars.df.reset_index().rename(columns={'timestamp': 'datetime'})
        return df
    except Exception as e:
        logger.error(f"Error fetching data for {symbol}: {e}")
        return None

async def analyze_symbol_enhanced(symbol: str):
    """Enhanced symbol analysis"""
    timeframes = [TimeFrame.Minute15, TimeFrame.Minute5, TimeFrame.Minute30]
    data_dict = {}
    
    for tf in timeframes:
        df = await fetch_bars_enhanced(symbol, tf)
        if df is not None and len(df) >= 30:
            data_dict[tf] = df
    
    if len(data_dict) < 2:
        return None
    
    analyzer = EnhancedTTMSqueeze()
    result = analyzer.analyze_symbol_enhanced(symbol, data_dict)
    
    return result

async def run_enhanced_scanner(universe: List[str], max_concurrent: int = 10):
    """Enhanced scanner with concurrency control"""
    semaphore = asyncio.Semaphore(max_concurrent)
    
    async def analyze_with_semaphore(symbol):
        async with semaphore:
            return await analyze_symbol_enhanced(symbol)
    
    tasks = [analyze_with_semaphore(sym) for sym in universe]
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    # Filter successful results
    signals = [r for r in results if r is not None and not isinstance(r, Exception)]
    
    # Sort by signal strength
    signals.sort(key=lambda x: x.get('signal_strength', 0), reverse=True)
    
    return signals

# CLI with enhanced features
if __name__ == '__main__':
    import typer
    app = typer.Typer()

    @app.command()
    def scan_enhanced(
        auto_trade: bool = False,
        max_positions: int = 5,
        sl: float = 0.02, 
        tp: float = 0.04,
        min_signal_strength: float = 0.7
    ):
        """Enhanced scanner with optional auto-trading"""
        try:
            # Get universe
            sp500 = fmp_client.get_sp500()
            large_caps = fmp_client.get_large_caps(100e9)
            universe = list(set(sp500 + large_caps))[:200]  # Limit for testing
            
            logger.info(f"Scanning {len(universe)} symbols...")
            
            # Run scanner
            signals = asyncio.run(run_enhanced_scanner(universe))
            
            # Filter by signal strength
            strong_signals = [s for s in signals if s['signal_strength'] >= min_signal_strength]
            
            logger.info(f"Found {len(strong_signals)} strong signals")
            
            # Display results
            for signal in strong_signals[:10]:  # Top 10
                logger.info(f"Signal: {signal['symbol']} | "
                          f"Strength: {signal['signal_strength']:.2f} | "
                          f"Squeeze: {signal['squeeze_confirmations']}/3 | "
                          f"Filters: {signal['filter_score']}/4")
            
            # Auto-trade if enabled
            if auto_trade and strong_signals:
                trader = EnhancedAlpacaTrader()
                
                # Get current positions
                positions = trader.client.list_positions()
                current_symbols = {pos.symbol for pos in positions}
                
                for signal in strong_signals[:max_positions]:
                    if signal['symbol'] not in current_symbols:
                        order = trader.submit_smart_order(signal, sl, tp)
                        if order:
                            logger.info(f"Auto-trade executed for {signal['symbol']}")
                        
                        if len(current_symbols) >= max_positions:
                            break
                            
        except Exception as e:
            logger.error(f"Scanner error: {e}")

    @app.command() 
    def analyze(symbol: str):
        """Analyze a specific symbol"""
        result = asyncio.run(analyze_symbol_enhanced(symbol.upper()))
        if result:
            print(f"\nAnalysis for {result['symbol']}:")
            print(f"Signal Strength: {result['signal_strength']:.2f}")
            print(f"Squeeze Confirmations: {result['squeeze_confirmations']}/3")
            print(f"Filter Score: {result['filter_score']}/4")
            print(f"Primary Momentum: {result['primary_momentum']:.6f}")
        else:
            print(f"No valid signal found for {symbol}")

    app()
