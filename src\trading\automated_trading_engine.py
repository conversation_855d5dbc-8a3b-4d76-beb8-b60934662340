"""
Automated Trading Engine for High-Confidence TTM Squeeze Setups
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
import logging
from datetime import datetime, timedelta, time
from dataclasses import dataclass
from enum import Enum
import asyncio
import json

from .alpaca_trader import AlpacaTrader
from .ai_stop_loss_manager import AIStopLossManager
from ..ai.signal_enhancer import AISignalEnhancer, AIEnhancedSignal

logger = logging.getLogger(__name__)

class TradingMode(Enum):
    PAPER = "paper"
    LIVE = "live"
    DISABLED = "disabled"

class OrderStatus(Enum):
    PENDING = "pending"
    FILLED = "filled"
    CANCELLED = "cancelled"
    REJECTED = "rejected"

@dataclass
class TradingConfig:
    """Automated trading configuration"""
    enabled: bool = False
    mode: TradingMode = TradingMode.PAPER
    min_signal_score: float = 85.0
    min_confirmation_score: float = 80.0
    max_daily_trades: int = 10
    max_portfolio_risk: float = 0.06  # 6%
    max_single_position: float = 0.10  # 10%
    max_correlation_exposure: float = 0.30  # 30%
    trading_start_time: time = time(9, 30)  # 9:30 AM
    trading_end_time: time = time(15, 30)   # 3:30 PM
    emergency_stop: bool = False

@dataclass
class AutoTrade:
    """Automated trade record"""
    trade_id: str
    symbol: str
    signal: AIEnhancedSignal
    order_side: str  # 'buy' or 'sell'
    quantity: int
    entry_price: float
    stop_loss: float
    take_profit: float
    order_status: OrderStatus
    timestamp: datetime
    fill_price: Optional[float] = None
    exit_price: Optional[float] = None
    exit_timestamp: Optional[datetime] = None
    pnl: Optional[float] = None

class AutomatedTradingEngine:
    """AI-powered automated trading engine"""
    
    def __init__(self, data_manager, ai_enhancer: AISignalEnhancer):
        self.data_manager = data_manager
        self.ai_enhancer = ai_enhancer
        
        # Initialize components
        self.trader = AlpacaTrader()
        self.stop_manager = AIStopLossManager(data_manager, ai_enhancer.risk_engine)
        
        # Trading configuration
        self.config = TradingConfig()
        
        # Trading state
        self.active_trades = {}  # trade_id -> AutoTrade
        self.daily_trade_count = 0
        self.last_trade_date = None
        self.portfolio_risk = 0.0
        
        # Safety controls
        self.circuit_breaker_triggered = False
        self.unusual_market_detected = False
        
        # Performance tracking
        self.trade_history = []
        self.performance_metrics = {}
        
        logger.info("Automated Trading Engine initialized")
    
    async def process_signal(self, enhanced_signal: AIEnhancedSignal) -> Optional[AutoTrade]:
        """
        Process an AI-enhanced signal for potential automated trading
        
        Args:
            enhanced_signal: AI-enhanced TTM Squeeze signal
            
        Returns:
            AutoTrade if trade was executed, None otherwise
        """
        try:
            # Check if auto-trading is enabled
            if not self.config.enabled or self.config.mode == TradingMode.DISABLED:
                return None
            
            # Safety checks
            if not self._safety_checks_passed():
                return None
            
            # Signal quality checks
            if not self._signal_quality_check(enhanced_signal):
                logger.debug(f"Signal quality check failed for {enhanced_signal.original_signal.symbol}")
                return None
            
            # Market hours check
            if not self._is_market_hours():
                return None
            
            # Risk management checks
            if not await self._risk_management_check(enhanced_signal):
                return None
            
            # Execute trade
            trade = await self._execute_automated_trade(enhanced_signal)
            
            if trade:
                logger.info(f"Automated trade executed: {trade.symbol} - "
                           f"Score: {enhanced_signal.final_score:.1f}")
            
            return trade
            
        except Exception as e:
            logger.error(f"Error processing signal for auto-trading: {e}")
            return None
    
    def _safety_checks_passed(self) -> bool:
        """Perform safety checks before trading"""
        
        # Emergency stop check
        if self.config.emergency_stop:
            logger.warning("Emergency stop activated - no trading")
            return False
        
        # Circuit breaker check
        if self.circuit_breaker_triggered:
            logger.warning("Circuit breaker triggered - no trading")
            return False
        
        # Daily trade limit check
        if self._is_new_trading_day():
            self.daily_trade_count = 0
            self.last_trade_date = datetime.now().date()
        
        if self.daily_trade_count >= self.config.max_daily_trades:
            logger.debug(f"Daily trade limit reached: {self.daily_trade_count}")
            return False
        
        # Portfolio risk check
        if self.portfolio_risk >= self.config.max_portfolio_risk:
            logger.debug(f"Portfolio risk limit reached: {self.portfolio_risk:.2%}")
            return False
        
        return True
    
    def _signal_quality_check(self, signal: AIEnhancedSignal) -> bool:
        """Check if signal meets quality thresholds"""
        
        # Final score check
        if signal.final_score < self.config.min_signal_score:
            return False
        
        # Multi-factor confirmation check (if available)
        # This would be integrated with the multi-factor confirmation system
        
        # Market context check
        if signal.market_context_score < 50:  # Poor market conditions
            return False
        
        # AI confidence check
        if signal.ai_confidence < 70:
            return False
        
        return True
    
    def _is_market_hours(self) -> bool:
        """Check if current time is within trading hours"""
        now = datetime.now().time()
        return self.config.trading_start_time <= now <= self.config.trading_end_time
    
    def _is_new_trading_day(self) -> bool:
        """Check if it's a new trading day"""
        today = datetime.now().date()
        return self.last_trade_date != today
    
    async def _risk_management_check(self, signal: AIEnhancedSignal) -> bool:
        """Perform comprehensive risk management checks"""
        try:
            # Position size check
            suggested_size = signal.suggested_position_size
            if suggested_size > self.config.max_single_position:
                logger.debug(f"Position size too large: {suggested_size:.2%}")
                return False
            
            # Portfolio correlation check
            correlation_exposure = await self._calculate_correlation_exposure(signal.original_signal.symbol)
            if correlation_exposure > self.config.max_correlation_exposure:
                logger.debug(f"Correlation exposure too high: {correlation_exposure:.2%}")
                return False
            
            # Account buying power check
            account = self.trader.get_account()
            if not account or float(account.buying_power) < 1000:  # Minimum $1000
                logger.warning("Insufficient buying power for automated trading")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error in risk management check: {e}")
            return False
    
    async def _calculate_correlation_exposure(self, symbol: str) -> float:
        """Calculate portfolio exposure to correlated assets"""
        try:
            # Get current positions
            positions = self.trader.get_positions()
            if not positions:
                return 0.0
            
            # Simple sector-based correlation (would be enhanced with actual correlation data)
            sector_exposure = {}
            total_exposure = 0.0
            
            for position in positions:
                pos_symbol = position.symbol
                sector = self._get_symbol_sector(pos_symbol)
                market_value = abs(float(position.market_value))
                
                sector_exposure[sector] = sector_exposure.get(sector, 0) + market_value
                total_exposure += market_value
            
            # Check exposure to same sector as new symbol
            new_symbol_sector = self._get_symbol_sector(symbol)
            sector_exp = sector_exposure.get(new_symbol_sector, 0)
            
            if total_exposure > 0:
                return sector_exp / total_exposure
            else:
                return 0.0
                
        except Exception as e:
            logger.warning(f"Error calculating correlation exposure: {e}")
            return 0.0
    
    def _get_symbol_sector(self, symbol: str) -> str:
        """Get sector for symbol (simplified mapping)"""
        # This would be enhanced with a proper sector database
        tech_symbols = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'NVDA', 'META']
        finance_symbols = ['JPM', 'BAC', 'WFC', 'GS', 'MS', 'C']
        healthcare_symbols = ['JNJ', 'PFE', 'UNH', 'ABBV', 'MRK']
        
        if symbol in tech_symbols:
            return 'Technology'
        elif symbol in finance_symbols:
            return 'Financials'
        elif symbol in healthcare_symbols:
            return 'Healthcare'
        else:
            return 'Other'
    
    async def _execute_automated_trade(self, signal: AIEnhancedSignal) -> Optional[AutoTrade]:
        """Execute automated trade based on signal"""
        try:
            symbol = signal.original_signal.symbol
            
            # Determine trade direction
            if signal.original_signal.primary_signal.momentum > 0:
                order_side = 'buy'
            else:
                order_side = 'sell'  # For short selling (if enabled)
                # For now, only handle long positions
                return None
            
            # Calculate position size
            account = self.trader.get_account()
            account_value = float(account.portfolio_value)
            position_value = account_value * signal.suggested_position_size
            
            # Get current price
            current_price = await self._get_current_price(symbol)
            if not current_price:
                return None
            
            # Calculate quantity
            quantity = int(position_value / current_price)
            if quantity < 1:
                logger.debug(f"Position size too small for {symbol}: {quantity}")
                return None
            
            # Create stop loss
            stop_loss_level = self.stop_manager.create_ai_stop_loss(
                symbol=symbol,
                entry_price=current_price,
                position_side='long',
                signal_confidence=signal.ai_confidence,
                market_context={'regime': signal.market_regime}
            )
            
            # Create trade record
            trade_id = f"{symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            trade = AutoTrade(
                trade_id=trade_id,
                symbol=symbol,
                signal=signal,
                order_side=order_side,
                quantity=quantity,
                entry_price=current_price,
                stop_loss=stop_loss_level.current_stop,
                take_profit=current_price * (1 + signal.suggested_take_profit),
                order_status=OrderStatus.PENDING,
                timestamp=datetime.now()
            )
            
            # Execute order
            if self.config.mode == TradingMode.PAPER:
                # Paper trading - simulate execution
                trade.order_status = OrderStatus.FILLED
                trade.fill_price = current_price
                logger.info(f"Paper trade executed: {symbol} {quantity} shares at ${current_price:.2f}")
            else:
                # Live trading
                order = self.trader.place_order(
                    symbol=symbol,
                    qty=quantity,
                    side=order_side,
                    type='market',
                    time_in_force='day'
                )
                
                if order:
                    trade.order_status = OrderStatus.FILLED
                    trade.fill_price = float(order.filled_avg_price) if order.filled_avg_price else current_price
                    logger.info(f"Live trade executed: {symbol} {quantity} shares")
                else:
                    trade.order_status = OrderStatus.REJECTED
                    logger.warning(f"Trade rejected for {symbol}")
                    return None
            
            # Store active trade
            self.active_trades[trade_id] = trade
            self.daily_trade_count += 1
            
            # Update portfolio risk
            self._update_portfolio_risk()
            
            return trade
            
        except Exception as e:
            logger.error(f"Error executing automated trade: {e}")
            return None
    
    async def _get_current_price(self, symbol: str) -> Optional[float]:
        """Get current price for symbol"""
        try:
            # Get latest data
            data = self.data_manager.get_historical_data(symbol, '1Min', periods=1)
            if data is not None and len(data) > 0:
                return data['close'].iloc[-1]
            return None
        except Exception:
            return None
    
    def _update_portfolio_risk(self):
        """Update current portfolio risk"""
        try:
            total_risk = 0.0
            for trade in self.active_trades.values():
                if trade.order_status == OrderStatus.FILLED:
                    position_risk = abs(trade.entry_price - trade.stop_loss) / trade.entry_price
                    position_size = trade.quantity * trade.entry_price
                    total_risk += position_risk * position_size
            
            # Get account value
            account = self.trader.get_account()
            if account:
                account_value = float(account.portfolio_value)
                self.portfolio_risk = total_risk / account_value
            
        except Exception as e:
            logger.error(f"Error updating portfolio risk: {e}")
    
    async def monitor_positions(self):
        """Monitor active positions and manage stops"""
        try:
            if not self.active_trades:
                return
            
            # Get current prices
            symbols = [trade.symbol for trade in self.active_trades.values() 
                      if trade.order_status == OrderStatus.FILLED]
            
            current_prices = {}
            for symbol in symbols:
                price = await self._get_current_price(symbol)
                if price:
                    current_prices[symbol] = price
            
            # Update stop losses
            stop_updates = self.stop_manager.update_stop_losses(current_prices)
            
            # Handle triggered stops
            for symbol, stop_info in stop_updates['triggered'].items():
                await self._handle_stop_trigger(symbol, stop_info)
            
            # Log stop updates
            for symbol, update_info in stop_updates['updates'].items():
                logger.info(f"Stop updated for {symbol}: "
                           f"${update_info['old_stop']:.2f} -> ${update_info['new_stop']:.2f}")
            
        except Exception as e:
            logger.error(f"Error monitoring positions: {e}")
    
    async def _handle_stop_trigger(self, symbol: str, stop_info: Dict):
        """Handle triggered stop loss"""
        try:
            # Find the trade
            trade = None
            for t in self.active_trades.values():
                if t.symbol == symbol and t.order_status == OrderStatus.FILLED:
                    trade = t
                    break
            
            if not trade:
                return
            
            # Execute stop loss order
            if self.config.mode == TradingMode.PAPER:
                # Paper trading
                trade.exit_price = stop_info['current_price']
                trade.exit_timestamp = datetime.now()
                trade.pnl = (trade.exit_price - trade.entry_price) * trade.quantity
                logger.info(f"Paper stop triggered for {symbol}: "
                           f"Exit at ${trade.exit_price:.2f}, PnL: ${trade.pnl:.2f}")
            else:
                # Live trading - place market sell order
                order = self.trader.place_order(
                    symbol=symbol,
                    qty=trade.quantity,
                    side='sell',
                    type='market',
                    time_in_force='day'
                )
                
                if order:
                    trade.exit_price = float(order.filled_avg_price) if order.filled_avg_price else stop_info['current_price']
                    trade.exit_timestamp = datetime.now()
                    trade.pnl = (trade.exit_price - trade.entry_price) * trade.quantity
                    logger.info(f"Stop loss executed for {symbol}")
            
            # Move to trade history
            self.trade_history.append(trade)
            del self.active_trades[trade.trade_id]
            
            # Remove stop loss
            self.stop_manager.remove_stop(symbol)
            
            # Update portfolio risk
            self._update_portfolio_risk()
            
        except Exception as e:
            logger.error(f"Error handling stop trigger for {symbol}: {e}")
    
    def set_config(self, config_dict: Dict):
        """Update trading configuration"""
        try:
            for key, value in config_dict.items():
                if hasattr(self.config, key):
                    setattr(self.config, key, value)
            
            logger.info(f"Trading config updated: {config_dict}")
            
        except Exception as e:
            logger.error(f"Error updating config: {e}")
    
    def get_status(self) -> Dict:
        """Get trading engine status"""
        return {
            'enabled': self.config.enabled,
            'mode': self.config.mode.value,
            'active_trades': len(self.active_trades),
            'daily_trade_count': self.daily_trade_count,
            'portfolio_risk': self.portfolio_risk,
            'circuit_breaker': self.circuit_breaker_triggered,
            'emergency_stop': self.config.emergency_stop,
            'total_trades': len(self.trade_history),
            'config': {
                'min_signal_score': self.config.min_signal_score,
                'min_confirmation_score': self.config.min_confirmation_score,
                'max_daily_trades': self.config.max_daily_trades,
                'max_portfolio_risk': self.config.max_portfolio_risk
            }
        }
    
    def emergency_stop_all(self):
        """Emergency stop all automated trading"""
        self.config.emergency_stop = True
        self.config.enabled = False
        logger.critical("EMERGENCY STOP ACTIVATED - All automated trading halted")
    
    def get_performance_summary(self) -> Dict:
        """Get performance summary"""
        if not self.trade_history:
            return {'total_trades': 0, 'total_pnl': 0.0, 'win_rate': 0.0}
        
        total_pnl = sum(trade.pnl for trade in self.trade_history if trade.pnl)
        winning_trades = sum(1 for trade in self.trade_history if trade.pnl and trade.pnl > 0)
        win_rate = winning_trades / len(self.trade_history) * 100
        
        return {
            'total_trades': len(self.trade_history),
            'active_trades': len(self.active_trades),
            'total_pnl': total_pnl,
            'win_rate': win_rate,
            'avg_trade_pnl': total_pnl / len(self.trade_history) if self.trade_history else 0.0
        }
