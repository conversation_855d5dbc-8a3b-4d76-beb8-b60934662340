"""
Technical Indicators for TTM Squeeze Trading System
"""
import numpy as np
import pandas as pd
from typing import Tuple, Optional
import logging

logger = logging.getLogger(__name__)

class TechnicalIndicators:
    """Collection of technical indicators for TTM Squeeze system"""
    
    @staticmethod
    def sma(data: pd.Series, period: int) -> pd.Series:
        """Simple Moving Average"""
        return data.rolling(window=period).mean()
    
    @staticmethod
    def ema(data: pd.Series, period: int) -> pd.Series:
        """Exponential Moving Average"""
        return data.ewm(span=period, adjust=False).mean()
    
    @staticmethod
    def atr(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> pd.Series:
        """Average True Range"""
        tr1 = high - low
        tr2 = abs(high - close.shift(1))
        tr3 = abs(low - close.shift(1))
        
        true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        return true_range.rolling(window=period).mean()
    
    @staticmethod
    def bollinger_bands(data: pd.Series, period: int = 20, std_dev: float = 2.0) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """
        Bollinger Bands calculation
        Returns: (upper_band, middle_band, lower_band)
        """
        middle_band = TechnicalIndicators.sma(data, period)
        std = data.rolling(window=period).std()
        
        upper_band = middle_band + (std * std_dev)
        lower_band = middle_band - (std * std_dev)
        
        return upper_band, middle_band, lower_band
    
    @staticmethod
    def keltner_channels(high: pd.Series, low: pd.Series, close: pd.Series, 
                        period: int = 20, atr_multiplier: float = 1.5) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """
        Keltner Channels calculation
        Returns: (upper_channel, middle_channel, lower_channel)
        """
        middle_channel = TechnicalIndicators.ema(close, period)
        atr_values = TechnicalIndicators.atr(high, low, close, period)
        
        upper_channel = middle_channel + (atr_values * atr_multiplier)
        lower_channel = middle_channel - (atr_values * atr_multiplier)
        
        return upper_channel, middle_channel, lower_channel
    
    @staticmethod
    def macd(data: pd.Series, fast_period: int = 12, slow_period: int = 26, signal_period: int = 9) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """
        MACD calculation
        Returns: (macd_line, signal_line, histogram)
        """
        ema_fast = TechnicalIndicators.ema(data, fast_period)
        ema_slow = TechnicalIndicators.ema(data, slow_period)
        
        macd_line = ema_fast - ema_slow
        signal_line = TechnicalIndicators.ema(macd_line, signal_period)
        histogram = macd_line - signal_line
        
        return macd_line, signal_line, histogram
    
    @staticmethod
    def linear_regression_slope(data: pd.Series, period: int) -> pd.Series:
        """
        Calculate linear regression slope for momentum
        """
        def calc_slope(y_values):
            if len(y_values) < period:
                return np.nan
            
            x = np.arange(len(y_values))
            y = y_values.values
            
            # Linear regression: y = mx + b
            n = len(x)
            sum_x = np.sum(x)
            sum_y = np.sum(y)
            sum_xy = np.sum(x * y)
            sum_x2 = np.sum(x * x)
            
            # Calculate slope (m)
            slope = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x * sum_x)
            return slope
        
        return data.rolling(window=period).apply(calc_slope, raw=False)
    
    @staticmethod
    def momentum_histogram(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 20) -> pd.Series:
        """
        TTM Squeeze momentum histogram calculation
        """
        # Calculate the midpoint of high and low
        hl2 = (high + low) / 2
        
        # Get the highest high and lowest low over the period
        highest_high = high.rolling(window=period).max()
        lowest_low = low.rolling(window=period).min()
        
        # Calculate momentum as linear regression slope of (close - average of highest high and lowest low)
        avg_hl = (highest_high + lowest_low) / 2
        momentum_data = close - avg_hl
        
        momentum = TechnicalIndicators.linear_regression_slope(momentum_data, period)
        
        return momentum
    
    @staticmethod
    def volume_sma(volume: pd.Series, period: int = 20) -> pd.Series:
        """Volume Simple Moving Average"""
        return volume.rolling(window=period).mean()
    
    @staticmethod
    def rsi(data: pd.Series, period: int = 14) -> pd.Series:
        """Relative Strength Index"""
        delta = data.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        
        return rsi
