"""
Configuration settings for TTM Squeeze Trading System
"""
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class Config:
    """Main configuration class"""
    
    # API Keys
    ALPACA_API_KEY = os.getenv('ALPACA_API_KEY', 'PK2O4NB71EQDMMENX77L')
    ALPACA_SECRET_KEY = os.getenv('ALPACA_SECRET_KEY', 'HLv1oA7hH3Yah59LElAayj17pncG5KmMTNKEoW8j')
    ALPACA_BASE_URL = os.getenv('ALPACA_BASE_URL', 'https://paper-api.alpaca.markets')  # Paper trading by default
    
    FMP_API_KEY = os.getenv('FMP_API_KEY', 'K63wnAbUDHNPICjzeq3JcEG1vi8Q2oz7')
    
    # TTM Squeeze Parameters
    TTM_SQUEEZE_CONFIG = {
        'bb_period': 20,
        'bb_std_dev': 2.0,
        'kc_period': 20,
        'kc_atr_multiplier': 1.5,
        'momentum_period': 20,
        'ema_fast': 8,
        'ema_slow': 21,
        'volume_period': 20,
        'volume_threshold': 0.8,  # 80% of average volume
    }
    
    # Timeframes for multi-timeframe analysis
    TIMEFRAMES = {
        'primary': '15Min',
        'confirmation': ['5Min', '30Min', '1Hour', '1Day'],
        'required_confirmations': 2  # At least 2 out of 5 timeframes
    }
    
    # Stock Universe - Entire S&P 500 + 100B+ market cap stocks
    STOCK_UNIVERSE = {
        'sp500': True,  # Include all S&P 500 stocks
        'min_market_cap': 100_000_000_000,  # $100 billion
        'min_volume': 500_000,  # Reduced minimum daily volume for broader coverage
        'min_price': 5.0,  # Reduced minimum stock price for broader coverage
        'max_stocks': 1000,  # Increased limit to capture more stocks
    }
    
    # Risk Management
    RISK_MANAGEMENT = {
        'max_position_size': 0.02,  # 2% of portfolio per position
        'max_portfolio_risk': 0.06,  # 6% total portfolio risk
        'stop_loss_pct': 0.02,  # 2% stop loss
        'take_profit_pct': 0.04,  # 4% take profit (2:1 R/R)
        'max_positions': 10,  # Maximum concurrent positions
    }
    
    # Scanner Settings
    SCANNER_CONFIG = {
        'scan_interval': 60,  # Scan every 60 seconds
        'market_hours_only': False,  # Allow scanning outside market hours for testing
        'premarket_scan': True,
        'afterhours_scan': True,
    }
    
    # Database
    DATABASE_URL = os.getenv('DATABASE_URL', 'sqlite:///ttm_squeeze.db')
    
    # Logging
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'DEBUG')  # More verbose logging for debugging
    LOG_FILE = os.getenv('LOG_FILE', 'logs/ttm_squeeze.log')
    
    # Flask settings
    FLASK_HOST = os.getenv('FLASK_HOST', '127.0.0.1')
    FLASK_PORT = int(os.getenv('FLASK_PORT', 5000))
    FLASK_DEBUG = os.getenv('FLASK_DEBUG', 'True').lower() == 'true'

class ProductionConfig(Config):
    """Production configuration"""
    ALPACA_BASE_URL = 'https://api.alpaca.markets'  # Live trading
    FLASK_DEBUG = False

class DevelopmentConfig(Config):
    """Development configuration"""
    FLASK_DEBUG = True

class TestingConfig(Config):
    """Testing configuration"""
    TESTING = True
    DATABASE_URL = 'sqlite:///:memory:'
