"""
Market Context Analyzer - AI-powered analysis of broader market conditions
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
import logging
from datetime import datetime, timedelta
import asyncio
import aiohttp
from dataclasses import dataclass

from .openai_market_intelligence import OpenAIMarketIntelligence

logger = logging.getLogger(__name__)

@dataclass
class MarketRegime:
    """Market regime classification"""
    regime_type: str  # 'trending', 'ranging', 'volatile', 'calm'
    confidence: float  # 0-100
    duration_days: int
    characteristics: Dict

@dataclass
class SectorAnalysis:
    """Sector rotation and strength analysis"""
    sector: str
    relative_strength: float  # -100 to 100
    momentum: float
    rotation_score: float  # How much money is flowing in/out

class MarketContextAnalyzer:
    """Analyzes broader market conditions for signal filtering"""
    
    def __init__(self, data_manager):
        self.data_manager = data_manager
        self.market_data_cache = {}
        self.last_update = None
        self.update_interval = timedelta(minutes=15)

        # Initialize OpenAI intelligence
        self.openai_intelligence = OpenAIMarketIntelligence()
        
        # Market indicators to track
        self.market_indicators = {
            'SPY': 'S&P 500 ETF',
            'QQQ': 'NASDAQ ETF', 
            'IWM': 'Russell 2000 ETF',
            'VIX': 'Volatility Index',
            'TLT': 'Treasury Bonds',
            'GLD': 'Gold ETF',
            'DXY': 'Dollar Index'
        }
        
        # Sector ETFs for rotation analysis
        self.sector_etfs = {
            'XLK': 'Technology',
            'XLF': 'Financials',
            'XLV': 'Healthcare',
            'XLE': 'Energy',
            'XLI': 'Industrials',
            'XLY': 'Consumer Discretionary',
            'XLP': 'Consumer Staples',
            'XLB': 'Materials',
            'XLU': 'Utilities',
            'XLRE': 'Real Estate'
        }
        
        logger.info("Market Context Analyzer initialized")
    
    async def analyze_context(self, symbol: str, market_data: Dict) -> Dict:
        """
        Analyze market context for a specific symbol
        
        Args:
            symbol: Stock symbol to analyze
            market_data: Current market data
            
        Returns:
            Dictionary with market context analysis
        """
        try:
            # Update market data if needed
            await self._update_market_data()
            
            # Analyze market regime
            market_regime = await self._analyze_market_regime()
            
            # Analyze sector strength
            sector_analysis = await self._analyze_sector_strength(symbol)
            
            # Calculate market favorability score
            favorability_score = self._calculate_favorability_score(
                market_regime, sector_analysis, market_data
            )
            
            # Analyze volatility environment
            volatility_analysis = self._analyze_volatility_environment()
            
            # Get news sentiment (placeholder for now)
            news_sentiment = await self._get_news_sentiment(symbol)
            
            return {
                'favorability_score': favorability_score,
                'regime': market_regime.regime_type,
                'regime_confidence': market_regime.confidence,
                'sector_strength': sector_analysis.get('relative_strength', 0.0),
                'sector_momentum': sector_analysis.get('momentum', 0.0),
                'volatility_regime': volatility_analysis['regime'],
                'volatility_percentile': volatility_analysis['percentile'],
                'news_sentiment': news_sentiment,
                'market_breadth': self._calculate_market_breadth(),
                'risk_on_off': self._assess_risk_sentiment()
            }
            
        except Exception as e:
            logger.error(f"Error analyzing market context: {e}")
            return self._get_default_context()
    
    async def _update_market_data(self):
        """Update market data cache"""
        if (self.last_update and 
            datetime.now() - self.last_update < self.update_interval):
            return
        
        try:
            # Get data for market indicators
            for symbol in self.market_indicators.keys():
                try:
                    data = self.data_manager.get_historical_data(symbol, '1Day', periods=50)
                    if data is not None:
                        self.market_data_cache[symbol] = data
                except Exception as e:
                    logger.debug(f"Could not get data for {symbol}: {e}")
            
            # Get sector ETF data
            for symbol in self.sector_etfs.keys():
                try:
                    data = self.data_manager.get_historical_data(symbol, '1Day', periods=50)
                    if data is not None:
                        self.market_data_cache[symbol] = data
                except Exception as e:
                    logger.debug(f"Could not get sector data for {symbol}: {e}")
            
            self.last_update = datetime.now()
            logger.debug("Market data cache updated")
            
        except Exception as e:
            logger.warning(f"Error updating market data: {e}")
    
    async def _analyze_market_regime(self) -> MarketRegime:
        """Analyze current market regime"""
        try:
            spy_data = self.market_data_cache.get('SPY')
            vix_data = self.market_data_cache.get('VIX')
            
            if spy_data is None or len(spy_data) < 20:
                return MarketRegime('unknown', 50.0, 0, {})
            
            # Calculate regime indicators
            returns = spy_data['close'].pct_change().dropna()
            volatility = returns.rolling(20).std() * np.sqrt(252)
            
            # Trend analysis
            sma_20 = spy_data['close'].rolling(20).mean()
            sma_50 = spy_data['close'].rolling(50).mean() if len(spy_data) >= 50 else sma_20
            
            current_price = spy_data['close'].iloc[-1]
            trend_strength = (current_price - sma_20.iloc[-1]) / sma_20.iloc[-1]
            
            # Volatility regime
            current_vol = volatility.iloc[-1] if not volatility.empty else 0.2
            vol_percentile = (volatility <= current_vol).mean() * 100
            
            # Determine regime
            if current_vol > 0.25:  # High volatility
                if abs(trend_strength) > 0.05:
                    regime_type = 'volatile_trending'
                else:
                    regime_type = 'volatile'
            else:  # Low volatility
                if abs(trend_strength) > 0.03:
                    regime_type = 'trending'
                else:
                    regime_type = 'ranging'
            
            # Calculate confidence
            confidence = min(100.0, max(50.0, 100 - vol_percentile))
            
            return MarketRegime(
                regime_type=regime_type,
                confidence=confidence,
                duration_days=self._estimate_regime_duration(spy_data),
                characteristics={
                    'volatility': current_vol,
                    'trend_strength': trend_strength,
                    'vol_percentile': vol_percentile
                }
            )
            
        except Exception as e:
            logger.warning(f"Error analyzing market regime: {e}")
            return MarketRegime('unknown', 50.0, 0, {})
    
    async def _analyze_sector_strength(self, symbol: str) -> Dict:
        """Analyze sector strength and rotation"""
        try:
            # Get symbol's sector (simplified mapping)
            symbol_sector = self._get_symbol_sector(symbol)
            
            if not symbol_sector:
                return {'relative_strength': 0.0, 'momentum': 0.0, 'rotation_score': 0.0}
            
            # Get sector ETF data
            sector_data = self.market_data_cache.get(symbol_sector)
            spy_data = self.market_data_cache.get('SPY')
            
            if sector_data is None or spy_data is None:
                return {'relative_strength': 0.0, 'momentum': 0.0, 'rotation_score': 0.0}
            
            # Calculate relative strength
            sector_returns = sector_data['close'].pct_change(20).iloc[-1]
            spy_returns = spy_data['close'].pct_change(20).iloc[-1]
            relative_strength = (sector_returns - spy_returns) * 100
            
            # Calculate momentum
            sector_momentum = sector_data['close'].pct_change(5).iloc[-1] * 100
            
            # Calculate rotation score (volume-weighted)
            if 'volume' in sector_data.columns:
                avg_volume = sector_data['volume'].rolling(20).mean().iloc[-1]
                current_volume = sector_data['volume'].iloc[-1]
                rotation_score = (current_volume / avg_volume - 1) * 100
            else:
                rotation_score = 0.0
            
            return {
                'relative_strength': relative_strength,
                'momentum': sector_momentum,
                'rotation_score': rotation_score,
                'sector': self.sector_etfs.get(symbol_sector, 'Unknown')
            }
            
        except Exception as e:
            logger.warning(f"Error analyzing sector strength: {e}")
            return {'relative_strength': 0.0, 'momentum': 0.0, 'rotation_score': 0.0}
    
    def _get_symbol_sector(self, symbol: str) -> Optional[str]:
        """Get sector ETF for a symbol (simplified mapping)"""
        # This is a simplified mapping - in production, you'd use a proper sector database
        tech_symbols = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'NVDA', 'META']
        finance_symbols = ['JPM', 'BAC', 'WFC', 'GS', 'MS', 'C']
        healthcare_symbols = ['JNJ', 'PFE', 'UNH', 'ABBV', 'MRK']
        
        if symbol in tech_symbols:
            return 'XLK'
        elif symbol in finance_symbols:
            return 'XLF'
        elif symbol in healthcare_symbols:
            return 'XLV'
        else:
            return 'SPY'  # Default to market
    
    def _analyze_volatility_environment(self) -> Dict:
        """Analyze current volatility environment"""
        try:
            vix_data = self.market_data_cache.get('VIX')
            
            if vix_data is None or len(vix_data) < 20:
                return {'regime': 'normal', 'percentile': 50.0, 'current_vix': 20.0}
            
            current_vix = vix_data['close'].iloc[-1]
            vix_percentile = (vix_data['close'] <= current_vix).mean() * 100
            
            # Classify volatility regime
            if current_vix < 15:
                regime = 'low'
            elif current_vix < 25:
                regime = 'normal'
            elif current_vix < 35:
                regime = 'elevated'
            else:
                regime = 'high'
            
            return {
                'regime': regime,
                'percentile': vix_percentile,
                'current_vix': current_vix
            }
            
        except Exception as e:
            logger.warning(f"Error analyzing volatility: {e}")
            return {'regime': 'normal', 'percentile': 50.0, 'current_vix': 20.0}
    
    async def _get_news_sentiment(self, symbol: str) -> float:
        """Get news sentiment for symbol using OpenAI"""
        try:
            # In a real implementation, you would fetch actual news headlines
            # For now, we'll use a placeholder that could be enhanced with news APIs
            sample_headlines = [
                f"{symbol} reports quarterly earnings",
                f"Market analysis for {symbol}",
                f"Recent developments in {symbol}"
            ]

            # Use OpenAI to analyze sentiment
            news_analysis = await self.openai_intelligence.analyze_news_sentiment(
                symbol, sample_headlines
            )

            return news_analysis.sentiment_score

        except Exception as e:
            logger.warning(f"Error getting news sentiment for {symbol}: {e}")
            return 0.0
    
    def _calculate_market_breadth(self) -> float:
        """Calculate market breadth indicator"""
        try:
            # Use sector ETF performance as proxy for breadth
            sector_performances = []
            
            for sector_etf in self.sector_etfs.keys():
                data = self.market_data_cache.get(sector_etf)
                if data is not None and len(data) >= 5:
                    performance = data['close'].pct_change(5).iloc[-1]
                    sector_performances.append(performance)
            
            if not sector_performances:
                return 0.0
            
            # Calculate percentage of sectors with positive performance
            positive_sectors = sum(1 for p in sector_performances if p > 0)
            breadth = (positive_sectors / len(sector_performances)) * 100
            
            return breadth
            
        except Exception as e:
            logger.warning(f"Error calculating market breadth: {e}")
            return 50.0
    
    def _assess_risk_sentiment(self) -> str:
        """Assess risk-on vs risk-off sentiment"""
        try:
            # Compare growth vs defensive sectors
            growth_etfs = ['XLK', 'XLY']  # Tech, Consumer Discretionary
            defensive_etfs = ['XLU', 'XLP']  # Utilities, Consumer Staples
            
            growth_performance = 0.0
            defensive_performance = 0.0
            
            for etf in growth_etfs:
                data = self.market_data_cache.get(etf)
                if data is not None and len(data) >= 5:
                    growth_performance += data['close'].pct_change(5).iloc[-1]
            
            for etf in defensive_etfs:
                data = self.market_data_cache.get(etf)
                if data is not None and len(data) >= 5:
                    defensive_performance += data['close'].pct_change(5).iloc[-1]
            
            if growth_performance > defensive_performance:
                return 'risk_on'
            else:
                return 'risk_off'
                
        except Exception as e:
            logger.warning(f"Error assessing risk sentiment: {e}")
            return 'neutral'
    
    def _calculate_favorability_score(self, market_regime: MarketRegime, 
                                    sector_analysis: Dict, 
                                    market_data: Dict) -> float:
        """Calculate overall market favorability score"""
        score = 50.0  # Base score
        
        # Regime-based adjustments
        regime_scores = {
            'trending': 80.0,
            'ranging': 60.0,
            'volatile': 30.0,
            'volatile_trending': 40.0,
            'unknown': 50.0
        }
        
        regime_score = regime_scores.get(market_regime.regime_type, 50.0)
        score = score * 0.4 + regime_score * 0.6
        
        # Sector strength adjustment
        sector_strength = sector_analysis.get('relative_strength', 0.0)
        if sector_strength > 5:
            score += 15
        elif sector_strength > 0:
            score += 5
        elif sector_strength < -5:
            score -= 15
        elif sector_strength < 0:
            score -= 5
        
        # Volatility adjustment
        vix = market_data.get('vix', 20.0)
        if vix < 15:
            score += 10  # Low volatility is good
        elif vix > 30:
            score -= 20  # High volatility is bad
        
        return max(0.0, min(100.0, score))
    
    def _estimate_regime_duration(self, spy_data: pd.DataFrame) -> int:
        """Estimate how long current regime has been in place"""
        try:
            # Simple regime change detection based on volatility
            returns = spy_data['close'].pct_change().dropna()
            volatility = returns.rolling(10).std()
            
            current_vol = volatility.iloc[-1]
            
            # Count days since significant regime change
            days = 0
            for i in range(len(volatility) - 2, -1, -1):
                if abs(volatility.iloc[i] - current_vol) > current_vol * 0.5:
                    break
                days += 1
            
            return min(days, 100)  # Cap at 100 days
            
        except Exception:
            return 0
    
    def _get_default_context(self) -> Dict:
        """Get default context when analysis fails"""
        return {
            'favorability_score': 50.0,
            'regime': 'unknown',
            'regime_confidence': 50.0,
            'sector_strength': 0.0,
            'sector_momentum': 0.0,
            'volatility_regime': 'normal',
            'volatility_percentile': 50.0,
            'news_sentiment': 0.0,
            'market_breadth': 50.0,
            'risk_on_off': 'neutral'
        }
    
    async def train(self, historical_data: pd.DataFrame):
        """Train market context models (placeholder)"""
        logger.info("Market context analyzer training completed")
    
    def is_ready(self) -> bool:
        """Check if analyzer is ready"""
        return len(self.market_data_cache) > 0
    
    def get_status(self) -> Dict:
        """Get analyzer status"""
        return {
            'ready': self.is_ready(),
            'last_update': self.last_update,
            'cached_symbols': list(self.market_data_cache.keys()),
            'market_indicators_count': len(self.market_indicators),
            'sector_etfs_count': len(self.sector_etfs)
        }
