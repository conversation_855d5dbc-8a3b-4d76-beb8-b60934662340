# Utils package
from .logger import setup_logging
import json
import os
from datetime import datetime, timedelta
from typing import Dict, Any, Union

class ConfigManager:
    """Simplified configuration manager"""

    def __init__(self, config_file: str = "config.json"):
        self.config_file = config_file
        self.config = {}

    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value"""
        return default

class PerformanceMonitor:
    """Simplified performance monitor"""

    def __init__(self):
        self.metrics = {}
        self.start_time = datetime.now()

    def record_metric(self, name: str, value: Union[int, float], timestamp=None):
        """Record a performance metric"""
        if timestamp is None:
            timestamp = datetime.now()

        if name not in self.metrics:
            self.metrics[name] = []

        self.metrics[name].append({
            'value': value,
            'timestamp': timestamp
        })
