"""
Signal Quality ML Model - Predicts TTM Squeeze signal reliability using machine learning
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
import logging
from datetime import datetime, timedelta
import pickle
import os
from dataclasses import dataclass

# ML imports
try:
    from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
    from sklearn.model_selection import train_test_split, cross_val_score
    from sklearn.preprocessing import StandardScaler
    from sklearn.metrics import mean_squared_error, r2_score
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    logging.warning("scikit-learn not available. Using fallback signal quality prediction.")

logger = logging.getLogger(__name__)

@dataclass
class SignalOutcome:
    """Historical signal outcome for training"""
    signal_features: np.ndarray
    outcome_score: float  # 0-100, based on actual trade performance
    days_to_target: int
    max_favorable_move: float
    max_adverse_move: float
    final_return: float

class SignalQualityPredictor:
    """ML model to predict TTM Squeeze signal quality"""
    
    def __init__(self, model_path: str = "models/signal_quality"):
        self.model_path = model_path
        self.model = None
        self.scaler = StandardScaler()
        self.feature_names = []
        self.trained = False
        
        # Model hyperparameters
        self.model_params = {
            'n_estimators': 100,
            'max_depth': 10,
            'min_samples_split': 5,
            'min_samples_leaf': 2,
            'random_state': 42
        }
        
        # Ensure model directory exists
        if os.path.dirname(model_path):
            os.makedirs(os.path.dirname(model_path), exist_ok=True)
        
        # Try to load existing model
        self._load_model()
    
    def predict(self, features: np.ndarray) -> Tuple[float, float]:
        """
        Predict signal quality and confidence
        
        Args:
            features: Signal features array
            
        Returns:
            Tuple of (quality_score, confidence)
        """
        if not SKLEARN_AVAILABLE or not self.trained:
            return self._fallback_prediction(features)
        
        try:
            # Reshape if single sample
            if features.ndim == 1:
                features = features.reshape(1, -1)
            
            # Scale features
            features_scaled = self.scaler.transform(features)
            
            # Get prediction
            quality_score = self.model.predict(features_scaled)[0]
            
            # Calculate confidence based on feature importance and model certainty
            confidence = self._calculate_confidence(features_scaled[0])
            
            # Ensure valid ranges
            quality_score = max(0.0, min(100.0, quality_score))
            confidence = max(0.0, min(100.0, confidence))
            
            return quality_score, confidence
            
        except Exception as e:
            logger.warning(f"Error in ML prediction: {e}")
            return self._fallback_prediction(features)
    
    async def train(self, historical_data: pd.DataFrame):
        """Train the signal quality model on historical data"""
        if not SKLEARN_AVAILABLE:
            logger.warning("scikit-learn not available. Skipping ML training.")
            return
        
        try:
            logger.info("Training signal quality ML model...")
            
            # Prepare training data
            X, y = self._prepare_training_data(historical_data)
            
            if len(X) < 50:  # Minimum samples for training
                logger.warning(f"Insufficient training data: {len(X)} samples. Need at least 50.")
                return
            
            # Split data
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=42
            )
            
            # Scale features
            X_train_scaled = self.scaler.fit_transform(X_train)
            X_test_scaled = self.scaler.transform(X_test)
            
            # Train ensemble model
            self.model = GradientBoostingRegressor(**self.model_params)
            self.model.fit(X_train_scaled, y_train)
            
            # Evaluate model
            train_score = self.model.score(X_train_scaled, y_train)
            test_score = self.model.score(X_test_scaled, y_test)
            
            # Cross-validation
            cv_scores = cross_val_score(self.model, X_train_scaled, y_train, cv=5)
            
            logger.info(f"Model training completed:")
            logger.info(f"  Training R²: {train_score:.3f}")
            logger.info(f"  Test R²: {test_score:.3f}")
            logger.info(f"  CV Score: {cv_scores.mean():.3f} ± {cv_scores.std():.3f}")
            
            # Feature importance analysis
            self._analyze_feature_importance()
            
            # Save model
            self._save_model()
            
            self.trained = True
            
        except Exception as e:
            logger.error(f"Error training signal quality model: {e}")
            raise
    
    def _prepare_training_data(self, historical_data: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """Prepare training data from historical signals"""
        features_list = []
        outcomes_list = []
        
        # This would be populated with actual historical signal data
        # For now, create synthetic training data based on signal patterns
        
        for _, row in historical_data.iterrows():
            try:
                # Extract signal features (same as in signal_enhancer.py)
                features = self._extract_features_from_row(row)
                
                # Calculate outcome score based on actual performance
                outcome_score = self._calculate_outcome_score(row)
                
                features_list.append(features)
                outcomes_list.append(outcome_score)
                
            except Exception as e:
                logger.debug(f"Error processing training row: {e}")
                continue
        
        if not features_list:
            # Create synthetic data for initial training
            features_list, outcomes_list = self._create_synthetic_training_data()
        
        return np.array(features_list), np.array(outcomes_list)
    
    def _extract_features_from_row(self, row: pd.Series) -> np.ndarray:
        """Extract features from historical data row"""
        # This should match the feature extraction in signal_enhancer.py
        features = []
        
        # Signal strength features
        features.extend([
            row.get('overall_strength', 0.5),
            row.get('confirmations_count', 1),
            row.get('required_confirmations', 2),
            float(row.get('is_valid_setup', False)),
            float(row.get('trend_alignment', False)),
            float(row.get('volume_confirmation', False))
        ])
        
        # Primary signal features
        features.extend([
            float(row.get('primary_squeeze', False)),
            row.get('primary_momentum', 0.0),
            row.get('primary_signal_strength', 0.5),
            float(row.get('primary_entry_signal', False))
        ])
        
        # Timeframe features
        features.extend([
            row.get('timeframe_count', 1),
            row.get('squeeze_ratio', 0.0),
            row.get('entry_ratio', 0.0)
        ])
        
        # Market context features
        features.extend([
            row.get('vix', 20.0),
            row.get('spy_rsi', 50.0),
            row.get('sector_rotation', 0.0)
        ])
        
        return np.array(features, dtype=np.float32)
    
    def _calculate_outcome_score(self, row: pd.Series) -> float:
        """Calculate outcome score based on actual trade performance"""
        # This would use actual trade results
        # For now, use synthetic scoring based on signal characteristics
        
        base_score = 50.0
        
        # Reward multi-timeframe alignment
        if row.get('confirmations_count', 0) >= 3:
            base_score += 20.0
        elif row.get('confirmations_count', 0) >= 2:
            base_score += 10.0
        
        # Reward strong momentum
        momentum = abs(row.get('primary_momentum', 0.0))
        if momentum > 0.5:
            base_score += 15.0
        elif momentum > 0.3:
            base_score += 8.0
        
        # Reward volume confirmation
        if row.get('volume_confirmation', False):
            base_score += 10.0
        
        # Penalize high volatility periods
        vix = row.get('vix', 20.0)
        if vix > 30:
            base_score -= 10.0
        elif vix > 25:
            base_score -= 5.0
        
        return max(0.0, min(100.0, base_score))
    
    def _create_synthetic_training_data(self) -> Tuple[List[np.ndarray], List[float]]:
        """Create synthetic training data for initial model"""
        features_list = []
        outcomes_list = []
        
        # Generate synthetic data points
        np.random.seed(42)
        
        for _ in range(1000):
            # Random signal features
            features = np.random.rand(16)
            
            # Adjust some features to be more realistic
            features[0] = np.random.uniform(0.3, 1.0)  # overall_strength
            features[1] = np.random.randint(1, 5)      # confirmations_count
            features[2] = 2                            # required_confirmations
            features[7] = np.random.uniform(-1, 1)     # momentum
            features[13] = np.random.uniform(10, 40)   # VIX
            features[14] = np.random.uniform(20, 80)   # SPY RSI
            
            # Calculate synthetic outcome based on features
            outcome = self._synthetic_outcome_function(features)
            
            features_list.append(features)
            outcomes_list.append(outcome)
        
        logger.info("Created 1000 synthetic training samples")
        return features_list, outcomes_list
    
    def _synthetic_outcome_function(self, features: np.ndarray) -> float:
        """Synthetic function to generate realistic outcomes"""
        score = 50.0
        
        # Higher overall strength = better outcome
        score += features[0] * 30
        
        # More confirmations = better outcome
        score += (features[1] / features[2]) * 20
        
        # Strong momentum = better outcome
        score += abs(features[7]) * 15
        
        # Lower VIX = better outcome
        score += max(0, (30 - features[13]) / 30) * 10
        
        # Add some noise
        score += np.random.normal(0, 5)
        
        return max(0.0, min(100.0, score))
    
    def _calculate_confidence(self, features: np.ndarray) -> float:
        """Calculate prediction confidence"""
        if not self.trained:
            return 50.0
        
        try:
            # Use feature importance and prediction variance
            feature_importance = self.model.feature_importances_
            
            # Calculate weighted feature strength
            feature_strength = np.sum(features * feature_importance)
            
            # Normalize to 0-100 range
            confidence = min(100.0, max(0.0, feature_strength * 100))
            
            return confidence
            
        except Exception:
            return 50.0
    
    def _analyze_feature_importance(self):
        """Analyze and log feature importance"""
        if not self.model:
            return
        
        feature_names = [
            'overall_strength', 'confirmations_count', 'required_confirmations',
            'is_valid_setup', 'trend_alignment', 'volume_confirmation',
            'primary_squeeze', 'primary_momentum', 'primary_signal_strength',
            'primary_entry_signal', 'timeframe_count', 'squeeze_ratio',
            'entry_ratio', 'vix', 'spy_rsi', 'sector_rotation'
        ]
        
        importance = self.model.feature_importances_
        
        # Sort by importance
        sorted_idx = np.argsort(importance)[::-1]
        
        logger.info("Feature importance ranking:")
        for i, idx in enumerate(sorted_idx[:10]):  # Top 10
            logger.info(f"  {i+1}. {feature_names[idx]}: {importance[idx]:.3f}")
    
    def _fallback_prediction(self, features: np.ndarray) -> Tuple[float, float]:
        """Fallback prediction when ML is not available"""
        # Simple heuristic-based prediction
        if features.ndim == 1:
            overall_strength = features[0] if len(features) > 0 else 0.5
            confirmations = features[1] if len(features) > 1 else 1
            
            # Simple scoring
            quality_score = overall_strength * 60 + (confirmations / 5) * 40
            confidence = 40.0  # Lower confidence for heuristic
            
            return min(100.0, quality_score), confidence
        
        return 50.0, 30.0
    
    def _save_model(self):
        """Save trained model to disk"""
        try:
            model_data = {
                'model': self.model,
                'scaler': self.scaler,
                'feature_names': self.feature_names,
                'trained': self.trained,
                'timestamp': datetime.now()
            }
            
            with open(f"{self.model_path}.pkl", 'wb') as f:
                pickle.dump(model_data, f)
            
            logger.info(f"Model saved to {self.model_path}.pkl")
            
        except Exception as e:
            logger.error(f"Error saving model: {e}")
    
    def _load_model(self):
        """Load trained model from disk"""
        try:
            model_file = f"{self.model_path}.pkl"
            if os.path.exists(model_file):
                with open(model_file, 'rb') as f:
                    model_data = pickle.load(f)
                
                self.model = model_data['model']
                self.scaler = model_data['scaler']
                self.feature_names = model_data.get('feature_names', [])
                self.trained = model_data.get('trained', False)
                
                logger.info(f"Model loaded from {model_file}")
            
        except Exception as e:
            logger.warning(f"Could not load existing model: {e}")
    
    def is_trained(self) -> bool:
        """Check if model is trained and ready"""
        return self.trained and self.model is not None
    
    def get_model_info(self) -> Dict:
        """Get model information"""
        return {
            'trained': self.trained,
            'sklearn_available': SKLEARN_AVAILABLE,
            'model_type': type(self.model).__name__ if self.model else None,
            'feature_count': len(self.feature_names),
            'model_path': self.model_path
        }
