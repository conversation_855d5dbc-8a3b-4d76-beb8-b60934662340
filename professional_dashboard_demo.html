<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TTM Squeeze Pro - Professional Trading Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --bg-primary: #0d1421;
            --bg-secondary: #1a1f2e;
            --bg-tertiary: #2d3748;
            --text-primary: #ffffff;
            --text-secondary: #b8bcc8;
            --text-muted: #718096;
            --accent-blue: #00d4ff;
            --accent-green: #00ff88;
            --accent-red: #ff4757;
            --accent-yellow: #ffa502;
            --border-color: #4a5568;
            --hover-bg: #2d3748;
        }

        body {
            background: var(--bg-primary);
            color: var(--text-primary);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
        }

        .dashboard-header {
            background: var(--bg-secondary);
            border-bottom: 1px solid var(--border-color);
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--accent-blue);
        }

        .market-status {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--accent-green);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .trading-grid {
            display: grid;
            grid-template-columns: 300px 1fr 350px;
            grid-template-rows: auto 1fr;
            gap: 1rem;
            padding: 1rem;
            height: calc(100vh - 80px);
        }

        .market-overview {
            grid-column: 1 / 4;
            grid-row: 1;
            display: flex;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .market-metric {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1rem;
            text-align: center;
            flex: 1;
        }

        .metric-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0.25rem;
        }

        .metric-label {
            font-size: 0.8rem;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .metric-change {
            font-size: 0.7rem;
            margin-top: 0.25rem;
        }

        .positive { color: var(--accent-green); }
        .negative { color: var(--accent-red); }
        .neutral { color: var(--text-secondary); }

        .grid-panel {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1rem;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .panel-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid var(--border-color);
        }

        .panel-title {
            color: var(--accent-blue);
            font-weight: 600;
            margin: 0;
        }

        .alert-item {
            background: var(--bg-tertiary);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            padding: 1rem;
            margin-bottom: 0.75rem;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .alert-item:hover {
            background: var(--hover-bg);
            border-color: var(--accent-blue);
        }

        .alert-symbol {
            font-weight: 700;
            font-size: 1.1rem;
            color: var(--accent-blue);
        }

        .alert-signal {
            color: var(--text-secondary);
            font-size: 0.9rem;
            margin: 0.5rem 0;
        }

        .timeframe-badge {
            background: var(--accent-green);
            color: var(--bg-primary);
            padding: 0.2rem 0.5rem;
            border-radius: 3px;
            font-size: 0.7rem;
            font-weight: 600;
            margin-right: 0.25rem;
        }

        .ai-gauge {
            width: 50px;
            height: 50px;
            position: relative;
        }

        .gauge-circle {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .gauge-inner {
            background: var(--bg-secondary);
            border-radius: 50%;
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .btn-trading {
            padding: 0.4rem 0.8rem;
            border-radius: 4px;
            font-weight: 600;
            font-size: 0.8rem;
            border: none;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .btn-buy {
            background: var(--accent-green);
            color: var(--bg-primary);
        }

        .btn-sell {
            background: var(--accent-red);
            color: var(--text-primary);
        }

        .analytics-content {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: 1rem;
            height: 100%;
            overflow-y: auto;
        }

        .analytics-metric {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.75rem;
            padding: 0.5rem 0;
            border-bottom: 1px solid var(--border-color);
        }

        .analytics-metric:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body>
    <div class="dashboard-header">
        <div class="logo">
            <i class="fas fa-chart-line me-2"></i>TTM SQUEEZE PRO
        </div>
        <div class="market-status">
            <div class="status-indicator"></div>
            <span>Market Open</span>
            <span class="text-muted">|</span>
            <span id="current-time">2:45:23 PM CST</span>
        </div>
    </div>

    <div class="trading-grid">
        <!-- Market Overview -->
        <div class="market-overview">
            <div class="market-metric">
                <div class="metric-value positive">$425.67</div>
                <div class="metric-label">SPY</div>
                <div class="metric-change positive">+0.85%</div>
            </div>
            <div class="market-metric">
                <div class="metric-value neutral">22.45</div>
                <div class="metric-label">VIX</div>
                <div class="metric-change negative">-2.1%</div>
            </div>
            <div class="market-metric">
                <div class="metric-value positive">+$1,247</div>
                <div class="metric-label">Day P&L</div>
                <div class="metric-change positive">+2.08%</div>
            </div>
            <div class="market-metric">
                <div class="metric-value neutral">4</div>
                <div class="metric-label">Active Alerts</div>
                <div class="metric-change positive">New: 2</div>
            </div>
            <div class="market-metric">
                <div class="metric-value positive">87%</div>
                <div class="metric-label">AI Confidence</div>
                <div class="metric-change positive">High</div>
            </div>
        </div>

        <!-- TTM Squeeze Alerts Panel -->
        <div class="grid-panel">
            <div class="panel-header">
                <h6 class="panel-title">TTM Squeeze Alerts</h6>
                <button class="btn btn-sm btn-outline-primary" onclick="refreshAlerts()">
                    <i class="fas fa-sync-alt"></i>
                </button>
            </div>
            <div id="alerts-container">
                <div class="alert-item">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <div class="alert-symbol">AAPL</div>
                        <div class="ai-gauge">
                            <div class="gauge-circle" style="background: conic-gradient(var(--accent-green) 313deg, var(--bg-tertiary) 0deg);">
                                <div class="gauge-inner">87</div>
                            </div>
                        </div>
                    </div>
                    <div class="alert-signal">TTM Squeeze Fire</div>
                    <div class="d-flex justify-content-between align-items-center mt-2">
                        <div>
                            <span class="timeframe-badge">5Min</span>
                            <span class="timeframe-badge">15Min</span>
                        </div>
                        <div class="text-primary" style="font-weight: 600;">$195.23</div>
                    </div>
                    <div class="mt-2">
                        <button class="btn-trading btn-buy me-1"><i class="fas fa-arrow-up"></i></button>
                        <button class="btn-trading btn-sell me-1"><i class="fas fa-arrow-down"></i></button>
                        <button class="btn btn-sm btn-outline-primary"><i class="fas fa-eye"></i></button>
                    </div>
                </div>

                <div class="alert-item">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <div class="alert-symbol">TSLA</div>
                        <div class="ai-gauge">
                            <div class="gauge-circle" style="background: conic-gradient(var(--accent-green) 331deg, var(--bg-tertiary) 0deg);">
                                <div class="gauge-inner">92</div>
                            </div>
                        </div>
                    </div>
                    <div class="alert-signal">Momentum Shift</div>
                    <div class="d-flex justify-content-between align-items-center mt-2">
                        <div>
                            <span class="timeframe-badge">1Hour</span>
                        </div>
                        <div class="text-primary" style="font-weight: 600;">$248.67</div>
                    </div>
                    <div class="mt-2">
                        <button class="btn-trading btn-buy me-1"><i class="fas fa-arrow-up"></i></button>
                        <button class="btn-trading btn-sell me-1"><i class="fas fa-arrow-down"></i></button>
                        <button class="btn btn-sm btn-outline-primary"><i class="fas fa-eye"></i></button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Market Analytics Panel -->
        <div class="grid-panel">
            <div class="panel-header">
                <h6 class="panel-title">Market Analytics & AI Insights</h6>
            </div>
            <div class="analytics-content">
                <div class="analytics-metric">
                    <span><i class="fas fa-chart-line me-2"></i>Market Sentiment:</span>
                    <span class="positive">Bullish (2.3:1)</span>
                </div>
                <div class="analytics-metric">
                    <span><i class="fas fa-thermometer-half me-2"></i>Volatility:</span>
                    <span class="neutral">Normal (22.45)</span>
                </div>
                <div class="analytics-metric">
                    <span><i class="fas fa-signal me-2"></i>Signal Accuracy:</span>
                    <span class="positive">78.3%</span>
                </div>
                <div class="analytics-metric">
                    <span><i class="fas fa-brain me-2"></i>AI Confidence:</span>
                    <span class="positive">87.2%</span>
                </div>
                <div class="analytics-metric">
                    <span><i class="fas fa-shield-alt me-2"></i>Risk Level:</span>
                    <span class="positive">Low</span>
                </div>
                <div class="analytics-metric">
                    <span><i class="fas fa-bullseye me-2"></i>Win Rate:</span>
                    <span class="positive">68.4%</span>
                </div>
                <div class="analytics-metric">
                    <span><i class="fas fa-clock me-2"></i>Avg Hold Time:</span>
                    <span class="neutral">2h 15m</span>
                </div>
                <div class="analytics-metric">
                    <span><i class="fas fa-dollar-sign me-2"></i>Avg Profit:</span>
                    <span class="positive">+$247</span>
                </div>
            </div>
        </div>

        <!-- Trading Panel -->
        <div class="grid-panel">
            <div class="panel-header">
                <h6 class="panel-title">Order Entry</h6>
            </div>
            <div class="order-panel">
                <div class="mb-3">
                    <label class="form-label">Symbol</label>
                    <input type="text" class="form-control" id="order-symbol" placeholder="AAPL" style="background: var(--bg-tertiary); border: 1px solid var(--border-color); color: var(--text-primary);">
                </div>
                <div class="mb-3">
                    <label class="form-label">Quantity</label>
                    <input type="number" class="form-control" id="order-quantity" value="100" style="background: var(--bg-tertiary); border: 1px solid var(--border-color); color: var(--text-primary);">
                </div>
                <div class="mb-3">
                    <label class="form-label">Order Type</label>
                    <select class="form-control" id="order-type" style="background: var(--bg-tertiary); border: 1px solid var(--border-color); color: var(--text-primary);">
                        <option>Market</option>
                        <option>Limit</option>
                        <option>Stop</option>
                    </select>
                </div>
                <div class="d-grid gap-2">
                    <button class="btn btn-trading btn-buy" onclick="placeOrder('buy')">
                        <i class="fas fa-arrow-up me-1"></i>BUY
                    </button>
                    <button class="btn btn-trading btn-sell" onclick="placeOrder('sell')">
                        <i class="fas fa-arrow-down me-1"></i>SELL
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        function updateTime() {
            const now = new Date();
            const timeStr = now.toLocaleTimeString('en-US', {
                timeZone: 'America/Chicago',
                hour12: true,
                hour: 'numeric',
                minute: '2-digit',
                second: '2-digit'
            }) + ' CST';
            document.getElementById('current-time').textContent = timeStr;
        }

        function refreshAlerts() {
            alert('Refreshing TTM Squeeze alerts...');
        }

        function placeOrder(side) {
            const symbol = document.getElementById('order-symbol').value || 'AAPL';
            const quantity = document.getElementById('order-quantity').value || '100';
            alert(`${side.toUpperCase()} order placed: ${quantity} ${symbol}`);
        }

        // Update time every second
        setInterval(updateTime, 1000);
        updateTime();

        // Simulate real-time updates
        setInterval(() => {
            const metrics = document.querySelectorAll('.metric-value');
            metrics.forEach(metric => {
                metric.style.transform = 'scale(1.05)';
                setTimeout(() => {
                    metric.style.transform = 'scale(1)';
                }, 200);
            });
        }, 3000);
    </script>
</body>
</html>
