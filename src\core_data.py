"""
Consolidated Data Management Module for TTM Squeeze Trading System
Combines Alpaca provider, FMP provider, and data management functionality
"""

import asyncio
import aiohttp
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta
import logging
import json
import time
from dataclasses import dataclass

logger = logging.getLogger(__name__)

# ============================================================================
# DATA STRUCTURES
# ============================================================================

@dataclass
class Quote:
    """Real-time quote data"""
    symbol: str
    price: float
    bid: float
    ask: float
    volume: int
    timestamp: datetime

@dataclass
class BarData:
    """OHLCV bar data"""
    symbol: str
    timestamp: datetime
    open: float
    high: float
    low: float
    close: float
    volume: int
    timeframe: str

# ============================================================================
# ALPACA DATA PROVIDER
# ============================================================================

class AlpacaDataProvider:
    """Alpaca Markets data provider for real-time and historical data"""
    
    def __init__(self, api_key: str, secret_key: str, base_url: str = "https://paper-api.alpaca.markets"):
        self.api_key = api_key
        self.secret_key = secret_key
        self.base_url = base_url
        self.session = None
        self.connected = False
        
        # Rate limiting
        self.last_request_time = 0
        self.min_request_interval = 0.1  # 100ms between requests
        
        logger.info(f"Alpaca provider initialized with base URL: {base_url}")
    
    async def connect(self) -> bool:
        """Connect to Alpaca API"""
        try:
            if not self.session:
                self.session = aiohttp.ClientSession(
                    headers={
                        'APCA-API-KEY-ID': self.api_key,
                        'APCA-API-SECRET-KEY': self.secret_key,
                        'Content-Type': 'application/json'
                    },
                    timeout=aiohttp.ClientTimeout(total=30)
                )
            
            # Test connection
            account_info = await self.get_account()
            if account_info:
                self.connected = True
                account_id = account_info.get('id', 'Unknown')
                logger.info(f"Connected to Alpaca API - Account: {account_id}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Failed to connect to Alpaca: {e}")
            return False
    
    async def get_account(self) -> Optional[Dict[str, Any]]:
        """Get account information"""
        try:
            await self._rate_limit()
            
            async with self.session.get(f"{self.base_url}/v2/account") as response:
                if response.status == 200:
                    return await response.json()
                else:
                    logger.error(f"Account request failed: {response.status}")
                    return None
                    
        except Exception as e:
            logger.error(f"Error getting account info: {e}")
            return None
    
    async def get_historical_data(self, symbol: str, timeframe: str, limit: int = 100) -> Optional[pd.DataFrame]:
        """Get historical bar data"""
        try:
            await self._rate_limit()
            
            # Map timeframes
            timeframe_map = {
                '5Min': '5Min',
                '15Min': '15Min',
                '30Min': '30Min',
                '1Hour': '1Hour',
                '1Day': '1Day'
            }
            
            alpaca_timeframe = timeframe_map.get(timeframe, '15Min')
            
            # Calculate start date
            if 'Min' in alpaca_timeframe:
                days_back = max(5, limit // 78)  # ~78 bars per day for minutes
            else:
                days_back = limit
            
            start_date = (datetime.now() - timedelta(days=days_back)).strftime('%Y-%m-%d')
            
            url = f"{self.base_url}/v2/stocks/{symbol}/bars"
            params = {
                'timeframe': alpaca_timeframe,
                'start': start_date,
                'limit': limit,
                'adjustment': 'raw',
                'feed': 'iex'
            }
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    bars = data.get('bars', [])
                    
                    if bars:
                        df = pd.DataFrame(bars)
                        df['timestamp'] = pd.to_datetime(df['t'])
                        df = df.rename(columns={
                            'o': 'open', 'h': 'high', 'l': 'low', 
                            'c': 'close', 'v': 'volume'
                        })
                        df = df[['timestamp', 'open', 'high', 'low', 'close', 'volume']]
                        df = df.sort_values('timestamp').reset_index(drop=True)
                        
                        logger.debug(f"Got {len(df)} periods for {symbol} {timeframe}")
                        return df
                    else:
                        logger.warning(f"No data returned for {symbol} {timeframe}")
                        return None
                else:
                    logger.error(f"Historical data request failed: {response.status}")
                    return None
                    
        except Exception as e:
            logger.error(f"Error getting historical data for {symbol}: {e}")
            return None
    
    async def get_real_time_quote(self, symbol: str) -> Optional[Quote]:
        """Get real-time quote"""
        try:
            await self._rate_limit()
            
            url = f"{self.base_url}/v2/stocks/{symbol}/quotes/latest"
            
            async with self.session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    quote_data = data.get('quote', {})
                    
                    return Quote(
                        symbol=symbol,
                        price=(quote_data.get('bp', 0) + quote_data.get('ap', 0)) / 2,
                        bid=quote_data.get('bp', 0),
                        ask=quote_data.get('ap', 0),
                        volume=quote_data.get('bs', 0) + quote_data.get('as', 0),
                        timestamp=datetime.now()
                    )
                else:
                    logger.error(f"Quote request failed: {response.status}")
                    return None
                    
        except Exception as e:
            logger.error(f"Error getting quote for {symbol}: {e}")
            return None
    
    async def _rate_limit(self):
        """Implement rate limiting"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < self.min_request_interval:
            await asyncio.sleep(self.min_request_interval - time_since_last)
        
        self.last_request_time = time.time()
    
    async def close(self):
        """Close the session"""
        if self.session:
            await self.session.close()
            self.session = None
            self.connected = False

# ============================================================================
# FMP DATA PROVIDER
# ============================================================================

class FMPDataProvider:
    """Financial Modeling Prep data provider"""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://financialmodelingprep.com/api/v3"
        self.session = None
        self.connected = False
        
        # Rate limiting
        self.last_request_time = 0
        self.min_request_interval = 0.2  # 200ms between requests
        
        logger.info("FMP provider initialized")
    
    async def connect(self) -> bool:
        """Connect to FMP API"""
        try:
            if not self.session:
                self.session = aiohttp.ClientSession(
                    timeout=aiohttp.ClientTimeout(total=30)
                )
            
            # Test connection
            test_data = await self.get_sp500_symbols()
            if test_data:
                self.connected = True
                logger.info("Connected to Financial Modeling Prep API")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Failed to connect to FMP: {e}")
            return False
    
    async def get_sp500_symbols(self) -> Optional[List[str]]:
        """Get S&P 500 symbols"""
        try:
            await self._rate_limit()
            
            url = f"{self.base_url}/sp500_constituent"
            params = {'apikey': self.api_key}
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    symbols = [item['symbol'] for item in data if 'symbol' in item]
                    logger.info(f"Retrieved {len(symbols)} S&P 500 symbols")
                    return symbols
                else:
                    logger.error(f"S&P 500 request failed: {response.status}")
                    return None
                    
        except Exception as e:
            logger.error(f"Error getting S&P 500 symbols: {e}")
            return None
    
    async def get_historical_data(self, symbol: str, timeframe: str, limit: int = 100) -> Optional[pd.DataFrame]:
        """Get historical data from FMP (fallback)"""
        try:
            await self._rate_limit()
            
            # FMP only supports daily data easily
            if timeframe != '1Day':
                return None
            
            url = f"{self.base_url}/historical-price-full/{symbol}"
            params = {
                'apikey': self.api_key,
                'timeseries': limit
            }
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    historical = data.get('historical', [])
                    
                    if historical:
                        df = pd.DataFrame(historical)
                        df['timestamp'] = pd.to_datetime(df['date'])
                        df = df[['timestamp', 'open', 'high', 'low', 'close', 'volume']]
                        df = df.sort_values('timestamp').reset_index(drop=True)
                        
                        return df
                    else:
                        return None
                else:
                    logger.error(f"FMP historical data request failed: {response.status}")
                    return None
                    
        except Exception as e:
            logger.error(f"Error getting FMP historical data for {symbol}: {e}")
            return None
    
    async def _rate_limit(self):
        """Implement rate limiting"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < self.min_request_interval:
            await asyncio.sleep(self.min_request_interval - time_since_last)
        
        self.last_request_time = time.time()
    
    async def close(self):
        """Close the session"""
        if self.session:
            await self.session.close()
            self.session = None
            self.connected = False

# ============================================================================
# UNIFIED DATA MANAGER
# ============================================================================

class DataManager:
    """Unified data manager combining multiple providers"""
    
    def __init__(self, alpaca_config: Dict[str, str], fmp_config: Dict[str, str]):
        # Initialize providers
        self.alpaca = AlpacaDataProvider(
            alpaca_config['api_key'],
            alpaca_config['secret_key'],
            alpaca_config.get('base_url', 'https://paper-api.alpaca.markets')
        )
        
        self.fmp = FMPDataProvider(fmp_config['api_key'])
        
        # Data cache
        self.cache = {}
        self.cache_expiry = timedelta(minutes=1)
        
        # Stock universe
        self.stock_universe = []
        self.universe_last_updated = None
        
        logger.info("Data manager initialized")
    
    async def initialize(self) -> bool:
        """Initialize all data providers"""
        try:
            # Connect to providers
            alpaca_connected = await self.alpaca.connect()
            fmp_connected = await self.fmp.connect()
            
            if alpaca_connected:
                logger.info("Alpaca provider initialized")
            
            if fmp_connected:
                logger.info("FMP provider initialized")
            
            # Load stock universe
            await self.refresh_stock_universe()
            
            return alpaca_connected or fmp_connected
            
        except Exception as e:
            logger.error(f"Error initializing data manager: {e}")
            return False
    
    async def refresh_stock_universe(self):
        """Refresh the stock universe"""
        try:
            # Get S&P 500 from FMP
            sp500_symbols = await self.fmp.get_sp500_symbols()
            
            if sp500_symbols:
                # Add additional large-cap stocks
                additional_stocks = [
                    'QQQ', 'SPY', 'IWM', 'VTI', 'VOO', 'VEA', 'VWO', 'AGG', 'BND',
                    'ARKK', 'ARKQ', 'ARKG', 'ARKW', 'ARKF', 'SQQQ', 'TQQQ', 'SPXL',
                    'UPRO', 'TMF', 'TLT', 'GLD', 'SLV', 'USO', 'UNG', 'VXX', 'UVXY',
                    'COIN', 'HOOD', 'SOFI', 'PLTR', 'SNOW', 'RBLX', 'RIVN', 'LCID',
                    'NKLA', 'SPCE', 'DKNG', 'PENN', 'FUBO', 'ROKU', 'ZM', 'DOCU',
                    'PTON', 'BYND', 'TDOC', 'CRSP', 'EDIT', 'NTLA', 'BEAM', 'PACB'
                ]
                
                self.stock_universe = list(set(sp500_symbols + additional_stocks))
                self.universe_last_updated = datetime.now()
                
                logger.info(f"Added {len(sp500_symbols)} S&P 500 stocks to universe")
                logger.info(f"Added {len(additional_stocks)} additional large-cap stocks")
                logger.info(f"Final universe contains {len(self.stock_universe)} stocks")
            else:
                # Fallback universe
                self.stock_universe = [
                    'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'META', 'NVDA', 'JPM', 'JNJ', 'V',
                    'PG', 'UNH', 'HD', 'MA', 'BAC', 'ABBV', 'PFE', 'KO', 'AVGO', 'PEP',
                    'TMO', 'COST', 'DIS', 'ABT', 'ACN', 'VZ', 'ADBE', 'DHR', 'NEE', 'LLY'
                ]
                logger.warning("Using fallback stock universe")
                
        except Exception as e:
            logger.error(f"Error refreshing stock universe: {e}")
    
    def get_stock_universe(self) -> List[str]:
        """Get the current stock universe"""
        return self.stock_universe.copy()
    
    async def get_historical_data(self, symbol: str, timeframe: str, limit: int = 100) -> Optional[pd.DataFrame]:
        """Get historical data with caching"""
        try:
            # Create cache key
            cache_key = f"{symbol}_{timeframe}_{limit}"
            
            # Check cache
            if cache_key in self.cache:
                cached_data, cached_time = self.cache[cache_key]
                if datetime.now() - cached_time < self.cache_expiry:
                    return cached_data
            
            # Try Alpaca first
            data = await self.alpaca.get_historical_data(symbol, timeframe, limit)
            
            # Fallback to FMP for daily data
            if data is None and timeframe == '1Day':
                data = await self.fmp.get_historical_data(symbol, timeframe, limit)
            
            # Cache the result
            if data is not None:
                self.cache[cache_key] = (data, datetime.now())
            
            return data
            
        except Exception as e:
            logger.error(f"Error getting historical data for {symbol}: {e}")
            return None
    
    async def get_real_time_quote(self, symbol: str) -> Optional[Quote]:
        """Get real-time quote"""
        try:
            return await self.alpaca.get_real_time_quote(symbol)
        except Exception as e:
            logger.error(f"Error getting real-time quote for {symbol}: {e}")
            return None
    
    async def close(self):
        """Close all connections"""
        await self.alpaca.close()
        await self.fmp.close()
        logger.info("Data manager closed")
