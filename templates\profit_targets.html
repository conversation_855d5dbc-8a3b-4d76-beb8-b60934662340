<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profit Target Trading - TTM Squeeze System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .target-card {
            border-left: 4px solid #007bff;
            transition: all 0.3s ease;
        }
        .target-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .target-achieved {
            border-left-color: #28a745;
            background-color: #f8fff9;
        }
        .target-conservative {
            border-left-color: #ffc107;
            background-color: #fffdf5;
        }
        .target-protection {
            border-left-color: #fd7e14;
            background-color: #fff8f5;
        }
        .progress-ring {
            transform: rotate(-90deg);
        }
        .progress-ring-circle {
            transition: stroke-dasharray 0.35s;
            transform: rotate(-90deg);
            transform-origin: 50% 50%;
        }
        .ai-score {
            background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 20px;
            padding: 4px 12px;
            font-size: 0.8em;
            font-weight: bold;
        }
        .execution-mode {
            font-size: 0.75em;
            padding: 2px 8px;
            border-radius: 12px;
            font-weight: bold;
        }
        .mode-normal { background-color: #e3f2fd; color: #1976d2; }
        .mode-conservative { background-color: #fff3e0; color: #f57c00; }
        .mode-aggressive { background-color: #ffebee; color: #d32f2f; }
        .mode-protection { background-color: #e8f5e8; color: #388e3c; }
        .mode-halted { background-color: #fafafa; color: #616161; }
        
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .control-panel {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-active { background-color: #28a745; }
        .status-paused { background-color: #ffc107; }
        .status-achieved { background-color: #17a2b8; }
        .status-halted { background-color: #dc3545; }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="fas fa-chart-line me-2"></i>TTM Squeeze System
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">Scanner</a>
                <a class="nav-link" href="/alerts">Alerts</a>
                <a class="nav-link active" href="/profit-targets">Profit Targets</a>
                <a class="nav-link" href="/performance">Performance</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Header with Controls -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h2><i class="fas fa-bullseye me-2"></i>Profit Target Trading</h2>
                    <div>
                        <button class="btn btn-success me-2" data-bs-toggle="modal" data-bs-target="#createTargetModal">
                            <i class="fas fa-plus me-1"></i>Create Target
                        </button>
                        <button class="btn btn-outline-primary me-2" onclick="refreshDashboard()">
                            <i class="fas fa-sync-alt me-1"></i>Refresh
                        </button>
                        <button class="btn btn-outline-danger" onclick="emergencyHalt()">
                            <i class="fas fa-stop me-1"></i>Emergency Halt
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- System Status -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="metric-card text-center">
                    <h3 id="activeTargets">0</h3>
                    <p class="mb-0">Active Targets</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card text-center">
                    <h3 id="achievementRate">0%</h3>
                    <p class="mb-0">Achievement Rate</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card text-center">
                    <h3 id="totalTrades">0</h3>
                    <p class="mb-0">Total Trades</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card text-center">
                    <h3 id="aiEfficiency">0%</h3>
                    <p class="mb-0">AI Efficiency</p>
                </div>
            </div>
        </div>

        <!-- Control Panel -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="control-panel">
                    <h5><i class="fas fa-cogs me-2"></i>System Controls</h5>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="targetTradingEnabled">
                                <label class="form-check-label" for="targetTradingEnabled">
                                    Target-Based Trading Enabled
                                </label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="aiOptimizationEnabled">
                                <label class="form-check-label" for="aiOptimizationEnabled">
                                    AI Optimization Enabled
                                </label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="rlLearningEnabled">
                                <label class="form-check-label" for="rlLearningEnabled">
                                    Reinforcement Learning
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Active Targets -->
        <div class="row">
            <div class="col-12">
                <h4><i class="fas fa-target me-2"></i>Active Targets</h4>
                <div id="targetsContainer" class="row">
                    <!-- Target cards will be populated here -->
                </div>
            </div>
        </div>

        <!-- Achievement History -->
        <div class="row mt-4">
            <div class="col-12">
                <h4><i class="fas fa-trophy me-2"></i>Recent Achievements</h4>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Target Name</th>
                                <th>Target Value</th>
                                <th>Achieved Value</th>
                                <th>Time to Achieve</th>
                                <th>Trades</th>
                                <th>Win Rate</th>
                                <th>AI Score</th>
                                <th>Date</th>
                            </tr>
                        </thead>
                        <tbody id="achievementHistory">
                            <!-- Achievement history will be populated here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Create Target Modal -->
    <div class="modal fade" id="createTargetModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Create New Profit Target</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="createTargetForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="targetName" class="form-label">Target Name</label>
                                    <input type="text" class="form-control" id="targetName" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="targetType" class="form-label">Target Type</label>
                                    <select class="form-select" id="targetType" required>
                                        <option value="absolute_dollar">Absolute Dollar Amount</option>
                                        <option value="percentage_account">Percentage of Account</option>
                                        <option value="percentage_daily">Daily Percentage</option>
                                        <option value="risk_adjusted">Risk Adjusted</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="targetValue" class="form-label">Target Value</label>
                                    <input type="number" class="form-control" id="targetValue" step="0.01" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="timeframe" class="form-label">Timeframe</label>
                                    <select class="form-select" id="timeframe" required>
                                        <option value="session">Trading Session</option>
                                        <option value="daily">Daily</option>
                                        <option value="weekly">Weekly</option>
                                        <option value="monthly">Monthly</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="maxDrawdown" class="form-label">Max Drawdown (%)</label>
                                    <input type="number" class="form-control" id="maxDrawdown" value="5" step="0.1" min="0" max="20">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="maxLoss" class="form-label">Max Loss Limit ($)</label>
                                    <input type="number" class="form-control" id="maxLoss" value="1000" step="1">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="conservativeThreshold" class="form-label">Conservative Threshold (%)</label>
                                    <input type="number" class="form-control" id="conservativeThreshold" value="80" step="1" min="50" max="95">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check form-switch mt-4">
                                    <input class="form-check-input" type="checkbox" id="haltOnAchievement" checked>
                                    <label class="form-check-label" for="haltOnAchievement">
                                        Halt Trading on Achievement
                                    </label>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="createTarget()">Create Target</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Global variables
        let targetsData = {};
        let refreshInterval;

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            loadDashboard();
            startAutoRefresh();
        });

        function loadDashboard() {
            Promise.all([
                fetch('/api/profit-targets/status').then(r => r.json()),
                fetch('/api/profit-targets/performance').then(r => r.json()),
                fetch('/api/profit-targets/achievements').then(r => r.json())
            ]).then(([status, performance, achievements]) => {
                updateSystemMetrics(performance);
                updateTargets(status.targets || {});
                updateAchievementHistory(achievements.history || []);
                updateSystemControls(status.config || {});
            }).catch(error => {
                console.error('Error loading dashboard:', error);
                showAlert('Error loading dashboard data', 'danger');
            });
        }

        function updateSystemMetrics(performance) {
            document.getElementById('activeTargets').textContent = performance.total_targets || 0;
            document.getElementById('achievementRate').textContent = (performance.achievement_rate || 0).toFixed(1) + '%';
            document.getElementById('totalTrades').textContent = performance.total_target_trades || 0;
            document.getElementById('aiEfficiency').textContent = (performance.avg_ai_efficiency || 0).toFixed(1) + '%';
        }

        function updateTargets(targets) {
            targetsData = targets;
            const container = document.getElementById('targetsContainer');
            container.innerHTML = '';

            Object.values(targets).forEach(target => {
                const targetCard = createTargetCard(target);
                container.appendChild(targetCard);
            });
        }

        function createTargetCard(target) {
            const col = document.createElement('div');
            col.className = 'col-md-6 col-lg-4 mb-3';

            const progress = target.current_progress || {};
            const completionPct = progress.completion_percentage || 0;
            const isAchieved = progress.is_achieved || false;
            const isConservative = progress.is_conservative_mode || false;
            const executionMode = target.execution_mode || 'normal';

            let cardClass = 'target-card';
            if (isAchieved) cardClass += ' target-achieved';
            else if (isConservative) cardClass += ' target-conservative';
            else if (executionMode === 'profit_protection') cardClass += ' target-protection';

            col.innerHTML = `
                <div class="card ${cardClass}">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <h6 class="card-title mb-0">${target.target_name}</h6>
                            <span class="execution-mode mode-${executionMode}">${executionMode.toUpperCase()}</span>
                        </div>
                        
                        <div class="row align-items-center mb-3">
                            <div class="col-8">
                                <div class="progress" style="height: 8px;">
                                    <div class="progress-bar ${isAchieved ? 'bg-success' : isConservative ? 'bg-warning' : 'bg-primary'}" 
                                         style="width: ${Math.min(100, completionPct)}%"></div>
                                </div>
                                <small class="text-muted">${completionPct.toFixed(1)}% Complete</small>
                            </div>
                            <div class="col-4 text-end">
                                <div class="ai-score">${(target.achievement_probability || 0).toFixed(0)}%</div>
                                <small class="text-muted">AI Score</small>
                            </div>
                        </div>

                        <div class="row text-center">
                            <div class="col-4">
                                <strong>$${(progress.target_amount || 0).toLocaleString()}</strong>
                                <br><small class="text-muted">Target</small>
                            </div>
                            <div class="col-4">
                                <strong>$${(progress.current_progress || 0).toLocaleString()}</strong>
                                <br><small class="text-muted">Progress</small>
                            </div>
                            <div class="col-4">
                                <strong>$${(progress.remaining_amount || 0).toLocaleString()}</strong>
                                <br><small class="text-muted">Remaining</small>
                            </div>
                        </div>

                        <div class="row mt-3 text-center">
                            <div class="col-6">
                                <strong>${target.trades_executed || 0}</strong>
                                <br><small class="text-muted">Trades</small>
                            </div>
                            <div class="col-6">
                                <strong>${formatTimeRemaining(progress.time_remaining)}</strong>
                                <br><small class="text-muted">Time Left</small>
                            </div>
                        </div>

                        <div class="mt-3">
                            <button class="btn btn-sm btn-outline-primary me-1" onclick="viewTargetDetails('${target.target_id}')">
                                <i class="fas fa-eye"></i> Details
                            </button>
                            <button class="btn btn-sm btn-outline-warning me-1" onclick="pauseTarget('${target.target_id}')">
                                <i class="fas fa-pause"></i> Pause
                            </button>
                            <button class="btn btn-sm btn-outline-danger" onclick="cancelTarget('${target.target_id}')">
                                <i class="fas fa-times"></i> Cancel
                            </button>
                        </div>
                    </div>
                </div>
            `;

            return col;
        }

        function updateAchievementHistory(achievements) {
            const tbody = document.getElementById('achievementHistory');
            tbody.innerHTML = '';

            achievements.slice(0, 10).forEach(achievement => {
                const row = tbody.insertRow();
                row.innerHTML = `
                    <td>${achievement.target_name}</td>
                    <td>$${achievement.target_value.toLocaleString()}</td>
                    <td>$${achievement.achieved_value.toLocaleString()}</td>
                    <td>${achievement.time_to_achieve}</td>
                    <td>${achievement.trades_count}</td>
                    <td>${(achievement.win_rate * 100).toFixed(1)}%</td>
                    <td>${achievement.ai_efficiency_score.toFixed(1)}</td>
                    <td>${new Date(achievement.achievement_date).toLocaleDateString()}</td>
                `;
            });
        }

        function updateSystemControls(config) {
            document.getElementById('targetTradingEnabled').checked = config.enabled || false;
            document.getElementById('aiOptimizationEnabled').checked = config.ai_optimization || false;
            document.getElementById('rlLearningEnabled').checked = config.rl_learning || false;
        }

        function formatTimeRemaining(timeStr) {
            if (!timeStr) return 'N/A';
            // Parse and format time remaining
            return timeStr.replace(':', 'h ') + 'm';
        }

        function createTarget() {
            const form = document.getElementById('createTargetForm');
            const formData = new FormData(form);
            
            const targetConfig = {
                name: document.getElementById('targetName').value,
                target_type: document.getElementById('targetType').value,
                target_value: parseFloat(document.getElementById('targetValue').value),
                timeframe: document.getElementById('timeframe').value,
                max_drawdown_pct: parseFloat(document.getElementById('maxDrawdown').value) / 100,
                max_loss_limit: parseFloat(document.getElementById('maxLoss').value),
                conservative_threshold: parseFloat(document.getElementById('conservativeThreshold').value) / 100,
                halt_on_achievement: document.getElementById('haltOnAchievement').checked
            };

            fetch('/api/profit-targets/create', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify(targetConfig)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('Target created successfully!', 'success');
                    bootstrap.Modal.getInstance(document.getElementById('createTargetModal')).hide();
                    form.reset();
                    loadDashboard();
                } else {
                    showAlert('Error creating target: ' + data.error, 'danger');
                }
            })
            .catch(error => {
                console.error('Error creating target:', error);
                showAlert('Error creating target', 'danger');
            });
        }

        function pauseTarget(targetId) {
            fetch(`/api/profit-targets/${targetId}/pause`, {method: 'POST'})
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('Target paused', 'info');
                    loadDashboard();
                } else {
                    showAlert('Error pausing target', 'danger');
                }
            });
        }

        function cancelTarget(targetId) {
            if (confirm('Are you sure you want to cancel this target?')) {
                fetch(`/api/profit-targets/${targetId}/cancel`, {method: 'POST'})
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showAlert('Target cancelled', 'info');
                        loadDashboard();
                    } else {
                        showAlert('Error cancelling target', 'danger');
                    }
                });
            }
        }

        function emergencyHalt() {
            if (confirm('Emergency halt will stop all target-based trading. Continue?')) {
                fetch('/api/profit-targets/emergency-halt', {method: 'POST'})
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showAlert('Emergency halt activated!', 'warning');
                        loadDashboard();
                    }
                });
            }
        }

        function refreshDashboard() {
            loadDashboard();
            showAlert('Dashboard refreshed', 'info');
        }

        function startAutoRefresh() {
            refreshInterval = setInterval(loadDashboard, 30000); // Refresh every 30 seconds
        }

        function showAlert(message, type) {
            // Create and show bootstrap alert
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(alertDiv);
            
            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.parentNode.removeChild(alertDiv);
                }
            }, 5000);
        }

        // System control event listeners
        document.getElementById('targetTradingEnabled').addEventListener('change', function() {
            updateSystemConfig('enabled', this.checked);
        });

        document.getElementById('aiOptimizationEnabled').addEventListener('change', function() {
            updateSystemConfig('ai_optimization', this.checked);
        });

        document.getElementById('rlLearningEnabled').addEventListener('change', function() {
            updateSystemConfig('rl_learning', this.checked);
        });

        function updateSystemConfig(key, value) {
            fetch('/api/profit-targets/config', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({[key]: value})
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert(`${key} ${value ? 'enabled' : 'disabled'}`, 'info');
                }
            });
        }
    </script>
</body>
</html>
