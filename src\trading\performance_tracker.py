"""
Performance Tracking System for AI-Enhanced Trading
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
import logging
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
import json
import os
from collections import defaultdict

logger = logging.getLogger(__name__)

@dataclass
class TradePerformance:
    """Individual trade performance metrics"""
    trade_id: str
    symbol: str
    entry_date: datetime
    exit_date: datetime
    entry_price: float
    exit_price: float
    quantity: int
    pnl: float
    pnl_pct: float
    hold_time_hours: float
    ai_signal_score: float
    ai_confidence: float
    market_regime: str
    stop_loss_triggered: bool
    max_favorable_excursion: float
    max_adverse_excursion: float

@dataclass
class AIModelPerformance:
    """AI model effectiveness metrics"""
    model_name: str
    prediction_accuracy: float
    signal_quality_correlation: float
    false_positive_rate: float
    false_negative_rate: float
    avg_signal_score: float
    profitable_signals_pct: float
    total_signals: int
    last_updated: datetime

@dataclass
class PerformanceMetrics:
    """Comprehensive performance metrics"""
    total_trades: int
    winning_trades: int
    losing_trades: int
    win_rate: float
    avg_win: float
    avg_loss: float
    profit_factor: float
    sharpe_ratio: float
    max_drawdown: float
    total_return: float
    annualized_return: float
    volatility: float
    calmar_ratio: float

class PerformanceTracker:
    """Comprehensive performance tracking and analysis system"""
    
    def __init__(self):
        self.trades_history = []
        self.ai_model_metrics = {}
        self.daily_performance = {}
        
        # Performance data files
        self.trades_file = "data/trades_history.json"
        self.performance_file = "data/performance_metrics.json"
        self.ai_metrics_file = "data/ai_model_metrics.json"
        
        # Ensure data directory exists
        os.makedirs("data", exist_ok=True)
        
        # Load existing data
        self._load_historical_data()
        
        logger.info("Performance Tracker initialized")
    
    def record_trade(self, trade_data: Dict):
        """Record a completed trade"""
        try:
            # Calculate performance metrics
            entry_price = trade_data['entry_price']
            exit_price = trade_data['exit_price']
            quantity = trade_data['quantity']
            
            pnl = (exit_price - entry_price) * quantity
            pnl_pct = (exit_price - entry_price) / entry_price
            
            entry_date = trade_data['entry_date']
            exit_date = trade_data['exit_date']
            hold_time = (exit_date - entry_date).total_seconds() / 3600  # hours
            
            # Create trade performance record
            trade_perf = TradePerformance(
                trade_id=trade_data['trade_id'],
                symbol=trade_data['symbol'],
                entry_date=entry_date,
                exit_date=exit_date,
                entry_price=entry_price,
                exit_price=exit_price,
                quantity=quantity,
                pnl=pnl,
                pnl_pct=pnl_pct,
                hold_time_hours=hold_time,
                ai_signal_score=trade_data.get('ai_signal_score', 0.0),
                ai_confidence=trade_data.get('ai_confidence', 0.0),
                market_regime=trade_data.get('market_regime', 'unknown'),
                stop_loss_triggered=trade_data.get('stop_loss_triggered', False),
                max_favorable_excursion=trade_data.get('max_favorable_excursion', 0.0),
                max_adverse_excursion=trade_data.get('max_adverse_excursion', 0.0)
            )
            
            self.trades_history.append(trade_perf)
            
            # Update daily performance
            trade_date = exit_date.date()
            if trade_date not in self.daily_performance:
                self.daily_performance[trade_date] = {
                    'trades': 0, 'pnl': 0.0, 'winning_trades': 0
                }
            
            self.daily_performance[trade_date]['trades'] += 1
            self.daily_performance[trade_date]['pnl'] += pnl
            if pnl > 0:
                self.daily_performance[trade_date]['winning_trades'] += 1
            
            # Save to file
            self._save_trade_data(trade_perf)
            
            logger.info(f"Trade recorded: {trade_data['symbol']} - "
                       f"P&L: ${pnl:.2f} ({pnl_pct:.2%})")
            
        except Exception as e:
            logger.error(f"Error recording trade: {e}")
    
    def update_ai_model_performance(self, model_name: str, 
                                  predictions: List[Dict],
                                  actual_outcomes: List[Dict]):
        """Update AI model performance metrics"""
        try:
            if len(predictions) != len(actual_outcomes):
                logger.warning("Predictions and outcomes length mismatch")
                return
            
            # Calculate accuracy metrics
            correct_predictions = 0
            total_predictions = len(predictions)
            signal_scores = []
            profitable_signals = 0
            
            for pred, outcome in zip(predictions, actual_outcomes):
                # Check if prediction direction was correct
                pred_direction = 1 if pred['signal_score'] > 50 else -1
                actual_direction = 1 if outcome['pnl'] > 0 else -1
                
                if pred_direction == actual_direction:
                    correct_predictions += 1
                
                signal_scores.append(pred['signal_score'])
                
                if outcome['pnl'] > 0:
                    profitable_signals += 1
            
            # Calculate metrics
            accuracy = correct_predictions / total_predictions if total_predictions > 0 else 0
            avg_signal_score = np.mean(signal_scores) if signal_scores else 0
            profitable_pct = profitable_signals / total_predictions if total_predictions > 0 else 0
            
            # Calculate correlation between signal score and outcome
            if len(signal_scores) > 1:
                outcomes_pnl = [o['pnl'] for o in actual_outcomes]
                correlation = np.corrcoef(signal_scores, outcomes_pnl)[0, 1]
                correlation = correlation if not np.isnan(correlation) else 0
            else:
                correlation = 0
            
            # Update model metrics
            self.ai_model_metrics[model_name] = AIModelPerformance(
                model_name=model_name,
                prediction_accuracy=accuracy,
                signal_quality_correlation=correlation,
                false_positive_rate=self._calculate_false_positive_rate(predictions, actual_outcomes),
                false_negative_rate=self._calculate_false_negative_rate(predictions, actual_outcomes),
                avg_signal_score=avg_signal_score,
                profitable_signals_pct=profitable_pct,
                total_signals=total_predictions,
                last_updated=datetime.now()
            )
            
            # Save AI metrics
            self._save_ai_metrics()
            
            logger.info(f"AI model performance updated: {model_name} - "
                       f"Accuracy: {accuracy:.2%}, Correlation: {correlation:.3f}")
            
        except Exception as e:
            logger.error(f"Error updating AI model performance: {e}")
    
    def calculate_performance_metrics(self, start_date: Optional[datetime] = None,
                                    end_date: Optional[datetime] = None) -> PerformanceMetrics:
        """Calculate comprehensive performance metrics"""
        try:
            # Filter trades by date range
            trades = self.trades_history
            if start_date:
                trades = [t for t in trades if t.exit_date >= start_date]
            if end_date:
                trades = [t for t in trades if t.exit_date <= end_date]
            
            if not trades:
                return self._get_empty_metrics()
            
            # Basic metrics
            total_trades = len(trades)
            winning_trades = len([t for t in trades if t.pnl > 0])
            losing_trades = total_trades - winning_trades
            win_rate = winning_trades / total_trades if total_trades > 0 else 0
            
            # P&L metrics
            wins = [t.pnl for t in trades if t.pnl > 0]
            losses = [t.pnl for t in trades if t.pnl < 0]
            
            avg_win = np.mean(wins) if wins else 0
            avg_loss = np.mean(losses) if losses else 0
            
            total_pnl = sum(t.pnl for t in trades)
            
            # Profit factor
            gross_profit = sum(wins) if wins else 0
            gross_loss = abs(sum(losses)) if losses else 1
            profit_factor = gross_profit / gross_loss if gross_loss > 0 else 0
            
            # Time-based metrics
            if len(trades) > 1:
                returns = [t.pnl_pct for t in trades]
                
                # Sharpe ratio (assuming daily returns)
                avg_return = np.mean(returns)
                return_std = np.std(returns)
                sharpe_ratio = avg_return / return_std if return_std > 0 else 0
                
                # Drawdown calculation
                cumulative_returns = np.cumsum(returns)
                running_max = np.maximum.accumulate(cumulative_returns)
                drawdowns = cumulative_returns - running_max
                max_drawdown = abs(np.min(drawdowns)) if len(drawdowns) > 0 else 0
                
                # Annualized metrics
                days_trading = (trades[-1].exit_date - trades[0].exit_date).days
                if days_trading > 0:
                    total_return = cumulative_returns[-1]
                    annualized_return = (1 + total_return) ** (365 / days_trading) - 1
                    volatility = return_std * np.sqrt(252)  # Annualized volatility
                    calmar_ratio = annualized_return / max_drawdown if max_drawdown > 0 else 0
                else:
                    total_return = sum(returns)
                    annualized_return = 0
                    volatility = 0
                    calmar_ratio = 0
            else:
                sharpe_ratio = 0
                max_drawdown = 0
                total_return = trades[0].pnl_pct if trades else 0
                annualized_return = 0
                volatility = 0
                calmar_ratio = 0
            
            return PerformanceMetrics(
                total_trades=total_trades,
                winning_trades=winning_trades,
                losing_trades=losing_trades,
                win_rate=win_rate,
                avg_win=avg_win,
                avg_loss=avg_loss,
                profit_factor=profit_factor,
                sharpe_ratio=sharpe_ratio,
                max_drawdown=max_drawdown,
                total_return=total_return,
                annualized_return=annualized_return,
                volatility=volatility,
                calmar_ratio=calmar_ratio
            )
            
        except Exception as e:
            logger.error(f"Error calculating performance metrics: {e}")
            return self._get_empty_metrics()
    
    def get_ai_model_effectiveness(self) -> Dict[str, Dict]:
        """Get AI model effectiveness summary"""
        try:
            effectiveness = {}
            
            for model_name, metrics in self.ai_model_metrics.items():
                effectiveness[model_name] = {
                    'accuracy': metrics.prediction_accuracy,
                    'correlation': metrics.signal_quality_correlation,
                    'profitable_signals_pct': metrics.profitable_signals_pct,
                    'avg_signal_score': metrics.avg_signal_score,
                    'total_signals': metrics.total_signals,
                    'false_positive_rate': metrics.false_positive_rate,
                    'false_negative_rate': metrics.false_negative_rate,
                    'last_updated': metrics.last_updated.isoformat()
                }
            
            return effectiveness
            
        except Exception as e:
            logger.error(f"Error getting AI model effectiveness: {e}")
            return {}
    
    def get_performance_by_signal_score(self, score_ranges: List[Tuple[float, float]]) -> Dict:
        """Analyze performance by AI signal score ranges"""
        try:
            performance_by_score = {}
            
            for min_score, max_score in score_ranges:
                range_trades = [
                    t for t in self.trades_history 
                    if min_score <= t.ai_signal_score < max_score
                ]
                
                if range_trades:
                    win_rate = len([t for t in range_trades if t.pnl > 0]) / len(range_trades)
                    avg_pnl = np.mean([t.pnl for t in range_trades])
                    avg_pnl_pct = np.mean([t.pnl_pct for t in range_trades])
                    
                    performance_by_score[f"{min_score}-{max_score}"] = {
                        'trade_count': len(range_trades),
                        'win_rate': win_rate,
                        'avg_pnl': avg_pnl,
                        'avg_pnl_pct': avg_pnl_pct
                    }
            
            return performance_by_score
            
        except Exception as e:
            logger.error(f"Error analyzing performance by signal score: {e}")
            return {}
    
    def get_performance_by_market_regime(self) -> Dict:
        """Analyze performance by market regime"""
        try:
            regime_performance = defaultdict(list)
            
            for trade in self.trades_history:
                regime_performance[trade.market_regime].append(trade.pnl_pct)
            
            results = {}
            for regime, returns in regime_performance.items():
                if returns:
                    results[regime] = {
                        'trade_count': len(returns),
                        'win_rate': len([r for r in returns if r > 0]) / len(returns),
                        'avg_return': np.mean(returns),
                        'volatility': np.std(returns),
                        'best_trade': max(returns),
                        'worst_trade': min(returns)
                    }
            
            return results
            
        except Exception as e:
            logger.error(f"Error analyzing performance by market regime: {e}")
            return {}
    
    def _calculate_false_positive_rate(self, predictions: List[Dict], 
                                     outcomes: List[Dict]) -> float:
        """Calculate false positive rate"""
        try:
            false_positives = 0
            total_negative_outcomes = 0
            
            for pred, outcome in zip(predictions, outcomes):
                if outcome['pnl'] <= 0:  # Actual negative outcome
                    total_negative_outcomes += 1
                    if pred['signal_score'] > 50:  # Predicted positive
                        false_positives += 1
            
            return false_positives / total_negative_outcomes if total_negative_outcomes > 0 else 0
            
        except Exception:
            return 0.0
    
    def _calculate_false_negative_rate(self, predictions: List[Dict],
                                     outcomes: List[Dict]) -> float:
        """Calculate false negative rate"""
        try:
            false_negatives = 0
            total_positive_outcomes = 0
            
            for pred, outcome in zip(predictions, outcomes):
                if outcome['pnl'] > 0:  # Actual positive outcome
                    total_positive_outcomes += 1
                    if pred['signal_score'] <= 50:  # Predicted negative
                        false_negatives += 1
            
            return false_negatives / total_positive_outcomes if total_positive_outcomes > 0 else 0
            
        except Exception:
            return 0.0
    
    def _get_empty_metrics(self) -> PerformanceMetrics:
        """Get empty performance metrics"""
        return PerformanceMetrics(
            total_trades=0, winning_trades=0, losing_trades=0, win_rate=0.0,
            avg_win=0.0, avg_loss=0.0, profit_factor=0.0, sharpe_ratio=0.0,
            max_drawdown=0.0, total_return=0.0, annualized_return=0.0,
            volatility=0.0, calmar_ratio=0.0
        )
    
    def _save_trade_data(self, trade: TradePerformance):
        """Save trade data to file"""
        try:
            with open(self.trades_file, 'a') as f:
                trade_dict = asdict(trade)
                trade_dict['entry_date'] = trade.entry_date.isoformat()
                trade_dict['exit_date'] = trade.exit_date.isoformat()
                f.write(json.dumps(trade_dict) + '\n')
        except Exception as e:
            logger.error(f"Error saving trade data: {e}")
    
    def _save_ai_metrics(self):
        """Save AI model metrics to file"""
        try:
            metrics_dict = {}
            for model_name, metrics in self.ai_model_metrics.items():
                metrics_dict[model_name] = asdict(metrics)
                metrics_dict[model_name]['last_updated'] = metrics.last_updated.isoformat()
            
            with open(self.ai_metrics_file, 'w') as f:
                json.dump(metrics_dict, f, indent=2)
        except Exception as e:
            logger.error(f"Error saving AI metrics: {e}")
    
    def _load_historical_data(self):
        """Load historical performance data"""
        try:
            # Load trades history
            if os.path.exists(self.trades_file):
                with open(self.trades_file, 'r') as f:
                    for line in f:
                        trade_dict = json.loads(line.strip())
                        trade_dict['entry_date'] = datetime.fromisoformat(trade_dict['entry_date'])
                        trade_dict['exit_date'] = datetime.fromisoformat(trade_dict['exit_date'])
                        trade = TradePerformance(**trade_dict)
                        self.trades_history.append(trade)
            
            # Load AI metrics
            if os.path.exists(self.ai_metrics_file):
                with open(self.ai_metrics_file, 'r') as f:
                    metrics_dict = json.load(f)
                    for model_name, metrics_data in metrics_dict.items():
                        metrics_data['last_updated'] = datetime.fromisoformat(metrics_data['last_updated'])
                        self.ai_model_metrics[model_name] = AIModelPerformance(**metrics_data)
            
            logger.info(f"Loaded {len(self.trades_history)} historical trades")
            
        except Exception as e:
            logger.error(f"Error loading historical data: {e}")
    
    def export_performance_report(self, filepath: str):
        """Export comprehensive performance report"""
        try:
            metrics = self.calculate_performance_metrics()
            ai_effectiveness = self.get_ai_model_effectiveness()
            signal_performance = self.get_performance_by_signal_score([
                (0, 50), (50, 70), (70, 85), (85, 100)
            ])
            regime_performance = self.get_performance_by_market_regime()
            
            report = {
                'generated_at': datetime.now().isoformat(),
                'overall_performance': asdict(metrics),
                'ai_model_effectiveness': ai_effectiveness,
                'performance_by_signal_score': signal_performance,
                'performance_by_market_regime': regime_performance,
                'recent_trades': [asdict(t) for t in self.trades_history[-10:]]
            }
            
            with open(filepath, 'w') as f:
                json.dump(report, f, indent=2, default=str)
            
            logger.info(f"Performance report exported to {filepath}")
            
        except Exception as e:
            logger.error(f"Error exporting performance report: {e}")
    
    def get_summary_stats(self) -> Dict:
        """Get summary statistics"""
        metrics = self.calculate_performance_metrics()
        
        return {
            'total_trades': metrics.total_trades,
            'win_rate': f"{metrics.win_rate:.1%}",
            'profit_factor': f"{metrics.profit_factor:.2f}",
            'sharpe_ratio': f"{metrics.sharpe_ratio:.2f}",
            'max_drawdown': f"{metrics.max_drawdown:.1%}",
            'total_return': f"{metrics.total_return:.1%}",
            'ai_models_tracked': len(self.ai_model_metrics),
            'data_points': len(self.trades_history)
        }
