"""
Consolidated Real-time Scanner Module for TTM Squeeze Trading System
Combines real-time scanning, stock screening, and alert generation
Focus on 5Min and 15Min timeframes only
"""

import asyncio
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from collections import defaultdict, deque
import logging
import json

from core_indicators import MultiTimeframeAnalyzer, SignalFilter
from ai_intelligence import AISignalEnhancer, TradingSignal

logger = logging.getLogger(__name__)

# ============================================================================
# REAL-TIME SCANNER
# ============================================================================

class RealTimeScanner:
    """Real-time TTM Squeeze scanner focused on intraday patterns"""
    
    def __init__(self, data_manager):
        self.data_manager = data_manager
        self.analyzer = MultiTimeframeAnalyzer()
        self.signal_filter = SignalFilter()
        self.ai_enhancer = AISignalEnhancer()
        
        # Scanner configuration - OPTIMIZED FOR INTRADAY
        self.scan_interval = 30  # Scan every 30 seconds
        self.timeframes = ['5Min', '15Min']  # Only intraday timeframes
        self.max_concurrent_scans = 20  # Process 20 stocks at once
        
        # State management
        self.is_running = False
        self.current_alerts = deque(maxlen=1000)  # Keep last 1000 alerts
        self.scan_stats = {
            'total_scans': 0,
            'alerts_generated': 0,
            'last_scan_time': None,
            'scan_duration': 0,
            'stocks_scanned': 0
        }
        
        # Alert management
        self.alert_history = defaultdict(list)
        self.alert_cooldown = timedelta(minutes=15)  # 15-minute cooldown per symbol
        
        logger.info(f"Real-time scanner initialized - Timeframes: {', '.join(self.timeframes)}")
    
    async def start_scanning(self):
        """Start the real-time scanning process"""
        if self.is_running:
            logger.warning("Scanner is already running")
            return
        
        self.is_running = True
        logger.info("Starting real-time TTM Squeeze scanner...")
        
        try:
            while self.is_running:
                scan_start = datetime.now()
                
                # Perform scan
                await self._perform_scan()
                
                # Update stats
                scan_duration = (datetime.now() - scan_start).total_seconds()
                self.scan_stats['scan_duration'] = scan_duration
                self.scan_stats['last_scan_time'] = scan_start
                self.scan_stats['total_scans'] += 1
                
                # Log scan completion
                alerts_count = len([a for a in self.current_alerts if a['timestamp'] > scan_start])
                logger.info(f"Scan completed in {scan_duration:.1f}s - {alerts_count} new alerts")
                
                # Wait for next scan
                await asyncio.sleep(self.scan_interval)
                
        except Exception as e:
            logger.error(f"Error in scanning loop: {e}")
        finally:
            self.is_running = False
            logger.info("Real-time scanner stopped")
    
    def stop_scanning(self):
        """Stop the scanning process"""
        self.is_running = False
        logger.info("Stopping real-time scanner...")
    
    async def _perform_scan(self):
        """Perform a single scan cycle"""
        try:
            # Get stock universe
            stock_universe = self.data_manager.get_stock_universe()
            
            if not stock_universe:
                logger.warning("No stocks in universe to scan")
                return
            
            # Process stocks in batches
            batch_size = self.max_concurrent_scans
            total_alerts = 0
            
            for i in range(0, len(stock_universe), batch_size):
                batch = stock_universe[i:i + batch_size]
                
                # Process batch concurrently
                batch_results = await self._scan_batch(batch)
                
                # Process results
                for result in batch_results:
                    if result and not result.get('error'):
                        alerts = await self._process_scan_result(result)
                        total_alerts += len(alerts)
            
            # Update stats
            self.scan_stats['stocks_scanned'] = len(stock_universe)
            self.scan_stats['alerts_generated'] += total_alerts
            
            logger.debug(f"Scanned {len(stock_universe)} stocks, generated {total_alerts} alerts")
            
        except Exception as e:
            logger.error(f"Error performing scan: {e}")
    
    async def _scan_batch(self, symbols: List[str]) -> List[Dict[str, Any]]:
        """Scan a batch of symbols concurrently"""
        try:
            # Create tasks for concurrent processing
            tasks = [self.analyzer.analyze_symbol(symbol, self.data_manager) for symbol in symbols]
            
            # Execute with timeout
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Filter out exceptions
            valid_results = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.debug(f"Error scanning {symbols[i]}: {result}")
                else:
                    valid_results.append(result)
            
            return valid_results
            
        except Exception as e:
            logger.error(f"Error scanning batch: {e}")
            return []
    
    async def _process_scan_result(self, result: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Process scan result and generate alerts"""
        try:
            symbol = result['symbol']
            
            # Check if symbol has signals
            if result['confirmations'] == 0:
                return []
            
            # Check cooldown
            if self._is_symbol_in_cooldown(symbol):
                return []
            
            # Filter signals
            filtered_results = self.signal_filter.filter_signals([result])
            
            if not filtered_results:
                return []
            
            # Generate alerts
            alerts = []
            for filtered_result in filtered_results:
                alert = await self._create_alert(filtered_result)
                if alert:
                    alerts.append(alert)
                    self.current_alerts.append(alert)
                    
                    # Update cooldown
                    self.alert_history[symbol].append(datetime.now())
            
            return alerts
            
        except Exception as e:
            logger.error(f"Error processing scan result: {e}")
            return []
    
    def _is_symbol_in_cooldown(self, symbol: str) -> bool:
        """Check if symbol is in alert cooldown period"""
        try:
            recent_alerts = self.alert_history[symbol]
            if not recent_alerts:
                return False
            
            # Check if last alert was within cooldown period
            last_alert = recent_alerts[-1]
            return datetime.now() - last_alert < self.alert_cooldown
            
        except Exception as e:
            logger.error(f"Error checking cooldown for {symbol}: {e}")
            return False
    
    async def _create_alert(self, scan_result: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Create alert from scan result"""
        try:
            symbol = scan_result['symbol']
            signals = scan_result['signals']
            timeframes = scan_result['timeframes']
            confirmations = scan_result['confirmations']
            
            # Get the best signal
            best_signal = max(signals, key=lambda s: s.get('signal_strength', 0))
            
            # Enhance signal with AI
            raw_signal = {
                'symbol': symbol,
                'signal_type': best_signal['signal_type'],
                'timeframes': timeframes,
                'current_price': best_signal['price'],
                'signal_strength': best_signal['signal_strength'],
                'momentum': best_signal.get('momentum', 0),
                'volume_confirmation': True,  # Assume volume confirmation
                'trend_alignment': confirmations >= 2
            }
            
            enhanced_signal = await self.ai_enhancer.enhance_signal(raw_signal)
            
            # Create alert
            alert = {
                'id': f"{symbol}_{datetime.now().timestamp()}",
                'symbol': symbol,
                'signal_type': enhanced_signal.signal_type,
                'timeframes': timeframes,
                'confirmations': confirmations,
                'required_confirmations': 1,  # Reduced requirement
                'price': enhanced_signal.entry_price,
                'confidence': enhanced_signal.confidence,
                'ai_score': enhanced_signal.ai_score,
                'risk_level': enhanced_signal.risk_level,
                'reasoning': enhanced_signal.reasoning,
                'timestamp': datetime.now(),
                'momentum': best_signal.get('momentum', 0),
                'signal_strength': best_signal.get('signal_strength', 0)
            }
            
            return alert
            
        except Exception as e:
            logger.error(f"Error creating alert: {e}")
            return None
    
    def get_recent_alerts(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Get recent alerts"""
        try:
            # Convert deque to list and sort by timestamp
            alerts = list(self.current_alerts)
            alerts.sort(key=lambda x: x['timestamp'], reverse=True)
            
            return alerts[:limit]
            
        except Exception as e:
            logger.error(f"Error getting recent alerts: {e}")
            return []
    
    def get_alerts_by_symbol(self, symbol: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Get alerts for specific symbol"""
        try:
            symbol_alerts = [
                alert for alert in self.current_alerts 
                if alert['symbol'] == symbol
            ]
            
            # Sort by timestamp
            symbol_alerts.sort(key=lambda x: x['timestamp'], reverse=True)
            
            return symbol_alerts[:limit]
            
        except Exception as e:
            logger.error(f"Error getting alerts for {symbol}: {e}")
            return []
    
    def get_scanner_stats(self) -> Dict[str, Any]:
        """Get scanner statistics"""
        try:
            # Calculate alerts per hour
            now = datetime.now()
            hour_ago = now - timedelta(hours=1)
            recent_alerts = [
                alert for alert in self.current_alerts 
                if alert['timestamp'] > hour_ago
            ]
            
            # Calculate success rate (simplified)
            total_alerts = len(self.current_alerts)
            success_rate = 0.75 if total_alerts > 0 else 0  # Placeholder
            
            stats = {
                'is_running': self.is_running,
                'total_scans': self.scan_stats['total_scans'],
                'total_alerts': total_alerts,
                'alerts_last_hour': len(recent_alerts),
                'stocks_scanned': self.scan_stats['stocks_scanned'],
                'last_scan_time': self.scan_stats['last_scan_time'].isoformat() if self.scan_stats['last_scan_time'] else None,
                'scan_duration': self.scan_stats['scan_duration'],
                'scan_interval': self.scan_interval,
                'timeframes': self.timeframes,
                'success_rate': success_rate,
                'alerts_per_scan': total_alerts / max(self.scan_stats['total_scans'], 1)
            }
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting scanner stats: {e}")
            return {'error': str(e)}
    
    def clear_alerts(self):
        """Clear all alerts"""
        self.current_alerts.clear()
        self.alert_history.clear()
        logger.info("All alerts cleared")
    
    def update_configuration(self, config: Dict[str, Any]):
        """Update scanner configuration"""
        try:
            if 'scan_interval' in config:
                self.scan_interval = max(10, config['scan_interval'])  # Minimum 10 seconds
            
            if 'max_concurrent_scans' in config:
                self.max_concurrent_scans = max(5, min(50, config['max_concurrent_scans']))
            
            if 'alert_cooldown_minutes' in config:
                cooldown_minutes = max(5, config['alert_cooldown_minutes'])
                self.alert_cooldown = timedelta(minutes=cooldown_minutes)
            
            logger.info(f"Scanner configuration updated: {config}")
            
        except Exception as e:
            logger.error(f"Error updating scanner configuration: {e}")

# ============================================================================
# STOCK SCREENER
# ============================================================================

class StockScreener:
    """Screen stocks based on various criteria"""
    
    def __init__(self, data_manager):
        self.data_manager = data_manager
        
    async def screen_by_volume(self, min_volume: int = 1000000) -> List[str]:
        """Screen stocks by minimum volume"""
        try:
            stock_universe = self.data_manager.get_stock_universe()
            high_volume_stocks = []
            
            # Process in batches
            batch_size = 20
            for i in range(0, len(stock_universe), batch_size):
                batch = stock_universe[i:i + batch_size]
                
                # Get volume data for batch
                tasks = [self._get_stock_volume(symbol) for symbol in batch]
                volumes = await asyncio.gather(*tasks, return_exceptions=True)
                
                # Filter by volume
                for symbol, volume in zip(batch, volumes):
                    if isinstance(volume, (int, float)) and volume >= min_volume:
                        high_volume_stocks.append(symbol)
            
            logger.info(f"Found {len(high_volume_stocks)} stocks with volume >= {min_volume:,}")
            return high_volume_stocks
            
        except Exception as e:
            logger.error(f"Error screening by volume: {e}")
            return []
    
    async def screen_by_price_range(self, min_price: float = 5.0, max_price: float = 500.0) -> List[str]:
        """Screen stocks by price range"""
        try:
            stock_universe = self.data_manager.get_stock_universe()
            price_filtered_stocks = []
            
            # Process in batches
            batch_size = 20
            for i in range(0, len(stock_universe), batch_size):
                batch = stock_universe[i:i + batch_size]
                
                # Get price data for batch
                tasks = [self._get_stock_price(symbol) for symbol in batch]
                prices = await asyncio.gather(*tasks, return_exceptions=True)
                
                # Filter by price
                for symbol, price in zip(batch, prices):
                    if isinstance(price, (int, float)) and min_price <= price <= max_price:
                        price_filtered_stocks.append(symbol)
            
            logger.info(f"Found {len(price_filtered_stocks)} stocks in price range ${min_price}-${max_price}")
            return price_filtered_stocks
            
        except Exception as e:
            logger.error(f"Error screening by price range: {e}")
            return []
    
    async def _get_stock_volume(self, symbol: str) -> float:
        """Get current volume for a stock"""
        try:
            # Get recent 5-minute data
            data = await self.data_manager.get_historical_data(symbol, '5Min', 10)
            if data is not None and len(data) > 0:
                return data['volume'].iloc[-1]
            return 0
        except:
            return 0
    
    async def _get_stock_price(self, symbol: str) -> float:
        """Get current price for a stock"""
        try:
            quote = await self.data_manager.get_real_time_quote(symbol)
            if quote:
                return quote.price
            
            # Fallback to historical data
            data = await self.data_manager.get_historical_data(symbol, '5Min', 1)
            if data is not None and len(data) > 0:
                return data['close'].iloc[-1]
            
            return 0
        except:
            return 0

# ============================================================================
# ALERT MANAGER
# ============================================================================

class AlertManager:
    """Manage and distribute trading alerts"""
    
    def __init__(self):
        self.subscribers = []
        self.alert_queue = asyncio.Queue()
        self.is_processing = False
        
    def subscribe(self, callback):
        """Subscribe to alerts"""
        self.subscribers.append(callback)
        logger.info(f"New alert subscriber added. Total: {len(self.subscribers)}")
    
    def unsubscribe(self, callback):
        """Unsubscribe from alerts"""
        if callback in self.subscribers:
            self.subscribers.remove(callback)
            logger.info(f"Alert subscriber removed. Total: {len(self.subscribers)}")
    
    async def publish_alert(self, alert: Dict[str, Any]):
        """Publish alert to all subscribers"""
        try:
            await self.alert_queue.put(alert)
            
            if not self.is_processing:
                asyncio.create_task(self._process_alerts())
                
        except Exception as e:
            logger.error(f"Error publishing alert: {e}")
    
    async def _process_alerts(self):
        """Process alert queue"""
        if self.is_processing:
            return
        
        self.is_processing = True
        
        try:
            while not self.alert_queue.empty():
                alert = await self.alert_queue.get()
                
                # Notify all subscribers
                for callback in self.subscribers:
                    try:
                        if asyncio.iscoroutinefunction(callback):
                            await callback(alert)
                        else:
                            callback(alert)
                    except Exception as e:
                        logger.error(f"Error in alert callback: {e}")
                
                self.alert_queue.task_done()
                
        except Exception as e:
            logger.error(f"Error processing alerts: {e}")
        finally:
            self.is_processing = False
