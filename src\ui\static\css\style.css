/* TTM Squeeze Trading System Styles */

body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.navbar-brand {
    font-weight: bold;
}

.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid #e9ecef;
    border-radius: 10px 10px 0 0 !important;
    font-weight: 600;
}

.nav-tabs .nav-link {
    border: none;
    color: #6c757d;
    font-weight: 500;
}

.nav-tabs .nav-link.active {
    background-color: #fff;
    border-bottom: 3px solid #007bff;
    color: #007bff;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
    font-size: 0.875rem;
}

.table td {
    font-size: 0.875rem;
    vertical-align: middle;
}

.btn {
    border-radius: 6px;
    font-weight: 500;
}

.btn-sm {
    font-size: 0.8rem;
}

/* Alert Priority Colors */
.alert-priority-1 {
    border-left: 4px solid #dc3545;
    background-color: #f8d7da;
}

.alert-priority-2 {
    border-left: 4px solid #ffc107;
    background-color: #fff3cd;
}

.alert-priority-3 {
    border-left: 4px solid #17a2b8;
    background-color: #d1ecf1;
}

/* Signal Strength Indicators */
.signal-strength {
    display: inline-block;
    width: 60px;
    height: 8px;
    background-color: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.signal-strength-fill {
    height: 100%;
    border-radius: 4px;
    transition: width 0.3s ease;
}

.signal-strength-high .signal-strength-fill {
    background-color: #28a745;
}

.signal-strength-medium .signal-strength-fill {
    background-color: #ffc107;
}

.signal-strength-low .signal-strength-fill {
    background-color: #dc3545;
}

/* Momentum Colors */
.momentum-red {
    color: #dc3545;
    font-weight: bold;
}

.momentum-yellow {
    color: #ffc107;
    font-weight: bold;
}

.momentum-green {
    color: #28a745;
    font-weight: bold;
}

/* Status Indicators */
.status-running {
    color: #28a745;
}

.status-stopped {
    color: #dc3545;
}

.status-loading {
    color: #ffc107;
}

/* Market Status */
.market-open {
    color: #28a745;
}

.market-closed {
    color: #dc3545;
}

/* Risk Level Colors */
.risk-low {
    color: #28a745;
    background-color: #d4edda;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: bold;
}

.risk-medium {
    color: #856404;
    background-color: #fff3cd;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: bold;
}

.risk-high {
    color: #721c24;
    background-color: #f8d7da;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: bold;
}

.risk-critical {
    color: #fff;
    background-color: #dc3545;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: bold;
}

/* P&L Colors */
.pnl-positive {
    color: #28a745;
    font-weight: bold;
}

.pnl-negative {
    color: #dc3545;
    font-weight: bold;
}

/* Loading Spinner */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .card-body {
        padding: 1rem;
    }
    
    .table-responsive {
        font-size: 0.8rem;
    }
    
    .btn {
        font-size: 0.8rem;
        padding: 0.375rem 0.75rem;
    }
}

/* Custom scrollbar */
.table-responsive::-webkit-scrollbar {
    height: 8px;
}

.table-responsive::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Analysis result styling */
.analysis-result {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    background-color: #fff;
}

.signal-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
}

.signal-indicator.squeeze {
    background-color: #ffc107;
}

.signal-indicator.no-squeeze {
    background-color: #6c757d;
}

.signal-indicator.entry {
    background-color: #28a745;
}

/* Timeframe badges */
.timeframe-badge {
    font-size: 0.75rem;
    font-weight: 600;
    padding: 4px 8px;
    margin: 2px;
    border-radius: 4px;
    display: inline-block;
    color: #ffffff !important;
    background-color: #6c757d;
    border: 1px solid #5a6268;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.5);
}

/* Specific timeframe colors for better visibility */
.badge-primary {
    background-color: #007bff !important;
    border-color: #0056b3 !important;
    color: #ffffff !important;
}

.badge-success {
    background-color: #28a745 !important;
    border-color: #1e7e34 !important;
    color: #ffffff !important;
}

.badge-warning {
    background-color: #ffc107 !important;
    border-color: #d39e00 !important;
    color: #212529 !important;
    text-shadow: none;
}

.badge-secondary {
    background-color: #6c757d !important;
    border-color: #545b62 !important;
    color: #ffffff !important;
}

.badge-info {
    background-color: #17a2b8 !important;
    border-color: #117a8b !important;
    color: #ffffff !important;
}

.badge-danger {
    background-color: #dc3545 !important;
    border-color: #bd2130 !important;
    color: #ffffff !important;
}

/* Dashboard cards hover effect */
.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
}
