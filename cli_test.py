"""
Command Line Interface for TTM Squeeze Trading System
Quick testing and analysis tool
"""
import sys
import asyncio
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent / 'src'))

from data.data_manager import DataManager
from indicators.multi_timeframe import MultiTimeframeAnalyzer
from scanner.stock_screener import StockScreener, ScreeningCriteria
from trading.alpaca_trader import AlpacaTrader
from trading.risk_manager import RiskManager
from config import Config

def test_symbol_analysis(symbol: str):
    """Test symbol analysis"""
    print(f"\n🔍 Analyzing {symbol}...")
    
    try:
        # Initialize components
        data_manager = DataManager()
        analyzer = MultiTimeframeAnalyzer()
        
        # Get data for all timeframes
        timeframes = [Config.TIMEFRAMES['primary']] + Config.TIMEFRAMES['confirmation']
        data_dict = {}
        
        for timeframe in timeframes:
            print(f"   Fetching {timeframe} data...")
            data = data_manager.get_historical_data(symbol, timeframe, periods=100)
            if data is not None and len(data) >= 30:
                data_dict[timeframe] = data
                print(f"   ✅ {timeframe}: {len(data)} bars")
            else:
                print(f"   ❌ {timeframe}: Insufficient data")
        
        if len(data_dict) < 2:
            print(f"   ❌ Not enough timeframe data for analysis")
            return
        
        # Perform analysis
        signal = analyzer.analyze_symbol(symbol, data_dict)
        
        if signal:
            print(f"\n📊 Analysis Results for {symbol}:")
            print(f"   Signal Strength: {signal.overall_strength:.2f}")
            print(f"   Valid Setup: {'✅' if signal.is_valid_setup else '❌'}")
            print(f"   Confirmations: {signal.confirmations_count}/{signal.required_confirmations}")
            print(f"   Trend Alignment: {'✅' if signal.trend_alignment else '❌'}")
            print(f"   Volume Confirmation: {'✅' if signal.volume_confirmation else '❌'}")
            print(f"   Recommendation: {signal.entry_recommendation.upper()}")
            
            # Primary signal details
            primary = signal.primary_signal
            print(f"\n   Primary Signal ({primary.timeframe}):")
            print(f"     Squeeze: {'✅' if primary.is_squeeze else '❌'}")
            print(f"     Momentum: {primary.momentum:.6f} ({primary.momentum_color})")
            print(f"     Entry Signal: {'✅' if primary.entry_signal else '❌'}")
            
            # Confirmation signals
            print(f"\n   Confirmation Signals:")
            for tf, conf_signal in signal.confirmation_signals.items():
                print(f"     {tf}: Squeeze={'✅' if conf_signal.is_squeeze else '❌'}, "
                      f"Momentum={conf_signal.momentum:.6f} ({conf_signal.momentum_color})")
        else:
            print(f"   ❌ No valid signal generated for {symbol}")
            
    except Exception as e:
        print(f"   ❌ Error analyzing {symbol}: {e}")

def test_account_info():
    """Test account information"""
    print(f"\n💰 Account Information...")
    
    try:
        trader = AlpacaTrader()
        account_info = trader.get_account_info()
        
        print(f"   Account ID: {account_info['account_id']}")
        print(f"   Status: {account_info['status']}")
        print(f"   Portfolio Value: ${account_info['portfolio_value']:,.2f}")
        print(f"   Buying Power: ${account_info['buying_power']:,.2f}")
        print(f"   Cash: ${account_info['cash']:,.2f}")
        print(f"   Day Trade Count: {account_info['day_trade_count']}")
        
        # Get positions
        positions = trader.get_positions()
        print(f"   Current Positions: {len(positions)}")
        
        for pos in positions[:5]:  # Show first 5 positions
            pnl_sign = '+' if pos['unrealized_pl'] >= 0 else ''
            print(f"     {pos['symbol']}: {pos['quantity']} shares, "
                  f"P&L: {pnl_sign}${pos['unrealized_pl']:.2f}")
        
    except Exception as e:
        print(f"   ❌ Error getting account info: {e}")

def test_risk_assessment(symbol: str, entry_price: float, stop_loss: float):
    """Test risk assessment"""
    print(f"\n⚖️ Risk Assessment for {symbol}...")
    
    try:
        trader = AlpacaTrader()
        risk_manager = RiskManager(trader)
        
        # Calculate position size
        position_size = risk_manager.calculate_position_size(symbol, entry_price, stop_loss)
        print(f"   Recommended Position Size: {position_size} shares")
        
        # Assess trade risk
        trade_risk = risk_manager.assess_trade_risk(symbol, entry_price, stop_loss, position_size)
        
        print(f"   Risk Amount: ${trade_risk.risk_amount:.2f}")
        print(f"   Position Size %: {trade_risk.position_size_pct:.2%}")
        print(f"   Risk/Reward Ratio: {trade_risk.risk_reward_ratio:.2f}")
        print(f"   Trade Acceptable: {'✅' if trade_risk.is_acceptable else '❌'}")
        
        if trade_risk.warnings:
            print(f"   Warnings:")
            for warning in trade_risk.warnings:
                print(f"     ⚠️ {warning}")
        
        # Portfolio risk
        portfolio_risk = risk_manager.assess_portfolio_risk()
        print(f"\n   Portfolio Risk: {portfolio_risk.risk_percentage:.2%} / {portfolio_risk.max_risk_percentage:.2%}")
        print(f"   Risk Level: {portfolio_risk.risk_level.value.upper()}")
        
    except Exception as e:
        print(f"   ❌ Error in risk assessment: {e}")

def test_screener():
    """Test stock screener"""
    print(f"\n🔍 Running Stock Screener...")
    
    try:
        data_manager = DataManager()
        screener = StockScreener(data_manager)
        
        # Define criteria
        criteria = ScreeningCriteria(
            min_price=20.0,
            max_price=500.0,
            min_volume=2_000_000,
            ema_alignment=True
        )
        
        print(f"   Criteria: Price ${criteria.min_price}-${criteria.max_price}, "
              f"Volume >{criteria.min_volume:,}, EMA Aligned")
        
        # Run screener (limit to 10 for testing)
        results = screener.screen_stocks(criteria, max_stocks=10)
        
        print(f"   Found {len(results)} stocks meeting criteria:")
        
        for i, stock in enumerate(results[:5], 1):
            print(f"   {i}. {stock.symbol} ({stock.company_name})")
            print(f"      Price: ${stock.price:.2f}, Volume: {stock.volume:,}")
            print(f"      RSI: {stock.rsi:.1f}, Score: {stock.score:.1f}")
            print(f"      EMA Aligned: {'✅' if stock.ema_aligned else '❌'}")
        
    except Exception as e:
        print(f"   ❌ Error in screener: {e}")

def main():
    """Main CLI interface"""
    print("🚀 TTM Squeeze Trading System - CLI Test Tool")
    print("=" * 60)
    
    while True:
        print("\nOptions:")
        print("1. Analyze Symbol")
        print("2. Account Information")
        print("3. Risk Assessment")
        print("4. Stock Screener")
        print("5. Test Popular Symbols")
        print("6. Exit")
        
        choice = input("\nEnter choice (1-6): ").strip()
        
        if choice == '1':
            symbol = input("Enter symbol (e.g., AAPL): ").strip().upper()
            if symbol:
                test_symbol_analysis(symbol)
        
        elif choice == '2':
            test_account_info()
        
        elif choice == '3':
            symbol = input("Enter symbol: ").strip().upper()
            try:
                entry_price = float(input("Enter entry price: "))
                stop_loss = float(input("Enter stop loss price: "))
                test_risk_assessment(symbol, entry_price, stop_loss)
            except ValueError:
                print("❌ Invalid price format")
        
        elif choice == '4':
            test_screener()
        
        elif choice == '5':
            popular_symbols = ['AAPL', 'MSFT', 'GOOGL', 'TSLA', 'NVDA']
            print(f"\n🔍 Testing popular symbols: {', '.join(popular_symbols)}")
            for symbol in popular_symbols:
                test_symbol_analysis(symbol)
                print("-" * 40)
        
        elif choice == '6':
            print("👋 Goodbye!")
            break
        
        else:
            print("❌ Invalid choice")

if __name__ == "__main__":
    main()
