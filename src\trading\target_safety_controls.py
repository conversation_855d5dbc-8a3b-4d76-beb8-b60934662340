"""
Target-Specific Safety Controls and Risk Management
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
import logging
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

from .profit_target_config import ProfitTarget, TargetStatus
from .safety_controls import TradingSafetyControls, CircuitBreakerType, AlertLevel

logger = logging.getLogger(__name__)

class TargetRiskLevel(Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    EXTREME = "extreme"

@dataclass
class TargetRiskMetrics:
    """Risk metrics specific to target trading"""
    target_id: str
    current_risk_score: float
    velocity_risk: float          # Risk from trading velocity
    concentration_risk: float     # Risk from position concentration
    time_pressure_risk: float    # Risk from time constraints
    market_condition_risk: float # Risk from market conditions
    overall_risk_level: TargetRiskLevel
    recommended_action: str

@dataclass
class TargetSafetyAlert:
    """Safety alert specific to target trading"""
    alert_id: str
    target_id: str
    alert_type: str
    severity: AlertLevel
    message: str
    risk_metrics: TargetRiskMetrics
    timestamp: datetime
    auto_action_taken: str
    requires_manual_review: bool

class TargetSpecificSafetyControls:
    """Enhanced safety controls specifically for profit target trading"""
    
    def __init__(self, base_safety_controls: TradingSafetyControls):
        self.base_controls = base_safety_controls
        
        # Target-specific configuration
        self.target_config = {
            'max_velocity_trades_per_hour': 5,
            'max_target_concentration': 0.4,  # 40% of portfolio in target trades
            'time_pressure_threshold_hours': 2,
            'extreme_risk_halt_threshold': 85,
            'high_risk_reduction_threshold': 70,
            'market_volatility_halt_vix': 40,
            'correlation_limit_per_target': 0.6,
            'max_drawdown_from_target': 0.15,  # 15% drawdown from target start
        }
        
        # Target risk tracking
        self.target_risk_metrics = {}  # target_id -> TargetRiskMetrics
        self.target_alerts = []
        self.velocity_tracking = {}    # target_id -> list of trade timestamps
        
        # Market condition monitoring
        self.market_stress_indicators = {
            'vix_spike': False,
            'breadth_deterioration': False,
            'sector_rotation': False,
            'unusual_volume': False
        }
        
        logger.info("Target-Specific Safety Controls initialized")
    
    def assess_target_risk(self, target: ProfitTarget, 
                          current_progress: Dict,
                          market_conditions: Dict,
                          recent_trades: List[Dict]) -> TargetRiskMetrics:
        """
        Comprehensive risk assessment for target trading
        
        Args:
            target: Profit target configuration
            current_progress: Current progress toward target
            market_conditions: Current market conditions
            recent_trades: Recent trades for this target
            
        Returns:
            Comprehensive risk metrics
        """
        try:
            # Calculate individual risk components
            velocity_risk = self._calculate_velocity_risk(target.target_id, recent_trades)
            concentration_risk = self._calculate_concentration_risk(target, current_progress)
            time_pressure_risk = self._calculate_time_pressure_risk(target, current_progress)
            market_risk = self._calculate_market_condition_risk(market_conditions)
            
            # Calculate overall risk score (0-100)
            risk_weights = {
                'velocity': 0.25,
                'concentration': 0.25,
                'time_pressure': 0.20,
                'market': 0.30
            }
            
            overall_score = (
                velocity_risk * risk_weights['velocity'] +
                concentration_risk * risk_weights['concentration'] +
                time_pressure_risk * risk_weights['time_pressure'] +
                market_risk * risk_weights['market']
            )
            
            # Determine risk level
            if overall_score >= 85:
                risk_level = TargetRiskLevel.EXTREME
                recommended_action = "HALT_TRADING"
            elif overall_score >= 70:
                risk_level = TargetRiskLevel.HIGH
                recommended_action = "REDUCE_POSITION_SIZES"
            elif overall_score >= 50:
                risk_level = TargetRiskLevel.MEDIUM
                recommended_action = "INCREASE_SELECTIVITY"
            else:
                risk_level = TargetRiskLevel.LOW
                recommended_action = "CONTINUE_NORMAL"
            
            # Create risk metrics
            risk_metrics = TargetRiskMetrics(
                target_id=target.target_id,
                current_risk_score=overall_score,
                velocity_risk=velocity_risk,
                concentration_risk=concentration_risk,
                time_pressure_risk=time_pressure_risk,
                market_condition_risk=market_risk,
                overall_risk_level=risk_level,
                recommended_action=recommended_action
            )
            
            # Store for tracking
            self.target_risk_metrics[target.target_id] = risk_metrics
            
            # Generate alerts if necessary
            self._check_risk_alerts(target, risk_metrics)
            
            return risk_metrics
            
        except Exception as e:
            logger.error(f"Error assessing target risk: {e}")
            return self._get_default_risk_metrics(target.target_id)
    
    def _calculate_velocity_risk(self, target_id: str, recent_trades: List[Dict]) -> float:
        """Calculate risk from trading velocity"""
        try:
            # Track trade timestamps
            if target_id not in self.velocity_tracking:
                self.velocity_tracking[target_id] = []
            
            # Add recent trade timestamps
            now = datetime.now()
            for trade in recent_trades:
                trade_time = trade.get('timestamp', now)
                if isinstance(trade_time, str):
                    trade_time = datetime.fromisoformat(trade_time)
                self.velocity_tracking[target_id].append(trade_time)
            
            # Clean old timestamps (keep last 24 hours)
            cutoff = now - timedelta(hours=24)
            self.velocity_tracking[target_id] = [
                t for t in self.velocity_tracking[target_id] if t > cutoff
            ]
            
            # Calculate trades per hour in last 4 hours
            recent_cutoff = now - timedelta(hours=4)
            recent_trades_count = len([
                t for t in self.velocity_tracking[target_id] if t > recent_cutoff
            ])
            
            trades_per_hour = recent_trades_count / 4
            
            # Risk score based on velocity
            max_velocity = self.target_config['max_velocity_trades_per_hour']
            velocity_risk = min(100, (trades_per_hour / max_velocity) * 100)
            
            return velocity_risk
            
        except Exception as e:
            logger.warning(f"Error calculating velocity risk: {e}")
            return 0.0
    
    def _calculate_concentration_risk(self, target: ProfitTarget, 
                                    current_progress: Dict) -> float:
        """Calculate risk from position concentration"""
        try:
            # Get current portfolio value
            portfolio_value = current_progress.get('portfolio_value', 100000)
            target_exposure = current_progress.get('current_progress', 0)
            
            # Calculate concentration as percentage of portfolio
            if portfolio_value > 0:
                concentration = abs(target_exposure) / portfolio_value
            else:
                concentration = 0
            
            # Risk score based on concentration
            max_concentration = self.target_config['max_target_concentration']
            concentration_risk = min(100, (concentration / max_concentration) * 100)
            
            # Additional risk for multiple correlated targets
            # This would be enhanced with actual correlation analysis
            correlation_penalty = 0  # Placeholder
            
            return min(100, concentration_risk + correlation_penalty)
            
        except Exception as e:
            logger.warning(f"Error calculating concentration risk: {e}")
            return 0.0
    
    def _calculate_time_pressure_risk(self, target: ProfitTarget,
                                    current_progress: Dict) -> float:
        """Calculate risk from time pressure"""
        try:
            time_remaining = current_progress.get('time_remaining')
            if not time_remaining or not isinstance(time_remaining, timedelta):
                return 0.0
            
            hours_remaining = time_remaining.total_seconds() / 3600
            completion_pct = current_progress.get('completion_percentage', 0)
            
            # Calculate expected vs actual progress
            if target.timeframe.value == 'daily':
                total_hours = 6.5  # Trading day
            elif target.timeframe.value == 'weekly':
                total_hours = 32.5  # Trading week
            elif target.timeframe.value == 'monthly':
                total_hours = 140  # Trading month
            else:
                return 0.0
            
            elapsed_hours = total_hours - hours_remaining
            expected_progress = (elapsed_hours / total_hours) * 100
            
            # Risk increases if behind schedule
            progress_gap = max(0, expected_progress - completion_pct)
            
            # Time pressure risk
            time_pressure_threshold = self.target_config['time_pressure_threshold_hours']
            if hours_remaining < time_pressure_threshold:
                time_pressure = 80 + (progress_gap * 0.5)  # High base risk when time is short
            else:
                time_pressure = progress_gap * 0.8  # Moderate risk based on progress gap
            
            return min(100, time_pressure)
            
        except Exception as e:
            logger.warning(f"Error calculating time pressure risk: {e}")
            return 0.0
    
    def _calculate_market_condition_risk(self, market_conditions: Dict) -> float:
        """Calculate risk from market conditions"""
        try:
            risk_score = 0.0
            
            # VIX risk
            vix = market_conditions.get('vix', 20)
            if vix > self.target_config['market_volatility_halt_vix']:
                risk_score += 40
            elif vix > 30:
                risk_score += 20
            elif vix > 25:
                risk_score += 10
            
            # Market regime risk
            regime = market_conditions.get('regime', 'unknown')
            regime_risk = {
                'volatile': 30,
                'bear_market': 25,
                'transition': 20,
                'ranging': 10,
                'trending': 5,
                'bull_market': 0
            }
            risk_score += regime_risk.get(regime, 15)
            
            # Market breadth risk
            breadth = market_conditions.get('market_breadth', 50)
            if breadth < 20 or breadth > 80:
                risk_score += 20
            elif breadth < 30 or breadth > 70:
                risk_score += 10
            
            # Sector rotation risk
            sector_rotation = market_conditions.get('sector_rotation_intensity', 0)
            if sector_rotation > 0.7:
                risk_score += 15
            
            return min(100, risk_score)
            
        except Exception as e:
            logger.warning(f"Error calculating market condition risk: {e}")
            return 20.0  # Default moderate risk
    
    def _check_risk_alerts(self, target: ProfitTarget, risk_metrics: TargetRiskMetrics):
        """Check if risk alerts should be generated"""
        try:
            alerts_generated = []
            
            # Extreme risk alert
            if risk_metrics.overall_risk_level == TargetRiskLevel.EXTREME:
                alert = self._create_risk_alert(
                    target, risk_metrics, AlertLevel.CRITICAL,
                    f"EXTREME RISK: Target {target.name} risk score {risk_metrics.current_risk_score:.1f}",
                    "TRADING_HALTED"
                )
                alerts_generated.append(alert)
            
            # High velocity alert
            elif risk_metrics.velocity_risk > 80:
                alert = self._create_risk_alert(
                    target, risk_metrics, AlertLevel.WARNING,
                    f"High trading velocity detected for target {target.name}",
                    "VELOCITY_REDUCTION"
                )
                alerts_generated.append(alert)
            
            # High concentration alert
            elif risk_metrics.concentration_risk > 75:
                alert = self._create_risk_alert(
                    target, risk_metrics, AlertLevel.WARNING,
                    f"High concentration risk for target {target.name}",
                    "POSITION_SIZE_REDUCTION"
                )
                alerts_generated.append(alert)
            
            # Time pressure alert
            elif risk_metrics.time_pressure_risk > 70:
                alert = self._create_risk_alert(
                    target, risk_metrics, AlertLevel.INFO,
                    f"Time pressure increasing for target {target.name}",
                    "INCREASED_SELECTIVITY"
                )
                alerts_generated.append(alert)
            
            # Store alerts
            self.target_alerts.extend(alerts_generated)
            
            # Keep only recent alerts (last 24 hours)
            cutoff = datetime.now() - timedelta(hours=24)
            self.target_alerts = [a for a in self.target_alerts if a.timestamp > cutoff]
            
        except Exception as e:
            logger.error(f"Error checking risk alerts: {e}")
    
    def _create_risk_alert(self, target: ProfitTarget, risk_metrics: TargetRiskMetrics,
                          severity: AlertLevel, message: str, auto_action: str) -> TargetSafetyAlert:
        """Create a target-specific safety alert"""
        alert_id = f"target_risk_{target.target_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        return TargetSafetyAlert(
            alert_id=alert_id,
            target_id=target.target_id,
            alert_type="RISK_ASSESSMENT",
            severity=severity,
            message=message,
            risk_metrics=risk_metrics,
            timestamp=datetime.now(),
            auto_action_taken=auto_action,
            requires_manual_review=severity in [AlertLevel.CRITICAL, AlertLevel.EMERGENCY]
        )
    
    def check_target_trading_allowed(self, target: ProfitTarget,
                                   current_progress: Dict,
                                   market_conditions: Dict) -> Tuple[bool, str]:
        """
        Check if trading is allowed for specific target
        
        Returns:
            Tuple of (allowed, reason)
        """
        try:
            # Base safety checks
            base_allowed = self.base_controls.is_trading_allowed()
            if not base_allowed:
                return False, "Base safety controls triggered"
            
            # Target-specific risk assessment
            risk_metrics = self.assess_target_risk(
                target, current_progress, market_conditions, []
            )
            
            # Check extreme risk
            if risk_metrics.overall_risk_level == TargetRiskLevel.EXTREME:
                return False, f"Extreme risk level: {risk_metrics.current_risk_score:.1f}"
            
            # Check market volatility halt
            vix = market_conditions.get('vix', 20)
            if vix > self.target_config['market_volatility_halt_vix']:
                return False, f"Market volatility too high: VIX {vix:.1f}"
            
            # Check target-specific drawdown
            target_drawdown = self._calculate_target_drawdown(target, current_progress)
            if target_drawdown > self.target_config['max_drawdown_from_target']:
                return False, f"Target drawdown exceeded: {target_drawdown:.1%}"
            
            # Check velocity limits
            if risk_metrics.velocity_risk > 90:
                return False, "Trading velocity too high"
            
            return True, "All checks passed"
            
        except Exception as e:
            logger.error(f"Error checking target trading allowed: {e}")
            return False, f"Safety check error: {str(e)}"
    
    def _calculate_target_drawdown(self, target: ProfitTarget, 
                                 current_progress: Dict) -> float:
        """Calculate drawdown specific to target"""
        try:
            # This would track the high-water mark since target start
            # For now, use a simplified calculation
            completion_pct = current_progress.get('completion_percentage', 0) / 100
            
            # Assume some volatility in progress
            # In production, this would track actual high-water marks
            estimated_max_progress = completion_pct * 1.1  # Assume 10% volatility
            current_drawdown = max(0, estimated_max_progress - completion_pct)
            
            return current_drawdown
            
        except Exception:
            return 0.0
    
    def get_target_safety_status(self, target_id: str) -> Dict:
        """Get safety status for specific target"""
        try:
            risk_metrics = self.target_risk_metrics.get(target_id)
            if not risk_metrics:
                return {'status': 'unknown', 'message': 'No risk data available'}
            
            # Get recent alerts for this target
            recent_alerts = [
                a for a in self.target_alerts 
                if a.target_id == target_id and 
                a.timestamp > datetime.now() - timedelta(hours=1)
            ]
            
            return {
                'target_id': target_id,
                'risk_level': risk_metrics.overall_risk_level.value,
                'risk_score': risk_metrics.current_risk_score,
                'recommended_action': risk_metrics.recommended_action,
                'velocity_risk': risk_metrics.velocity_risk,
                'concentration_risk': risk_metrics.concentration_risk,
                'time_pressure_risk': risk_metrics.time_pressure_risk,
                'market_risk': risk_metrics.market_condition_risk,
                'recent_alerts': len(recent_alerts),
                'requires_attention': any(a.requires_manual_review for a in recent_alerts)
            }
            
        except Exception as e:
            logger.error(f"Error getting target safety status: {e}")
            return {'status': 'error', 'message': str(e)}
    
    def get_all_target_safety_summary(self) -> Dict:
        """Get safety summary for all targets"""
        try:
            summary = {
                'total_targets_monitored': len(self.target_risk_metrics),
                'risk_distribution': {level.value: 0 for level in TargetRiskLevel},
                'active_alerts': len(self.target_alerts),
                'critical_alerts': len([a for a in self.target_alerts if a.severity == AlertLevel.CRITICAL]),
                'targets_requiring_attention': 0,
                'market_stress_indicators': self.market_stress_indicators
            }
            
            # Count risk distribution
            for risk_metrics in self.target_risk_metrics.values():
                summary['risk_distribution'][risk_metrics.overall_risk_level.value] += 1
                
                if risk_metrics.overall_risk_level in [TargetRiskLevel.HIGH, TargetRiskLevel.EXTREME]:
                    summary['targets_requiring_attention'] += 1
            
            return summary
            
        except Exception as e:
            logger.error(f"Error getting safety summary: {e}")
            return {'error': str(e)}
    
    def _get_default_risk_metrics(self, target_id: str) -> TargetRiskMetrics:
        """Get default risk metrics when calculation fails"""
        return TargetRiskMetrics(
            target_id=target_id,
            current_risk_score=50.0,
            velocity_risk=0.0,
            concentration_risk=0.0,
            time_pressure_risk=0.0,
            market_condition_risk=50.0,
            overall_risk_level=TargetRiskLevel.MEDIUM,
            recommended_action="MONITOR"
        )
    
    def emergency_halt_target(self, target_id: str, reason: str):
        """Emergency halt for specific target"""
        try:
            # Create emergency alert
            alert = TargetSafetyAlert(
                alert_id=f"emergency_{target_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                target_id=target_id,
                alert_type="EMERGENCY_HALT",
                severity=AlertLevel.EMERGENCY,
                message=f"Emergency halt for target {target_id}: {reason}",
                risk_metrics=self.target_risk_metrics.get(target_id, self._get_default_risk_metrics(target_id)),
                timestamp=datetime.now(),
                auto_action_taken="TRADING_HALTED",
                requires_manual_review=True
            )
            
            self.target_alerts.append(alert)
            
            logger.critical(f"EMERGENCY HALT for target {target_id}: {reason}")
            
        except Exception as e:
            logger.error(f"Error during emergency halt: {e}")
    
    def update_config(self, new_config: Dict):
        """Update target safety configuration"""
        try:
            self.target_config.update(new_config)
            logger.info(f"Target safety config updated: {new_config}")
        except Exception as e:
            logger.error(f"Error updating target safety config: {e}")
    
    def clear_target_data(self, target_id: str):
        """Clear tracking data for completed/cancelled target"""
        try:
            if target_id in self.target_risk_metrics:
                del self.target_risk_metrics[target_id]
            
            if target_id in self.velocity_tracking:
                del self.velocity_tracking[target_id]
            
            # Remove alerts for this target
            self.target_alerts = [a for a in self.target_alerts if a.target_id != target_id]
            
            logger.info(f"Cleared safety data for target {target_id}")
            
        except Exception as e:
            logger.error(f"Error clearing target data: {e}")
