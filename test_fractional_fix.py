"""
Test script to verify fractional shares fix
"""
import sys
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent / 'src'))

def test_fractional_shares():
    """Test fractional shares handling"""
    print("🧪 Testing Fractional Shares Fix...")
    
    try:
        from trading.alpaca_trader import AlpacaTrader
        
        # Initialize trader
        trader = AlpacaTrader()
        print("✅ Alpaca trader initialized")
        
        # Test getting positions (this was failing before)
        positions = trader.get_positions()
        print(f"✅ Successfully retrieved {len(positions)} positions")
        
        # Display positions with fractional handling
        if positions:
            print("\n📊 Current Positions:")
            for pos in positions:
                qty_type = "fractional" if isinstance(pos['quantity'], float) and pos['quantity'] != int(pos['quantity']) else "whole"
                print(f"   {pos['symbol']}: {pos['quantity']} shares ({qty_type})")
                print(f"      P&L: ${pos['unrealized_pl']:.2f} ({pos['unrealized_plpc']:.2%})")
        else:
            print("   No positions found")
        
        # Test getting orders
        orders = trader.get_orders(limit=5)
        print(f"✅ Successfully retrieved {len(orders)} recent orders")
        
        if orders:
            print("\n📋 Recent Orders:")
            for order in orders[:3]:  # Show first 3
                qty_type = "fractional" if isinstance(order['quantity'], float) and order['quantity'] != int(order['quantity']) else "whole"
                print(f"   {order['symbol']}: {order['side']} {order['quantity']} shares ({qty_type}) - {order['status']}")
        
        print("\n🎉 Fractional shares fix working correctly!")
        return True
        
    except Exception as e:
        print(f"❌ Error testing fractional shares: {e}")
        return False

def test_account_info():
    """Test account information"""
    print("\n💰 Testing Account Information...")
    
    try:
        from trading.alpaca_trader import AlpacaTrader
        
        trader = AlpacaTrader()
        account_info = trader.get_account_info()
        
        print(f"✅ Account Status: {account_info['status']}")
        print(f"✅ Portfolio Value: ${account_info['portfolio_value']:,.2f}")
        print(f"✅ Buying Power: ${account_info['buying_power']:,.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error getting account info: {e}")
        return False

def main():
    """Main test function"""
    print("🔧 Testing Fractional Shares Fix")
    print("=" * 50)
    
    tests_passed = 0
    total_tests = 2
    
    if test_account_info():
        tests_passed += 1
    
    if test_fractional_shares():
        tests_passed += 1
    
    print("\n" + "=" * 50)
    print(f"🎯 Test Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("🎉 All tests passed! Fractional shares issue is fixed.")
        print("\nThe web interface should now work properly.")
        print("You can refresh the browser and check the Trading tab.")
    else:
        print("⚠️ Some tests failed. Please check the errors above.")

if __name__ == "__main__":
    main()
