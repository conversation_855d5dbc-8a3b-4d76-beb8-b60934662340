"""
Trading Safety Controls and Circuit Breakers
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
import logging
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import json
import os

logger = logging.getLogger(__name__)

class AlertLevel(Enum):
    INFO = "info"
    WARNING = "warning"
    CRITICAL = "critical"
    EMERGENCY = "emergency"

class CircuitBreakerType(Enum):
    PORTFOLIO_LOSS = "portfolio_loss"
    DAILY_LOSS = "daily_loss"
    VOLATILITY_SPIKE = "volatility_spike"
    CORRELATION_BREACH = "correlation_breach"
    UNUSUAL_MARKET = "unusual_market"
    API_FAILURE = "api_failure"

@dataclass
class SafetyAlert:
    """Safety alert record"""
    alert_id: str
    alert_type: CircuitBreakerType
    level: AlertLevel
    message: str
    timestamp: datetime
    data: Dict
    action_taken: str
    resolved: bool = False

@dataclass
class CircuitBreakerConfig:
    """Circuit breaker configuration"""
    enabled: bool = True
    max_daily_loss_pct: float = 0.05      # 5% daily loss limit
    max_portfolio_loss_pct: float = 0.15   # 15% total loss limit
    max_position_correlation: float = 0.8   # 80% correlation limit
    max_vix_level: float = 50.0            # VIX circuit breaker
    max_drawdown_pct: float = 0.10         # 10% drawdown limit
    min_account_balance: float = 10000.0   # Minimum account balance
    max_consecutive_losses: int = 5        # Max consecutive losing trades

class TradingSafetyControls:
    """Comprehensive trading safety controls and circuit breakers"""
    
    def __init__(self, data_manager, trader):
        self.data_manager = data_manager
        self.trader = trader
        
        # Configuration
        self.config = CircuitBreakerConfig()
        
        # State tracking
        self.circuit_breakers_active = {}  # type -> bool
        self.safety_alerts = []
        self.audit_trail = []
        
        # Performance tracking
        self.daily_pnl = 0.0
        self.portfolio_high_water_mark = 0.0
        self.consecutive_losses = 0
        self.last_reset_date = datetime.now().date()
        
        # Market condition monitoring
        self.market_conditions = {}
        self.unusual_conditions_detected = False
        
        # Audit trail file
        self.audit_file = "logs/trading_audit.jsonl"
        os.makedirs(os.path.dirname(self.audit_file), exist_ok=True)
        
        logger.info("Trading Safety Controls initialized")
    
    def check_all_safety_conditions(self, current_positions: List[Dict],
                                   market_data: Dict) -> Dict[str, bool]:
        """
        Check all safety conditions and return status
        
        Args:
            current_positions: List of current trading positions
            market_data: Current market data
            
        Returns:
            Dict of safety check results
        """
        try:
            safety_status = {}
            
            # Update daily tracking if new day
            self._update_daily_tracking()
            
            # Check portfolio loss limits
            safety_status['portfolio_loss'] = self._check_portfolio_loss_limits()
            
            # Check daily loss limits
            safety_status['daily_loss'] = self._check_daily_loss_limits()
            
            # Check position correlation limits
            safety_status['correlation'] = self._check_correlation_limits(current_positions)
            
            # Check market volatility conditions
            safety_status['volatility'] = self._check_volatility_conditions(market_data)
            
            # Check account balance limits
            safety_status['account_balance'] = self._check_account_balance()
            
            # Check consecutive losses
            safety_status['consecutive_losses'] = self._check_consecutive_losses()
            
            # Check for unusual market conditions
            safety_status['market_conditions'] = self._check_unusual_market_conditions(market_data)
            
            # Overall safety status
            safety_status['overall_safe'] = all(safety_status.values())
            
            # Log safety check
            self._log_safety_check(safety_status)
            
            return safety_status
            
        except Exception as e:
            logger.error(f"Error checking safety conditions: {e}")
            return {'overall_safe': False, 'error': str(e)}
    
    def _check_portfolio_loss_limits(self) -> bool:
        """Check portfolio loss limits"""
        try:
            account = self.trader.get_account()
            if not account:
                return False
            
            current_value = float(account.portfolio_value)
            
            # Update high water mark
            if current_value > self.portfolio_high_water_mark:
                self.portfolio_high_water_mark = current_value
            
            # Calculate drawdown
            if self.portfolio_high_water_mark > 0:
                drawdown = (self.portfolio_high_water_mark - current_value) / self.portfolio_high_water_mark
                
                if drawdown > self.config.max_portfolio_loss_pct:
                    self._trigger_circuit_breaker(
                        CircuitBreakerType.PORTFOLIO_LOSS,
                        f"Portfolio loss limit exceeded: {drawdown:.2%}",
                        {'drawdown': drawdown, 'current_value': current_value}
                    )
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error checking portfolio loss limits: {e}")
            return False
    
    def _check_daily_loss_limits(self) -> bool:
        """Check daily loss limits"""
        try:
            # Calculate daily P&L
            account = self.trader.get_account()
            if not account:
                return False
            
            daily_change = float(account.portfolio_value) - float(account.last_equity)
            daily_change_pct = daily_change / float(account.last_equity) if float(account.last_equity) > 0 else 0
            
            self.daily_pnl = daily_change_pct
            
            if daily_change_pct < -self.config.max_daily_loss_pct:
                self._trigger_circuit_breaker(
                    CircuitBreakerType.DAILY_LOSS,
                    f"Daily loss limit exceeded: {daily_change_pct:.2%}",
                    {'daily_pnl': daily_change_pct, 'daily_change': daily_change}
                )
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error checking daily loss limits: {e}")
            return False
    
    def _check_correlation_limits(self, positions: List[Dict]) -> bool:
        """Check position correlation limits"""
        try:
            if len(positions) < 2:
                return True
            
            # Calculate sector concentration
            sector_exposure = {}
            total_exposure = 0.0
            
            for position in positions:
                symbol = position.get('symbol', '')
                market_value = abs(float(position.get('market_value', 0)))
                sector = self._get_symbol_sector(symbol)
                
                sector_exposure[sector] = sector_exposure.get(sector, 0) + market_value
                total_exposure += market_value
            
            # Check for excessive concentration
            if total_exposure > 0:
                for sector, exposure in sector_exposure.items():
                    concentration = exposure / total_exposure
                    
                    if concentration > self.config.max_position_correlation:
                        self._trigger_circuit_breaker(
                            CircuitBreakerType.CORRELATION_BREACH,
                            f"Sector concentration limit exceeded: {sector} {concentration:.2%}",
                            {'sector': sector, 'concentration': concentration}
                        )
                        return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error checking correlation limits: {e}")
            return False
    
    def _check_volatility_conditions(self, market_data: Dict) -> bool:
        """Check market volatility conditions"""
        try:
            vix = market_data.get('vix', 20.0)
            
            if vix > self.config.max_vix_level:
                self._trigger_circuit_breaker(
                    CircuitBreakerType.VOLATILITY_SPIKE,
                    f"VIX spike detected: {vix:.1f}",
                    {'vix': vix, 'threshold': self.config.max_vix_level}
                )
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error checking volatility conditions: {e}")
            return False
    
    def _check_account_balance(self) -> bool:
        """Check minimum account balance"""
        try:
            account = self.trader.get_account()
            if not account:
                return False
            
            balance = float(account.portfolio_value)
            
            if balance < self.config.min_account_balance:
                self._trigger_circuit_breaker(
                    CircuitBreakerType.PORTFOLIO_LOSS,
                    f"Account balance below minimum: ${balance:.2f}",
                    {'balance': balance, 'minimum': self.config.min_account_balance}
                )
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error checking account balance: {e}")
            return False
    
    def _check_consecutive_losses(self) -> bool:
        """Check consecutive losses limit"""
        try:
            if self.consecutive_losses >= self.config.max_consecutive_losses:
                self._trigger_circuit_breaker(
                    CircuitBreakerType.DAILY_LOSS,
                    f"Consecutive losses limit exceeded: {self.consecutive_losses}",
                    {'consecutive_losses': self.consecutive_losses}
                )
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error checking consecutive losses: {e}")
            return False
    
    def _check_unusual_market_conditions(self, market_data: Dict) -> bool:
        """Check for unusual market conditions"""
        try:
            # Check for market gaps
            spy_data = self.data_manager.get_historical_data('SPY', '1Day', periods=2)
            if spy_data is not None and len(spy_data) >= 2:
                gap = abs(spy_data['open'].iloc[-1] - spy_data['close'].iloc[-2]) / spy_data['close'].iloc[-2]
                
                if gap > 0.03:  # 3% gap
                    self._trigger_circuit_breaker(
                        CircuitBreakerType.UNUSUAL_MARKET,
                        f"Large market gap detected: {gap:.2%}",
                        {'gap_size': gap}
                    )
                    return False
            
            # Check for extreme breadth
            market_breadth = market_data.get('market_breadth', 50)
            if market_breadth < 20 or market_breadth > 80:
                self._trigger_circuit_breaker(
                    CircuitBreakerType.UNUSUAL_MARKET,
                    f"Extreme market breadth: {market_breadth:.1f}%",
                    {'market_breadth': market_breadth}
                )
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error checking unusual market conditions: {e}")
            return False
    
    def _trigger_circuit_breaker(self, breaker_type: CircuitBreakerType,
                               message: str, data: Dict):
        """Trigger a circuit breaker"""
        try:
            # Mark circuit breaker as active
            self.circuit_breakers_active[breaker_type] = True
            
            # Create safety alert
            alert = SafetyAlert(
                alert_id=f"{breaker_type.value}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                alert_type=breaker_type,
                level=AlertLevel.CRITICAL,
                message=message,
                timestamp=datetime.now(),
                data=data,
                action_taken="Trading halted"
            )
            
            self.safety_alerts.append(alert)
            
            # Log critical alert
            logger.critical(f"CIRCUIT BREAKER TRIGGERED: {breaker_type.value} - {message}")
            
            # Add to audit trail
            self._add_to_audit_trail("circuit_breaker_triggered", {
                'type': breaker_type.value,
                'message': message,
                'data': data
            })
            
        except Exception as e:
            logger.error(f"Error triggering circuit breaker: {e}")
    
    def _get_symbol_sector(self, symbol: str) -> str:
        """Get sector for symbol (simplified mapping)"""
        tech_symbols = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'NVDA', 'META']
        finance_symbols = ['JPM', 'BAC', 'WFC', 'GS', 'MS', 'C']
        healthcare_symbols = ['JNJ', 'PFE', 'UNH', 'ABBV', 'MRK']
        
        if symbol in tech_symbols:
            return 'Technology'
        elif symbol in finance_symbols:
            return 'Financials'
        elif symbol in healthcare_symbols:
            return 'Healthcare'
        else:
            return 'Other'
    
    def _update_daily_tracking(self):
        """Update daily tracking if new day"""
        today = datetime.now().date()
        if today != self.last_reset_date:
            self.daily_pnl = 0.0
            self.last_reset_date = today
            
            # Reset daily circuit breakers
            daily_breakers = [CircuitBreakerType.DAILY_LOSS]
            for breaker in daily_breakers:
                if breaker in self.circuit_breakers_active:
                    del self.circuit_breakers_active[breaker]
    
    def _log_safety_check(self, safety_status: Dict):
        """Log safety check results"""
        self._add_to_audit_trail("safety_check", {
            'status': safety_status,
            'active_breakers': list(self.circuit_breakers_active.keys()),
            'daily_pnl': self.daily_pnl,
            'consecutive_losses': self.consecutive_losses
        })
    
    def _add_to_audit_trail(self, action: str, data: Dict):
        """Add entry to audit trail"""
        try:
            audit_entry = {
                'timestamp': datetime.now().isoformat(),
                'action': action,
                'data': data
            }
            
            self.audit_trail.append(audit_entry)
            
            # Write to file
            with open(self.audit_file, 'a') as f:
                f.write(json.dumps(audit_entry) + '\n')
                
        except Exception as e:
            logger.error(f"Error adding to audit trail: {e}")
    
    def record_trade_outcome(self, symbol: str, pnl: float):
        """Record trade outcome for consecutive loss tracking"""
        try:
            if pnl < 0:
                self.consecutive_losses += 1
            else:
                self.consecutive_losses = 0
            
            self._add_to_audit_trail("trade_outcome", {
                'symbol': symbol,
                'pnl': pnl,
                'consecutive_losses': self.consecutive_losses
            })
            
        except Exception as e:
            logger.error(f"Error recording trade outcome: {e}")
    
    def reset_circuit_breaker(self, breaker_type: CircuitBreakerType):
        """Manually reset a circuit breaker"""
        try:
            if breaker_type in self.circuit_breakers_active:
                del self.circuit_breakers_active[breaker_type]
                
                logger.info(f"Circuit breaker reset: {breaker_type.value}")
                
                self._add_to_audit_trail("circuit_breaker_reset", {
                    'type': breaker_type.value
                })
                
        except Exception as e:
            logger.error(f"Error resetting circuit breaker: {e}")
    
    def emergency_shutdown(self, reason: str):
        """Emergency shutdown of all trading"""
        try:
            # Activate all circuit breakers
            for breaker_type in CircuitBreakerType:
                self.circuit_breakers_active[breaker_type] = True
            
            # Create emergency alert
            alert = SafetyAlert(
                alert_id=f"emergency_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                alert_type=CircuitBreakerType.UNUSUAL_MARKET,
                level=AlertLevel.EMERGENCY,
                message=f"EMERGENCY SHUTDOWN: {reason}",
                timestamp=datetime.now(),
                data={'reason': reason},
                action_taken="All trading halted"
            )
            
            self.safety_alerts.append(alert)
            
            logger.critical(f"EMERGENCY SHUTDOWN: {reason}")
            
            self._add_to_audit_trail("emergency_shutdown", {
                'reason': reason
            })
            
        except Exception as e:
            logger.error(f"Error during emergency shutdown: {e}")
    
    def get_safety_status(self) -> Dict:
        """Get current safety status"""
        return {
            'circuit_breakers_active': {k.value: v for k, v in self.circuit_breakers_active.items()},
            'daily_pnl': self.daily_pnl,
            'consecutive_losses': self.consecutive_losses,
            'portfolio_high_water_mark': self.portfolio_high_water_mark,
            'active_alerts': len([a for a in self.safety_alerts if not a.resolved]),
            'total_alerts': len(self.safety_alerts),
            'config': {
                'max_daily_loss_pct': self.config.max_daily_loss_pct,
                'max_portfolio_loss_pct': self.config.max_portfolio_loss_pct,
                'max_position_correlation': self.config.max_position_correlation,
                'max_vix_level': self.config.max_vix_level
            }
        }
    
    def get_recent_alerts(self, hours: int = 24) -> List[SafetyAlert]:
        """Get recent safety alerts"""
        cutoff = datetime.now() - timedelta(hours=hours)
        return [alert for alert in self.safety_alerts if alert.timestamp > cutoff]
    
    def is_trading_allowed(self) -> bool:
        """Check if trading is currently allowed"""
        return len(self.circuit_breakers_active) == 0
