<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TTM Squeeze Trading System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: #1a1d23;
            color: #ffffff;
            min-height: 100vh;
            padding: 24px;
            overflow-x: hidden;
        }

        .dashboard-container {
            max-width: 1600px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 24px;
            height: calc(100vh - 48px);
        }

        .panel {
            background: #2a2f3a;
            border-radius: 16px;
            padding: 24px;
            border: 1px solid #3a4047;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }

        .panel-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 24px;
            color: #ffffff;
            letter-spacing: 0.5px;
        }

        /* TTM Squeeze Scanner Panel */
        .scanner-panel {
            overflow-y: auto;
        }

        .scanner-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
        }

        .update-status {
            font-size: 11px;
            color: #8b949e;
            font-weight: 400;
        }

        .scanner-content {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }

        .scanner-subheader {
            display: grid;
            grid-template-columns: 80px 1fr;
            gap: 16px;
            margin-bottom: 16px;
            font-size: 12px;
            color: #8b949e;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .stock-item {
            display: grid;
            grid-template-columns: 80px 1fr;
            gap: 16px;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #3a4047;
        }

        .stock-item:last-child {
            border-bottom: none;
        }

        .stock-symbol {
            font-weight: 600;
            font-size: 14px;
            color: #ffffff;
            letter-spacing: 0.5px;
        }

        .squeeze-indicator {
            height: 12px;
            background: #1a1d23;
            border-radius: 6px;
            position: relative;
            overflow: hidden;
        }

        .squeeze-bar {
            height: 100%;
            border-radius: 6px;
            transition: width 0.3s ease;
        }

        .squeeze-green { background: #22c55e; }
        .squeeze-yellow { background: #eab308; }
        .squeeze-orange { background: #f97316; }
        .squeeze-red { background: #ef4444; }

        /* Signals Panel */
        .signals-panel {
            display: flex;
            flex-direction: column;
        }

        .signals-list {
            flex: 1;
            margin-bottom: 24px;
        }

        .signals-header {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
            margin-bottom: 16px;
            font-size: 12px;
            color: #8b949e;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .signal-item {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #3a4047;
        }

        .signal-item:last-child {
            border-bottom: none;
        }

        .signal-symbol {
            font-weight: 600;
            font-size: 14px;
            color: #ffffff;
            letter-spacing: 0.5px;
        }

        .buy-button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 8px;
            font-size: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            text-align: center;
        }

        .buy-button:hover {
            background: #2563eb;
            transform: translateY(-1px);
        }

        /* Trade Log */
        .trade-log {
            flex: 1;
        }

        .trade-log-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 24px;
            color: #ffffff;
            letter-spacing: 0.5px;
        }

        .trade-log-header {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 16px;
            padding: 8px 0;
            border-bottom: 1px solid #3a4047;
            font-size: 12px;
            color: #8b949e;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 16px;
        }

        .trade-log-item {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 16px;
            padding: 12px 0;
            border-bottom: 1px solid #3a4047;
            font-size: 14px;
            align-items: center;
        }

        .trade-log-item:last-child {
            border-bottom: none;
        }

        .trade-symbol {
            font-weight: 600;
            color: #ffffff;
            letter-spacing: 0.5px;
        }

        .trade-qty {
            color: #8b949e;
            font-weight: 500;
        }

        .profit-positive {
            color: #22c55e;
            font-weight: 600;
        }

        .profit-negative {
            color: #ef4444;
            font-weight: 600;
        }

        /* P&L Dashboard Panel */
        .pnl-panel {
            text-align: center;
        }

        .pnl-main {
            font-size: 64px;
            font-weight: 700;
            margin: 24px 0;
            color: #ffffff;
            letter-spacing: -1px;
        }

        .pnl-change {
            font-size: 18px;
            color: #22c55e;
            margin-bottom: 32px;
            font-weight: 600;
        }

        .pnl-stats {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 24px;
            margin-bottom: 32px;
        }

        .pnl-stat {
            text-align: center;
        }

        .pnl-stat-label {
            font-size: 12px;
            color: #8b949e;
            margin-bottom: 8px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .pnl-stat-value {
            font-size: 18px;
            font-weight: 600;
            color: #ffffff;
        }

        .pnl-chart {
            height: 140px;
            background: #1a1d23;
            border-radius: 12px;
            position: relative;
            overflow: hidden;
            border: 1px solid #3a4047;
        }

        .chart-line {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 85%;
            background: linear-gradient(to right,
                transparent 0%,
                #3b82f6 20%,
                #22c55e 40%,
                #3b82f6 60%,
                #22c55e 80%,
                #22c55e 100%);
            clip-path: polygon(0% 100%, 0% 70%, 8% 65%, 16% 60%, 24% 55%, 32% 50%, 40% 45%, 48% 40%, 56% 35%, 64% 30%, 72% 25%, 80% 20%, 88% 15%, 96% 10%, 100% 5%, 100% 100%);
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .dashboard-container {
                grid-template-columns: 1fr 1fr;
                grid-template-rows: auto auto;
            }
            
            .pnl-panel {
                grid-column: 1 / -1;
            }
        }

        @media (max-width: 768px) {
            .dashboard-container {
                grid-template-columns: 1fr;
                gap: 16px;
            }
            
            .panel {
                padding: 16px;
            }
            
            .pnl-main {
                font-size: 36px;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- TTM Squeeze Scanner Panel -->
        <div class="panel scanner-panel">
            <div class="scanner-header">
                <div class="panel-title">TTM Squeeze Scanner</div>
                <div class="update-status">Updating • > 5 m</div>
            </div>

            <div class="scanner-subheader">
                <div>Symbol</div>
                <div></div>
            </div>

            <div class="scanner-content" id="scannerContent">
                <!-- Scanner results will be populated here -->
                <div class="stock-item">
                    <div class="stock-symbol">IBM</div>
                    <div class="squeeze-indicator">
                        <div class="squeeze-bar squeeze-green" style="width: 65%;"></div>
                    </div>
                </div>
                <div class="stock-item">
                    <div class="stock-symbol">TSLA</div>
                    <div class="squeeze-indicator">
                        <div class="squeeze-bar squeeze-green" style="width: 45%;"></div>
                    </div>
                </div>
                <div class="stock-item">
                    <div class="stock-symbol">AAPL</div>
                    <div class="squeeze-indicator">
                        <div class="squeeze-bar squeeze-orange" style="width: 70%;"></div>
                    </div>
                </div>
                <div class="stock-item">
                    <div class="stock-symbol">MSFT</div>
                    <div class="squeeze-indicator">
                        <div class="squeeze-bar squeeze-orange" style="width: 50%;"></div>
                    </div>
                </div>
                <div class="stock-item">
                    <div class="stock-symbol">NVDA</div>
                    <div class="squeeze-indicator">
                        <div class="squeeze-bar squeeze-red" style="width: 80%;"></div>
                    </div>
                </div>
                <div class="stock-item">
                    <div class="stock-symbol">BA</div>
                    <div class="squeeze-indicator">
                        <div class="squeeze-bar squeeze-red" style="width: 75%;"></div>
                    </div>
                </div>
                <div class="stock-item">
                    <div class="stock-symbol">XOM</div>
                    <div class="squeeze-indicator">
                        <div class="squeeze-bar squeeze-orange" style="width: 40%;"></div>
                    </div>
                </div>
                <div class="stock-item">
                    <div class="stock-symbol">GOOGL</div>
                    <div class="squeeze-indicator">
                        <div class="squeeze-bar squeeze-orange" style="width: 60%;"></div>
                    </div>
                </div>
                <div class="stock-item">
                    <div class="stock-symbol">CSCO</div>
                    <div class="squeeze-indicator">
                        <div class="squeeze-bar squeeze-green" style="width: 55%;"></div>
                    </div>
                </div>
                <div class="stock-item">
                    <div class="stock-symbol">AMD</div>
                    <div class="squeeze-indicator">
                        <div class="squeeze-bar squeeze-green" style="width: 65%;"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Signals Panel -->
        <div class="panel signals-panel">
            <div class="panel-title">Signals</div>

            <div class="signals-header">
                <div>Signal</div>
                <div>Action</div>
            </div>

            <div class="signals-list" id="signalsList">
                <!-- Signals will be populated here -->
                <div class="signal-item">
                    <div class="signal-symbol">AAPL</div>
                    <button class="buy-button">Buy Bracket</button>
                </div>
                <div class="signal-item">
                    <div class="signal-symbol">TSLA</div>
                    <button class="buy-button">Buy Bracket</button>
                </div>
                <div class="signal-item">
                    <div class="signal-symbol">MSFT</div>
                    <button class="buy-button">Buy Bracket</button>
                </div>
            </div>

            <div class="trade-log">
                <div class="trade-log-title">Trade Log</div>
                <div class="trade-log-header">
                    <div>Symbol</div>
                    <div>Qty</div>
                    <div>P&L</div>
                </div>
                <div id="tradeLogContent">
                    <!-- Trade log will be populated here -->
                    <div class="trade-log-item">
                        <div class="trade-symbol">AAPL</div>
                        <div class="trade-qty">100</div>
                        <div class="profit-positive">$250,00</div>
                    </div>
                    <div class="trade-log-item">
                        <div class="trade-symbol">TSLA</div>
                        <div class="trade-qty">100</div>
                        <div class="profit-positive">$180,00</div>
                    </div>
                    <div class="trade-log-item">
                        <div class="trade-symbol">MSFT</div>
                        <div class="trade-qty">100</div>
                        <div class="profit-positive">$444,50</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Live P&L Dashboard Panel -->
        <div class="panel pnl-panel">
            <div class="panel-title">Live P&L Dashboard</div>
            
            <div class="pnl-main" id="pnlMain">179.82</div>
            <div class="pnl-change" id="pnlChange">+1,50 (+0,89 %)</div>
            
            <div class="pnl-stats">
                <div class="pnl-stat">
                    <div class="pnl-stat-label">P&L</div>
                    <div class="pnl-stat-value profit-positive" id="pnlValue">$874,50</div>
                </div>
                <div class="pnl-stat">
                    <div class="pnl-stat-label">Exposure</div>
                    <div class="pnl-stat-value" id="exposureValue">50,000</div>
                </div>
                <div class="pnl-stat">
                    <div class="pnl-stat-label">Win Rate</div>
                    <div class="pnl-stat-value" id="winRateValue">75,2%</div>
                </div>
            </div>
            
            <div class="pnl-chart">
                <div class="chart-line"></div>
            </div>

            <!-- Additional P&L Data -->
            <div style="margin-top: 24px; display: grid; grid-template-columns: 1fr 1fr; gap: 16px; font-size: 14px;">
                <div style="text-align: left;">
                    <div style="color: #8b949e; font-size: 12px; margin-bottom: 4px;">P&L</div>
                    <div style="color: #ffffff; font-weight: 600;">AAPL</div>
                    <div style="color: #ffffff; font-weight: 600;">TSLA</div>
                    <div style="color: #ffffff; font-weight: 600;">TSLA</div>
                </div>
                <div style="text-align: right;">
                    <div style="color: #8b949e; font-size: 12px; margin-bottom: 4px;">P&L</div>
                    <div style="color: #22c55e; font-weight: 600;">$250,00</div>
                    <div style="color: #22c55e; font-weight: 600;">$180,00</div>
                    <div style="color: #22c55e; font-weight: 600;">$444,50</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Real-time data fetching functions
        async function fetchScannerData() {
            try {
                const response = await fetch('/api/scanner/alerts');
                const data = await response.json();
                updateScannerDisplay(data);
            } catch (error) {
                console.error('Error fetching scanner data:', error);
            }
        }

        async function fetchSignalsData() {
            try {
                const response = await fetch('/api/trading/signals');
                const data = await response.json();
                updateSignalsDisplay(data);
            } catch (error) {
                console.error('Error fetching signals data:', error);
            }
        }

        async function fetchPnLData() {
            try {
                const response = await fetch('/api/trading/pnl');
                const data = await response.json();
                updatePnLDisplay(data);
            } catch (error) {
                console.error('Error fetching P&L data:', error);
            }
        }

        function updateScannerDisplay(data) {
            // Scanner data is already populated in HTML for demo
            // This function would update with real data
        }

        function updateSignalsDisplay(data) {
            // Signals data is already populated in HTML for demo
            // This function would update with real data
        }

        function updatePnLDisplay(data) {
            // P&L data is already populated in HTML for demo
            // This function would update with real data
        }

        // Initialize dashboard
        function initializeDashboard() {
            // Static demo data is already in HTML
            // In production, these would fetch real data:
            // fetchScannerData();
            // fetchSignalsData();
            // fetchPnLData();
        }

        // Auto-refresh every 30 seconds
        setInterval(() => {
            // fetchScannerData();
            // fetchSignalsData();
            // fetchPnLData();
        }, 30000);

        // Initialize dashboard when page loads
        document.addEventListener('DOMContentLoaded', function() {
            initializeDashboard();
        });
    </script>
</body>
</html>
