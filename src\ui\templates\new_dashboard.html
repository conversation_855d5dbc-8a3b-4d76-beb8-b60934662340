<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TTM Squeeze Trading System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #0f1419;
            color: #ffffff;
            min-height: 100vh;
            padding: 20px;
        }

        .dashboard-container {
            max-width: 1400px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
            height: calc(100vh - 40px);
        }

        .panel {
            background: #1a1f2e;
            border-radius: 12px;
            padding: 24px;
            border: 1px solid #2d3748;
        }

        .panel-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #ffffff;
        }

        /* TTM Squeeze Scanner Panel */
        .scanner-panel {
            overflow-y: auto;
        }

        .scanner-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .update-status {
            font-size: 12px;
            color: #6c757d;
        }

        .stock-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 0;
            border-bottom: 1px solid #2d3748;
        }

        .stock-item:last-child {
            border-bottom: none;
        }

        .stock-symbol {
            font-weight: 600;
            font-size: 14px;
            width: 60px;
        }

        .squeeze-indicator {
            flex: 1;
            margin: 0 16px;
            height: 8px;
            background: #2d3748;
            border-radius: 4px;
            position: relative;
            overflow: hidden;
        }

        .squeeze-bar {
            height: 100%;
            border-radius: 4px;
            width: 60%;
        }

        .squeeze-green { background: #00ff88; }
        .squeeze-yellow { background: #ffa502; }
        .squeeze-orange { background: #ff6348; }
        .squeeze-red { background: #ff4757; }

        /* Signals Panel */
        .signals-panel {
            display: flex;
            flex-direction: column;
        }

        .signals-list {
            flex: 1;
            margin-bottom: 20px;
        }

        .signal-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #2d3748;
        }

        .signal-item:last-child {
            border-bottom: none;
        }

        .signal-symbol {
            font-weight: 600;
            font-size: 14px;
        }

        .buy-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 6px 16px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: background 0.2s;
        }

        .buy-button:hover {
            background: #0056b3;
        }

        /* Trade Log */
        .trade-log {
            flex: 1;
        }

        .trade-log-header {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr;
            gap: 16px;
            padding: 8px 0;
            border-bottom: 1px solid #2d3748;
            font-size: 12px;
            color: #6c757d;
            font-weight: 600;
            margin-bottom: 12px;
        }

        .trade-log-item {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr;
            gap: 16px;
            padding: 8px 0;
            font-size: 14px;
        }

        .profit-positive {
            color: #00ff88;
        }

        .profit-negative {
            color: #ff4757;
        }

        /* P&L Dashboard Panel */
        .pnl-panel {
            text-align: center;
        }

        .pnl-main {
            font-size: 48px;
            font-weight: 700;
            margin: 20px 0;
        }

        .pnl-change {
            font-size: 16px;
            color: #00ff88;
            margin-bottom: 30px;
        }

        .pnl-stats {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }

        .pnl-stat {
            text-align: center;
        }

        .pnl-stat-label {
            font-size: 12px;
            color: #6c757d;
            margin-bottom: 4px;
        }

        .pnl-stat-value {
            font-size: 16px;
            font-weight: 600;
        }

        .pnl-chart {
            height: 120px;
            background: #2d3748;
            border-radius: 8px;
            position: relative;
            overflow: hidden;
        }

        .chart-line {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80%;
            background: linear-gradient(to right, 
                transparent 0%, 
                #007bff 20%, 
                #00ff88 40%, 
                #007bff 60%, 
                #00ff88 80%, 
                #00ff88 100%);
            clip-path: polygon(0% 100%, 0% 60%, 10% 55%, 20% 45%, 30% 40%, 40% 35%, 50% 30%, 60% 25%, 70% 20%, 80% 15%, 90% 10%, 100% 5%, 100% 100%);
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .dashboard-container {
                grid-template-columns: 1fr 1fr;
                grid-template-rows: auto auto;
            }
            
            .pnl-panel {
                grid-column: 1 / -1;
            }
        }

        @media (max-width: 768px) {
            .dashboard-container {
                grid-template-columns: 1fr;
                gap: 16px;
            }
            
            .panel {
                padding: 16px;
            }
            
            .pnl-main {
                font-size: 36px;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- TTM Squeeze Scanner Panel -->
        <div class="panel scanner-panel">
            <div class="scanner-header">
                <div class="panel-title">TTM Squeeze Scanner</div>
                <div class="update-status">Updating • > 5 m</div>
            </div>
            
            <div class="scanner-content" id="scannerContent">
                <!-- Scanner results will be populated here -->
            </div>
        </div>

        <!-- Signals Panel -->
        <div class="panel signals-panel">
            <div class="panel-title">Signals</div>
            
            <div class="signals-list" id="signalsList">
                <!-- Signals will be populated here -->
            </div>
            
            <div class="trade-log">
                <div class="panel-title">Trade Log</div>
                <div class="trade-log-header">
                    <div>Symbol</div>
                    <div>Qty</div>
                    <div>P&L</div>
                    <div>P&L</div>
                </div>
                <div id="tradeLogContent">
                    <!-- Trade log will be populated here -->
                </div>
            </div>
        </div>

        <!-- Live P&L Dashboard Panel -->
        <div class="panel pnl-panel">
            <div class="panel-title">Live P&L Dashboard</div>
            
            <div class="pnl-main" id="pnlMain">179.82</div>
            <div class="pnl-change" id="pnlChange">+1,50 (+0,89 %)</div>
            
            <div class="pnl-stats">
                <div class="pnl-stat">
                    <div class="pnl-stat-label">P&L</div>
                    <div class="pnl-stat-value profit-positive" id="pnlValue">$874,50</div>
                </div>
                <div class="pnl-stat">
                    <div class="pnl-stat-label">Exposure</div>
                    <div class="pnl-stat-value" id="exposureValue">50,000</div>
                </div>
                <div class="pnl-stat">
                    <div class="pnl-stat-label">Win Rate</div>
                    <div class="pnl-stat-value" id="winRateValue">75,2%</div>
                </div>
            </div>
            
            <div class="pnl-chart">
                <div class="chart-line"></div>
            </div>
        </div>
    </div>

    <script>
        // Sample data for demonstration
        const sampleStocks = [
            { symbol: 'IBM', strength: 60, color: 'green' },
            { symbol: 'TSLA', strength: 40, color: 'yellow' },
            { symbol: 'AAPL', strength: 70, color: 'orange' },
            { symbol: 'MSFT', strength: 45, color: 'orange' },
            { symbol: 'NVDA', strength: 85, color: 'red' },
            { symbol: 'BA', strength: 80, color: 'red' },
            { symbol: 'XOM', strength: 35, color: 'orange' },
            { symbol: 'GOOGL', strength: 55, color: 'orange' },
            { symbol: 'CSCO', strength: 50, color: 'green' },
            { symbol: 'AMD', strength: 65, color: 'green' }
        ];

        const sampleSignals = [
            { symbol: 'AAPL' },
            { symbol: 'TSLA' },
            { symbol: 'MSFT' }
        ];

        const sampleTrades = [
            { symbol: 'AAPL', qty: 100, pnl: '$250,00', pnl2: '$250,00' },
            { symbol: 'TSLA', qty: 100, pnl: '$180,00', pnl2: '$180,00' },
            { symbol: 'MSFT', qty: 100, pnl: '$444,50', pnl2: '$444,50' }
        ];

        // Populate scanner
        function populateScanner() {
            const container = document.getElementById('scannerContent');
            container.innerHTML = sampleStocks.map(stock => `
                <div class="stock-item">
                    <div class="stock-symbol">${stock.symbol}</div>
                    <div class="squeeze-indicator">
                        <div class="squeeze-bar squeeze-${stock.color}" style="width: ${stock.strength}%"></div>
                    </div>
                </div>
            `).join('');
        }

        // Populate signals
        function populateSignals() {
            const container = document.getElementById('signalsList');
            container.innerHTML = sampleSignals.map(signal => `
                <div class="signal-item">
                    <div class="signal-symbol">${signal.symbol}</div>
                    <button class="buy-button">Buy Bracket</button>
                </div>
            `).join('');
        }

        // Populate trade log
        function populateTradeLog() {
            const container = document.getElementById('tradeLogContent');
            container.innerHTML = sampleTrades.map(trade => `
                <div class="trade-log-item">
                    <div>${trade.symbol}</div>
                    <div>${trade.qty}</div>
                    <div class="profit-positive">${trade.pnl}</div>
                    <div class="profit-positive">${trade.pnl2}</div>
                </div>
            `).join('');
        }

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            populateScanner();
            populateSignals();
            populateTradeLog();
        });

        // Auto-refresh functionality
        setInterval(() => {
            // In a real implementation, this would fetch fresh data
            console.log('Refreshing data...');
        }, 5000);
    </script>
</body>
</html>
