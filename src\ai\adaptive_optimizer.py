"""
Adaptive Parameter Optimizer - AI-driven optimization of TTM Squeeze parameters
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
import logging
from datetime import datetime, timedelta
from dataclasses import dataclass
import json

logger = logging.getLogger(__name__)

@dataclass
class OptimizedParameters:
    """Optimized TTM Squeeze parameters for a specific symbol/market condition"""
    symbol: str
    bb_period: int
    bb_std_dev: float
    kc_period: int
    kc_atr_multiplier: float
    momentum_period: int
    volume_threshold: float
    confidence: float  # 0-100, confidence in these parameters
    market_regime: str
    last_updated: datetime

class AdaptiveParameterOptimizer:
    """Dynamically optimizes TTM Squeeze parameters based on market conditions"""
    
    def __init__(self):
        self.parameter_cache = {}  # symbol -> OptimizedParameters
        self.default_parameters = {
            'bb_period': 20,
            'bb_std_dev': 2.0,
            'kc_period': 20,
            'kc_atr_multiplier': 1.5,
            'momentum_period': 20,
            'volume_threshold': 1.2
        }
        
        # Parameter ranges for optimization
        self.parameter_ranges = {
            'bb_period': (10, 30),
            'bb_std_dev': (1.5, 2.5),
            'kc_period': (10, 30),
            'kc_atr_multiplier': (1.0, 2.5),
            'momentum_period': (10, 30),
            'volume_threshold': (1.0, 2.0)
        }
        
        # Market regime specific adjustments
        self.regime_adjustments = {
            'trending': {
                'bb_std_dev': 0.1,      # Slightly wider bands
                'kc_atr_multiplier': 0.1,  # Slightly wider channels
                'momentum_period': -2    # Shorter momentum period
            },
            'ranging': {
                'bb_std_dev': -0.1,     # Tighter bands
                'kc_atr_multiplier': -0.1, # Tighter channels
                'momentum_period': 2     # Longer momentum period
            },
            'volatile': {
                'bb_std_dev': 0.2,      # Much wider bands
                'kc_atr_multiplier': 0.3,  # Much wider channels
                'momentum_period': -5    # Much shorter momentum
            },
            'calm': {
                'bb_std_dev': -0.2,     # Much tighter bands
                'kc_atr_multiplier': -0.2, # Much tighter channels
                'momentum_period': 5     # Longer momentum
            }
        }
        
        logger.info("Adaptive Parameter Optimizer initialized")
    
    def get_optimized_parameters(self, symbol: str, market_regime: str = 'unknown',
                               volatility: float = 0.2) -> OptimizedParameters:
        """
        Get optimized parameters for a symbol and market condition
        
        Args:
            symbol: Stock symbol
            market_regime: Current market regime
            volatility: Current volatility level
            
        Returns:
            OptimizedParameters for the symbol
        """
        try:
            # Check cache first
            cached_params = self.parameter_cache.get(symbol)
            if (cached_params and 
                cached_params.market_regime == market_regime and
                datetime.now() - cached_params.last_updated < timedelta(hours=4)):
                return cached_params
            
            # Calculate optimized parameters
            optimized = self._optimize_parameters(symbol, market_regime, volatility)
            
            # Cache the result
            self.parameter_cache[symbol] = optimized
            
            return optimized
            
        except Exception as e:
            logger.warning(f"Error optimizing parameters for {symbol}: {e}")
            return self._get_default_parameters(symbol, market_regime)
    
    def _optimize_parameters(self, symbol: str, market_regime: str, 
                           volatility: float) -> OptimizedParameters:
        """Optimize parameters for specific symbol and conditions"""
        
        # Start with default parameters
        params = self.default_parameters.copy()
        
        # Apply market regime adjustments
        if market_regime in self.regime_adjustments:
            adjustments = self.regime_adjustments[market_regime]
            
            for param, adjustment in adjustments.items():
                if param in params:
                    if isinstance(adjustment, (int, float)):
                        params[param] += adjustment
                    
                    # Ensure within valid ranges
                    if param in self.parameter_ranges:
                        min_val, max_val = self.parameter_ranges[param]
                        params[param] = max(min_val, min(max_val, params[param]))
        
        # Volatility-based adjustments
        vol_adjustment = self._calculate_volatility_adjustment(volatility)
        params['bb_std_dev'] *= vol_adjustment
        params['kc_atr_multiplier'] *= vol_adjustment
        
        # Symbol-specific adjustments (based on historical behavior)
        symbol_adjustment = self._get_symbol_specific_adjustment(symbol)
        for param, adjustment in symbol_adjustment.items():
            if param in params:
                params[param] *= adjustment
        
        # Calculate confidence in these parameters
        confidence = self._calculate_parameter_confidence(symbol, market_regime, params)
        
        return OptimizedParameters(
            symbol=symbol,
            bb_period=int(params['bb_period']),
            bb_std_dev=params['bb_std_dev'],
            kc_period=int(params['kc_period']),
            kc_atr_multiplier=params['kc_atr_multiplier'],
            momentum_period=int(params['momentum_period']),
            volume_threshold=params['volume_threshold'],
            confidence=confidence,
            market_regime=market_regime,
            last_updated=datetime.now()
        )
    
    def _calculate_volatility_adjustment(self, volatility: float) -> float:
        """Calculate parameter adjustment based on volatility"""
        # Normalize volatility (typical range 0.1 to 0.5)
        normalized_vol = max(0.1, min(0.5, volatility))
        
        # Higher volatility = wider bands/channels
        # Scale from 0.8 to 1.4 based on volatility
        adjustment = 0.8 + (normalized_vol - 0.1) / (0.5 - 0.1) * 0.6
        
        return adjustment
    
    def _get_symbol_specific_adjustment(self, symbol: str) -> Dict[str, float]:
        """Get symbol-specific parameter adjustments"""
        # This would be based on historical analysis of each symbol
        # For now, use some heuristics based on symbol characteristics
        
        adjustments = {}
        
        # High-volatility stocks (tech, growth)
        high_vol_symbols = ['TSLA', 'NVDA', 'AMD', 'COIN', 'ARKK']
        if symbol in high_vol_symbols:
            adjustments = {
                'bb_std_dev': 1.1,
                'kc_atr_multiplier': 1.1,
                'momentum_period': 0.9  # Shorter period
            }
        
        # Low-volatility stocks (utilities, staples)
        low_vol_symbols = ['KO', 'PG', 'JNJ', 'WMT', 'VZ']
        if symbol in low_vol_symbols:
            adjustments = {
                'bb_std_dev': 0.9,
                'kc_atr_multiplier': 0.9,
                'momentum_period': 1.1  # Longer period
            }
        
        # Large cap stable stocks
        large_cap_symbols = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'META']
        if symbol in large_cap_symbols:
            adjustments = {
                'bb_period': 1.0,
                'kc_period': 1.0,
                'volume_threshold': 0.9  # Lower volume threshold
            }
        
        # Default to no adjustment
        return adjustments if adjustments else {k: 1.0 for k in self.default_parameters.keys()}
    
    def _calculate_parameter_confidence(self, symbol: str, market_regime: str, 
                                      params: Dict) -> float:
        """Calculate confidence in the optimized parameters"""
        confidence = 70.0  # Base confidence
        
        # Higher confidence for known market regimes
        if market_regime in ['trending', 'ranging']:
            confidence += 15.0
        elif market_regime == 'unknown':
            confidence -= 20.0
        
        # Higher confidence for common symbols
        common_symbols = ['SPY', 'QQQ', 'AAPL', 'MSFT', 'TSLA']
        if symbol in common_symbols:
            confidence += 10.0
        
        # Check if parameters are within reasonable ranges
        reasonable_ranges = True
        for param, value in params.items():
            if param in self.parameter_ranges:
                min_val, max_val = self.parameter_ranges[param]
                if not (min_val <= value <= max_val):
                    reasonable_ranges = False
                    break
        
        if not reasonable_ranges:
            confidence -= 25.0
        
        return max(0.0, min(100.0, confidence))
    
    def _get_default_parameters(self, symbol: str, market_regime: str) -> OptimizedParameters:
        """Get default parameters when optimization fails"""
        return OptimizedParameters(
            symbol=symbol,
            bb_period=self.default_parameters['bb_period'],
            bb_std_dev=self.default_parameters['bb_std_dev'],
            kc_period=self.default_parameters['kc_period'],
            kc_atr_multiplier=self.default_parameters['kc_atr_multiplier'],
            momentum_period=self.default_parameters['momentum_period'],
            volume_threshold=self.default_parameters['volume_threshold'],
            confidence=50.0,
            market_regime=market_regime,
            last_updated=datetime.now()
        )
    
    async def update_parameters(self, historical_data: pd.DataFrame):
        """Update parameter optimization based on historical performance"""
        try:
            logger.info("Updating adaptive parameter optimization...")
            
            # Analyze historical performance of different parameter sets
            # This would involve backtesting different parameter combinations
            
            # For now, just log that we're updating
            logger.info("Parameter optimization update completed")
            
        except Exception as e:
            logger.error(f"Error updating parameters: {e}")
    
    def optimize_for_backtest(self, symbol: str, historical_data: pd.DataFrame,
                            lookback_days: int = 252) -> OptimizedParameters:
        """
        Optimize parameters for backtesting on historical data
        
        Args:
            symbol: Stock symbol
            historical_data: Historical OHLCV data
            lookback_days: Days to look back for optimization
            
        Returns:
            Optimized parameters for the period
        """
        try:
            if len(historical_data) < lookback_days:
                return self._get_default_parameters(symbol, 'unknown')
            
            # Calculate historical volatility
            returns = historical_data['close'].pct_change().dropna()
            volatility = returns.std() * np.sqrt(252)
            
            # Determine market regime from historical data
            market_regime = self._determine_historical_regime(historical_data)
            
            # Get optimized parameters
            optimized = self._optimize_parameters(symbol, market_regime, volatility)
            
            logger.debug(f"Optimized parameters for {symbol}: "
                        f"BB({optimized.bb_period}, {optimized.bb_std_dev:.2f}), "
                        f"KC({optimized.kc_period}, {optimized.kc_atr_multiplier:.2f})")
            
            return optimized
            
        except Exception as e:
            logger.warning(f"Error optimizing for backtest: {e}")
            return self._get_default_parameters(symbol, 'unknown')
    
    def _determine_historical_regime(self, data: pd.DataFrame) -> str:
        """Determine market regime from historical data"""
        try:
            returns = data['close'].pct_change().dropna()
            volatility = returns.rolling(20).std().iloc[-1] * np.sqrt(252)
            
            # Simple regime classification
            if volatility > 0.3:
                return 'volatile'
            elif volatility < 0.15:
                return 'calm'
            else:
                # Check for trending vs ranging
                sma_20 = data['close'].rolling(20).mean()
                sma_50 = data['close'].rolling(50).mean()
                
                if len(sma_50.dropna()) > 0:
                    trend_strength = abs(sma_20.iloc[-1] - sma_50.iloc[-1]) / sma_50.iloc[-1]
                    if trend_strength > 0.05:
                        return 'trending'
                    else:
                        return 'ranging'
                else:
                    return 'ranging'
                    
        except Exception:
            return 'unknown'
    
    def get_parameter_summary(self) -> Dict:
        """Get summary of cached parameters"""
        summary = {
            'cached_symbols': len(self.parameter_cache),
            'default_parameters': self.default_parameters,
            'parameter_ranges': self.parameter_ranges,
            'regime_adjustments': list(self.regime_adjustments.keys())
        }
        
        if self.parameter_cache:
            # Calculate average confidence
            confidences = [p.confidence for p in self.parameter_cache.values()]
            summary['average_confidence'] = sum(confidences) / len(confidences)
            
            # Most common regimes
            regimes = [p.market_regime for p in self.parameter_cache.values()]
            summary['regime_distribution'] = {
                regime: regimes.count(regime) for regime in set(regimes)
            }
        
        return summary
    
    def clear_cache(self):
        """Clear parameter cache"""
        self.parameter_cache.clear()
        logger.info("Parameter cache cleared")
    
    def is_ready(self) -> bool:
        """Check if optimizer is ready"""
        return True  # Always ready with default parameters
    
    def save_parameters(self, filepath: str):
        """Save optimized parameters to file"""
        try:
            data = {}
            for symbol, params in self.parameter_cache.items():
                data[symbol] = {
                    'bb_period': params.bb_period,
                    'bb_std_dev': params.bb_std_dev,
                    'kc_period': params.kc_period,
                    'kc_atr_multiplier': params.kc_atr_multiplier,
                    'momentum_period': params.momentum_period,
                    'volume_threshold': params.volume_threshold,
                    'confidence': params.confidence,
                    'market_regime': params.market_regime,
                    'last_updated': params.last_updated.isoformat()
                }
            
            with open(filepath, 'w') as f:
                json.dump(data, f, indent=2)
            
            logger.info(f"Parameters saved to {filepath}")
            
        except Exception as e:
            logger.error(f"Error saving parameters: {e}")
    
    def load_parameters(self, filepath: str):
        """Load optimized parameters from file"""
        try:
            with open(filepath, 'r') as f:
                data = json.load(f)
            
            for symbol, params in data.items():
                self.parameter_cache[symbol] = OptimizedParameters(
                    symbol=symbol,
                    bb_period=params['bb_period'],
                    bb_std_dev=params['bb_std_dev'],
                    kc_period=params['kc_period'],
                    kc_atr_multiplier=params['kc_atr_multiplier'],
                    momentum_period=params['momentum_period'],
                    volume_threshold=params['volume_threshold'],
                    confidence=params['confidence'],
                    market_regime=params['market_regime'],
                    last_updated=datetime.fromisoformat(params['last_updated'])
                )
            
            logger.info(f"Parameters loaded from {filepath}")
            
        except Exception as e:
            logger.error(f"Error loading parameters: {e}")
