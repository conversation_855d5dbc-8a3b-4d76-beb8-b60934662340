"""
Consolidated Utilities Module for TTM Squeeze Trading System
Combines logging, configuration, and helper functions
"""

import logging
import os
import json
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
import asyncio
from pathlib import Path

# ============================================================================
# LOGGING CONFIGURATION
# ============================================================================

def setup_logging(log_level: str = "INFO", log_file: Optional[str] = None) -> logging.Logger:
    """Setup comprehensive logging configuration"""
    
    # Create logs directory if it doesn't exist
    if log_file:
        log_dir = Path(log_file).parent
        log_dir.mkdir(exist_ok=True)
    
    # Configure logging format
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    date_format = '%Y-%m-%d %H:%M:%S'
    
    # Set up root logger
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format=log_format,
        datefmt=date_format,
        handlers=[
            logging.StreamHandler(),  # Console output
            logging.FileHandler(log_file) if log_file else logging.NullHandler()
        ]
    )
    
    # Configure specific loggers
    loggers_config = {
        'aiohttp': logging.WARNING,
        'urllib3': logging.WARNING,
        'asyncio': logging.WARNING,
        'werkzeug': logging.WARNING
    }
    
    for logger_name, level in loggers_config.items():
        logging.getLogger(logger_name).setLevel(level)
    
    logger = logging.getLogger(__name__)
    logger.info(f"Logging configured - Level: {log_level}, File: {log_file or 'Console only'}")
    
    return logger

# ============================================================================
# CONFIGURATION MANAGEMENT
# ============================================================================

class ConfigManager:
    """Centralized configuration management"""
    
    def __init__(self, config_file: str = "config.json"):
        self.config_file = config_file
        self.config = {}
        self.load_config()
    
    def load_config(self):
        """Load configuration from file"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    self.config = json.load(f)
            else:
                self.config = self.get_default_config()
                self.save_config()
            
            logging.info(f"Configuration loaded from {self.config_file}")
            
        except Exception as e:
            logging.error(f"Error loading config: {e}")
            self.config = self.get_default_config()
    
    def save_config(self):
        """Save configuration to file"""
        try:
            with open(self.config_file, 'w') as f:
                json.dump(self.config, f, indent=4)
            
            logging.info(f"Configuration saved to {self.config_file}")
            
        except Exception as e:
            logging.error(f"Error saving config: {e}")
    
    def get_default_config(self) -> Dict[str, Any]:
        """Get default configuration"""
        return {
            "system": {
                "name": "TTM Squeeze Trading System",
                "version": "2.0.0",
                "environment": "development",
                "debug": True,
                "timeframes": ["5Min", "15Min"],  # Optimized for intraday
                "scan_interval": 30,
                "max_concurrent_scans": 20
            },
            "data": {
                "primary_provider": "alpaca",
                "fallback_provider": "fmp",
                "cache_duration_minutes": 1,
                "max_historical_bars": 100
            },
            "trading": {
                "enabled": True,
                "paper_trading": True,
                "max_position_size": 0.05,
                "max_daily_trades": 10,
                "risk_per_trade": 0.02
            },
            "ai": {
                "enabled": True,
                "confidence_threshold": 0.7,
                "risk_assessment": True,
                "automated_trading": False,
                "chat_assistant": True
            },
            "alerts": {
                "max_alerts": 1000,
                "cooldown_minutes": 15,
                "min_confidence": 0.6,
                "required_confirmations": 1  # Reduced for intraday focus
            },
            "web": {
                "host": "127.0.0.1",
                "port": 5000,
                "debug": True,
                "cors_enabled": True
            }
        }
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value using dot notation"""
        try:
            keys = key.split('.')
            value = self.config
            
            for k in keys:
                value = value[k]
            
            return value
            
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any):
        """Set configuration value using dot notation"""
        try:
            keys = key.split('.')
            config = self.config
            
            for k in keys[:-1]:
                if k not in config:
                    config[k] = {}
                config = config[k]
            
            config[keys[-1]] = value
            self.save_config()
            
        except Exception as e:
            logging.error(f"Error setting config {key}: {e}")
    
    def update(self, updates: Dict[str, Any]):
        """Update multiple configuration values"""
        try:
            for key, value in updates.items():
                self.set(key, value)
            
            logging.info(f"Configuration updated with {len(updates)} changes")
            
        except Exception as e:
            logging.error(f"Error updating config: {e}")

# ============================================================================
# DATA VALIDATION AND PROCESSING
# ============================================================================

class DataValidator:
    """Validate and clean trading data"""
    
    @staticmethod
    def validate_ohlcv_data(data: pd.DataFrame) -> bool:
        """Validate OHLCV data structure and values"""
        try:
            required_columns = ['open', 'high', 'low', 'close', 'volume']
            
            # Check required columns
            if not all(col in data.columns for col in required_columns):
                return False
            
            # Check for empty data
            if len(data) == 0:
                return False
            
            # Check for valid price relationships
            invalid_prices = (
                (data['high'] < data['low']) |
                (data['high'] < data['open']) |
                (data['high'] < data['close']) |
                (data['low'] > data['open']) |
                (data['low'] > data['close']) |
                (data['open'] <= 0) |
                (data['close'] <= 0)
            )
            
            if invalid_prices.any():
                logging.warning(f"Found {invalid_prices.sum()} invalid price relationships")
                return False
            
            # Check for reasonable volume
            if (data['volume'] < 0).any():
                return False
            
            return True
            
        except Exception as e:
            logging.error(f"Error validating OHLCV data: {e}")
            return False
    
    @staticmethod
    def clean_ohlcv_data(data: pd.DataFrame) -> pd.DataFrame:
        """Clean and prepare OHLCV data"""
        try:
            df = data.copy()
            
            # Remove rows with NaN values
            df = df.dropna()
            
            # Ensure positive prices
            price_columns = ['open', 'high', 'low', 'close']
            for col in price_columns:
                if col in df.columns:
                    df = df[df[col] > 0]
            
            # Ensure non-negative volume
            if 'volume' in df.columns:
                df = df[df['volume'] >= 0]
            
            # Sort by timestamp if available
            if 'timestamp' in df.columns:
                df = df.sort_values('timestamp')
            
            # Reset index
            df = df.reset_index(drop=True)
            
            return df
            
        except Exception as e:
            logging.error(f"Error cleaning OHLCV data: {e}")
            return data
    
    @staticmethod
    def validate_symbol(symbol: str) -> bool:
        """Validate stock symbol format"""
        try:
            if not symbol or not isinstance(symbol, str):
                return False
            
            # Basic symbol validation
            symbol = symbol.upper().strip()
            
            # Check length (1-5 characters typical)
            if len(symbol) < 1 or len(symbol) > 10:
                return False
            
            # Check for valid characters (letters and dots)
            if not symbol.replace('.', '').isalpha():
                return False
            
            return True
            
        except Exception:
            return False

# ============================================================================
# PERFORMANCE MONITORING
# ============================================================================

class PerformanceMonitor:
    """Monitor system performance and metrics"""
    
    def __init__(self):
        self.metrics = {}
        self.start_time = datetime.now()
    
    def record_metric(self, name: str, value: Union[int, float], timestamp: Optional[datetime] = None):
        """Record a performance metric"""
        try:
            if timestamp is None:
                timestamp = datetime.now()
            
            if name not in self.metrics:
                self.metrics[name] = []
            
            self.metrics[name].append({
                'value': value,
                'timestamp': timestamp
            })
            
            # Keep only last 1000 entries per metric
            if len(self.metrics[name]) > 1000:
                self.metrics[name] = self.metrics[name][-1000:]
                
        except Exception as e:
            logging.error(f"Error recording metric {name}: {e}")
    
    def get_metric_summary(self, name: str, hours: int = 1) -> Dict[str, Any]:
        """Get summary statistics for a metric"""
        try:
            if name not in self.metrics:
                return {'error': f'Metric {name} not found'}
            
            # Filter by time window
            cutoff_time = datetime.now() - timedelta(hours=hours)
            recent_data = [
                entry for entry in self.metrics[name]
                if entry['timestamp'] > cutoff_time
            ]
            
            if not recent_data:
                return {'error': f'No recent data for {name}'}
            
            values = [entry['value'] for entry in recent_data]
            
            summary = {
                'count': len(values),
                'min': min(values),
                'max': max(values),
                'mean': sum(values) / len(values),
                'latest': values[-1],
                'time_window_hours': hours
            }
            
            return summary
            
        except Exception as e:
            logging.error(f"Error getting metric summary for {name}: {e}")
            return {'error': str(e)}
    
    def get_system_health(self) -> Dict[str, Any]:
        """Get overall system health metrics"""
        try:
            uptime = datetime.now() - self.start_time
            
            health = {
                'uptime_seconds': uptime.total_seconds(),
                'uptime_formatted': str(uptime),
                'metrics_tracked': len(self.metrics),
                'total_data_points': sum(len(data) for data in self.metrics.values()),
                'status': 'healthy'
            }
            
            return health
            
        except Exception as e:
            logging.error(f"Error getting system health: {e}")
            return {'error': str(e)}

# ============================================================================
# ASYNC UTILITIES
# ============================================================================

class AsyncUtils:
    """Utilities for async operations"""
    
    @staticmethod
    async def run_with_timeout(coro, timeout_seconds: float):
        """Run coroutine with timeout"""
        try:
            return await asyncio.wait_for(coro, timeout=timeout_seconds)
        except asyncio.TimeoutError:
            logging.warning(f"Operation timed out after {timeout_seconds} seconds")
            return None
        except Exception as e:
            logging.error(f"Error in async operation: {e}")
            return None
    
    @staticmethod
    async def batch_process(items: List[Any], batch_size: int, processor_func, max_concurrent: int = 10):
        """Process items in batches with concurrency control"""
        try:
            results = []
            semaphore = asyncio.Semaphore(max_concurrent)
            
            async def process_with_semaphore(item):
                async with semaphore:
                    return await processor_func(item)
            
            # Process in batches
            for i in range(0, len(items), batch_size):
                batch = items[i:i + batch_size]
                batch_tasks = [process_with_semaphore(item) for item in batch]
                batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)
                results.extend(batch_results)
            
            return results
            
        except Exception as e:
            logging.error(f"Error in batch processing: {e}")
            return []

# ============================================================================
# TIME UTILITIES
# ============================================================================

class TimeUtils:
    """Time-related utilities"""
    
    @staticmethod
    def is_market_hours(dt: Optional[datetime] = None) -> bool:
        """Check if given time is during market hours (9:30 AM - 4:00 PM ET)"""
        try:
            if dt is None:
                dt = datetime.now()
            
            # Simple market hours check (ignores holidays)
            if dt.weekday() >= 5:  # Weekend
                return False
            
            market_open = dt.replace(hour=9, minute=30, second=0, microsecond=0)
            market_close = dt.replace(hour=16, minute=0, second=0, microsecond=0)
            
            return market_open <= dt <= market_close
            
        except Exception as e:
            logging.error(f"Error checking market hours: {e}")
            return False
    
    @staticmethod
    def format_duration(seconds: float) -> str:
        """Format duration in human-readable format"""
        try:
            if seconds < 60:
                return f"{seconds:.1f}s"
            elif seconds < 3600:
                return f"{seconds/60:.1f}m"
            else:
                return f"{seconds/3600:.1f}h"
                
        except Exception:
            return "Unknown"
    
    @staticmethod
    def get_timeframe_seconds(timeframe: str) -> int:
        """Get timeframe duration in seconds"""
        timeframe_map = {
            '1Min': 60,
            '5Min': 300,
            '15Min': 900,
            '30Min': 1800,
            '1Hour': 3600,
            '1Day': 86400
        }
        
        return timeframe_map.get(timeframe, 900)  # Default to 15Min

# ============================================================================
# MATH UTILITIES
# ============================================================================

class MathUtils:
    """Mathematical utilities for trading calculations"""
    
    @staticmethod
    def calculate_percentage_change(old_value: float, new_value: float) -> float:
        """Calculate percentage change"""
        try:
            if old_value == 0:
                return 0.0
            return ((new_value - old_value) / old_value) * 100
        except Exception:
            return 0.0
    
    @staticmethod
    def normalize_value(value: float, min_val: float, max_val: float) -> float:
        """Normalize value to 0-1 range"""
        try:
            if max_val == min_val:
                return 0.5
            return (value - min_val) / (max_val - min_val)
        except Exception:
            return 0.5
    
    @staticmethod
    def calculate_risk_reward_ratio(entry_price: float, stop_loss: float, take_profit: float) -> float:
        """Calculate risk-reward ratio"""
        try:
            risk = abs(entry_price - stop_loss)
            reward = abs(take_profit - entry_price)
            
            if risk == 0:
                return 0.0
            
            return reward / risk
            
        except Exception:
            return 0.0
