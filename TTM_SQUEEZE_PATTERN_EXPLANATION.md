# TTM Squeeze Pattern - Complete Logic Explanation

## What is the TTM Squeeze?

The TTM Squeeze is a volatility and momentum indicator created by <PERSON>. It identifies periods when volatility is low (the "squeeze") and predicts explosive moves when the squeeze releases.

## Core Components

### 1. Bollinger Bands (BB)
- **Period**: 20
- **Standard Deviations**: 2.0
- **Formula**: 
  - Middle: 20-period Simple Moving Average
  - Upper: Middle + (2 × 20-period Standard Deviation)
  - Lower: Middle - (2 × 20-period Standard Deviation)

### 2. Keltner Channels (KC)
- **Period**: 20
- **ATR Multiplier**: 1.5
- **Formula**:
  - Middle: 20-period Exponential Moving Average
  - ATR: 20-period Average True Range
  - Upper: Middle + (1.5 × ATR)
  - Lower: Middle - (1.5 × ATR)

### 3. Momentum Oscillator
- **Formula**: Linear Regression of (Close - ((High + Low)/2)) over 20 periods
- **Purpose**: Shows direction and strength of price movement

## The Squeeze Condition

### When is there a "Squeeze"?
```python
squeeze_condition = (bb_lower > kc_lower) AND (bb_upper < kc_upper)
```

**What this means:**
- Bollinger Bands are INSIDE Keltner Channels
- Volatility is contracting
- Price is consolidating
- A breakout is likely coming

### Visual Representation
```
    KC Upper ────────────────────
         BB Upper ─────────
              Price Action
         BB Lower ─────────
    KC Lower ────────────────────
    
    ↑ SQUEEZE ACTIVE ↑
```

## Our Multi-Timeframe Detection Logic

### 1. Signal Strength Calculation (0-100%)

```python
def calculate_signal_strength():
    # Base strength from squeeze condition (30% weight)
    squeeze_strength = is_squeeze * 0.3
    
    # Momentum strength (30% weight)
    momentum_normalized = abs(momentum) / (momentum_std * 2)
    momentum_strength = momentum_normalized * 0.3
    
    # Band compression strength (40% weight)
    compression_ratio = kc_width / bb_width
    compression_strength = (1 - compression_ratio) * 0.4
    
    # Volume confirmation bonus (up to 10%)
    volume_strength = 0.1 if volume > 1.2 * avg_volume else 0
    
    total_strength = squeeze_strength + momentum_strength + compression_strength + volume_strength
    return min(total_strength, 1.0) * 100
```

### 2. Entry Signal Detection

```python
def identify_entry_signals():
    # Squeeze release conditions
    squeeze_release = (
        previous_squeeze == True AND 
        current_squeeze == False AND
        abs(momentum) > momentum_threshold
    )
    
    # Momentum acceleration
    momentum_acceleration = (
        current_momentum > previous_momentum AND
        momentum_color_change == True
    )
    
    # Volume confirmation
    volume_confirmation = current_volume > 1.2 * average_volume
    
    return squeeze_release OR (momentum_acceleration AND volume_confirmation)
```

### 3. Multi-Timeframe Correlation

Our system scans these timeframes simultaneously:
- **Primary**: 15-minute (main signal)
- **Confirmation**: 5-min, 30-min, 1-hour, daily

```python
def multi_timeframe_analysis():
    signals = {}
    
    # Get signals from all timeframes
    for timeframe in ['5Min', '15Min', '30Min', '1Hour', '1Day']:
        signals[timeframe] = get_ttm_squeeze_signal(timeframe)
    
    # Count confirmations
    confirmations = 0
    for signal in signals.values():
        if signal.is_squeeze OR signal.entry_signal OR abs(signal.momentum) > 0.5:
            confirmations += 1
    
    # Require at least 2 timeframes to agree
    valid_setup = confirmations >= 2
    
    # Calculate overall strength
    overall_strength = calculate_multi_timeframe_strength(signals)
    
    return {
        'valid_setup': valid_setup,
        'confirmations': confirmations,
        'overall_strength': overall_strength,
        'timeframes_triggered': get_triggered_timeframes(signals)
    }
```

## Alert Types Generated

### 1. TTM Squeeze Setup (Priority 2)
- **Condition**: Valid multi-timeframe setup detected
- **Requirements**: 
  - At least 2 timeframes showing squeeze or strong momentum
  - Overall strength > 60%
- **Message**: "TTM Squeeze setup detected: X/Y confirmations"

### 2. Strong Entry Signal (Priority 1)
- **Condition**: Primary timeframe entry signal + high strength
- **Requirements**:
  - Primary timeframe entry signal = True
  - Valid setup = True
  - Overall strength > 80%
- **Message**: "Strong entry signal: [recommendation]"

### 3. Squeeze Release (Priority 2)
- **Condition**: Squeeze ending on primary, continuing on others
- **Requirements**:
  - Primary timeframe squeeze = False
  - At least one confirmation timeframe squeeze = True
- **Message**: "Potential squeeze release detected"

## Real Example from Current System

```
ALERT [2]: AAPL - TTM Squeeze setup detected: 2/2 confirmations | Timeframes: 15Min, 5Min, 1Day
```

**This means:**
- AAPL has TTM Squeeze conditions on 15-minute, 5-minute, AND daily charts
- 2 out of 2 required confirmations met
- Priority 2 alert (medium priority)
- Multiple timeframes agree = higher probability setup

## Trading Interpretation

### High Probability Setups
1. **3+ Timeframes Aligned**: Very strong signal
2. **Primary + Daily Agreement**: Trend and short-term aligned
3. **High Signal Strength (>80%)**: Strong compression and momentum

### Entry Strategy
1. Wait for squeeze release (entry signal = True)
2. Enter in direction of momentum
3. Use tight stops (volatility expansion expected)
4. Target 2-3x risk for reward

### Risk Management
- **Stop Loss**: Below/above recent swing low/high
- **Position Size**: Based on volatility (ATR)
- **Time Stop**: Exit if no movement within 5-10 bars

This system gives you a systematic, quantified approach to identifying the TTM Squeeze pattern across multiple timeframes with precise entry and exit signals.
