"""
Automated Trading Decision Engine for TTM Squeeze Trading System
AI-powered autopilot with confidence-based execution and multi-layer decision making
"""

import asyncio
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timed<PERSON>ta
from enum import Enum
import logging
import json

from .advanced_risk_manager import AIRiskManager, RiskLevel
from .news_sentiment_engine import NewsIntelligenceEngine

logger = logging.getLogger(__name__)

class AutoTradingMode(Enum):
    """Automated trading modes"""
    DISABLED = "disabled"
    PAPER_ONLY = "paper_only"
    CONSERVATIVE = "conservative"
    MODERATE = "moderate"
    AGGRESSIVE = "aggressive"

class ExecutionTier(Enum):
    """Execution confidence tiers"""
    HIGH_CONFIDENCE = "high_confidence"
    MEDIUM_CONFIDENCE = "medium_confidence"
    LOW_CONFIDENCE = "low_confidence"
    NO_EXECUTION = "no_execution"

@dataclass
class TradingDecision:
    """Automated trading decision result"""
    symbol: str
    action: str  # 'buy', 'sell', 'hold'
    confidence_score: float
    execution_tier: ExecutionTier
    position_size: float
    entry_price: Optional[float]
    stop_loss: Optional[float]
    take_profit: Optional[float]
    reasoning: List[str]
    risk_assessment: Any  # RiskAssessment object
    sentiment_analysis: Any  # SentimentAnalysis object
    technical_score: float
    fundamental_score: float
    market_context_score: float
    final_score: float
    execute_immediately: bool
    warnings: List[str]
    timestamp: datetime = field(default_factory=datetime.now)

@dataclass
class AutoTradingConfig:
    """Configuration for automated trading"""
    mode: AutoTradingMode = AutoTradingMode.DISABLED
    max_daily_trades: int = 10
    max_position_size: float = 0.05  # 5% of portfolio
    min_confidence_threshold: float = 0.75
    high_confidence_threshold: float = 0.85
    max_portfolio_risk: float = 0.15  # 15% total portfolio risk
    enable_news_filter: bool = True
    enable_market_regime_filter: bool = True
    enable_correlation_filter: bool = True
    trading_hours_only: bool = True
    max_drawdown_threshold: float = 0.10  # 10% max drawdown
    profit_target_multiplier: float = 2.0  # 2:1 risk/reward minimum

class MarketRegimeDetector:
    """Detect current market regime for trading decisions"""
    
    def __init__(self, data_manager):
        self.data_manager = data_manager
        self.regime_cache = {}
        self.cache_expiry = timedelta(minutes=15)
    
    async def detect_market_regime(self) -> Dict[str, Any]:
        """Detect current market regime"""
        cache_key = "market_regime"
        
        # Check cache
        if cache_key in self.regime_cache:
            cached_result, cached_time = self.regime_cache[cache_key]
            if datetime.now() - cached_time < self.cache_expiry:
                return cached_result
        
        try:
            # Get market data
            spy_data = self.data_manager.get_historical_data('SPY', '1Day', 50)
            vix_data = self.data_manager.get_real_time_quote('VIX')
            
            if spy_data is None or len(spy_data) < 20:
                return self._default_regime()
            
            # Calculate regime indicators
            spy_data['returns'] = spy_data['close'].pct_change()
            spy_data['volatility'] = spy_data['returns'].rolling(window=20).std() * np.sqrt(252)
            spy_data['sma_20'] = spy_data['close'].rolling(window=20).mean()
            spy_data['sma_50'] = spy_data['close'].rolling(window=50).mean()
            
            current_price = spy_data['close'].iloc[-1]
            sma_20 = spy_data['sma_20'].iloc[-1]
            sma_50 = spy_data['sma_50'].iloc[-1]
            current_vol = spy_data['volatility'].iloc[-1]
            vix_level = vix_data.get('price', 20) if vix_data else 20
            
            # Determine trend
            if current_price > sma_20 > sma_50:
                trend = "bullish"
                trend_strength = min(1.0, (current_price - sma_50) / sma_50 * 10)
            elif current_price < sma_20 < sma_50:
                trend = "bearish"
                trend_strength = min(1.0, (sma_50 - current_price) / sma_50 * 10)
            else:
                trend = "sideways"
                trend_strength = 0.5
            
            # Determine volatility regime
            if vix_level > 30 or current_vol > 0.25:
                volatility_regime = "high"
            elif vix_level < 15 and current_vol < 0.15:
                volatility_regime = "low"
            else:
                volatility_regime = "normal"
            
            # Calculate regime score (0-100)
            regime_score = 50  # Base score
            
            if trend == "bullish":
                regime_score += 20 * trend_strength
            elif trend == "bearish":
                regime_score -= 20 * trend_strength
            
            if volatility_regime == "low":
                regime_score += 10
            elif volatility_regime == "high":
                regime_score -= 15
            
            # Market breadth (simplified)
            recent_returns = spy_data['returns'].tail(5).mean()
            if recent_returns > 0.01:  # Strong recent performance
                regime_score += 10
            elif recent_returns < -0.01:  # Weak recent performance
                regime_score -= 10
            
            regime_score = max(0, min(100, regime_score))
            
            result = {
                'trend': trend,
                'trend_strength': trend_strength,
                'volatility_regime': volatility_regime,
                'vix_level': vix_level,
                'current_volatility': current_vol,
                'regime_score': regime_score,
                'favorable_for_trading': regime_score > 60,
                'timestamp': datetime.now()
            }
            
            # Cache result
            self.regime_cache[cache_key] = (result, datetime.now())
            
            return result
            
        except Exception as e:
            logger.error(f"Error detecting market regime: {e}")
            return self._default_regime()
    
    def _default_regime(self) -> Dict[str, Any]:
        """Default market regime when detection fails"""
        return {
            'trend': 'sideways',
            'trend_strength': 0.5,
            'volatility_regime': 'normal',
            'vix_level': 20.0,
            'current_volatility': 0.20,
            'regime_score': 50.0,
            'favorable_for_trading': False,
            'timestamp': datetime.now()
        }

class ConfidenceBasedExecutor:
    """Execute trades based on confidence levels"""
    
    def __init__(self, config: AutoTradingConfig):
        self.config = config
        self.execution_tiers = {
            ExecutionTier.HIGH_CONFIDENCE: {
                'threshold': config.high_confidence_threshold,
                'position_size_multiplier': 1.0,
                'execute_immediately': True
            },
            ExecutionTier.MEDIUM_CONFIDENCE: {
                'threshold': config.min_confidence_threshold,
                'position_size_multiplier': 0.7,
                'execute_immediately': True
            },
            ExecutionTier.LOW_CONFIDENCE: {
                'threshold': 0.6,
                'position_size_multiplier': 0.4,
                'execute_immediately': False
            },
            ExecutionTier.NO_EXECUTION: {
                'threshold': 0.0,
                'position_size_multiplier': 0.0,
                'execute_immediately': False
            }
        }
    
    def determine_execution_tier(self, confidence_score: float) -> ExecutionTier:
        """Determine execution tier based on confidence score"""
        for tier in [ExecutionTier.HIGH_CONFIDENCE, ExecutionTier.MEDIUM_CONFIDENCE, ExecutionTier.LOW_CONFIDENCE]:
            if confidence_score >= self.execution_tiers[tier]['threshold']:
                return tier
        
        return ExecutionTier.NO_EXECUTION
    
    def calculate_position_size(self, base_size: float, tier: ExecutionTier, risk_assessment: Any) -> float:
        """Calculate position size based on execution tier and risk"""
        tier_multiplier = self.execution_tiers[tier]['position_size_multiplier']
        
        # Adjust for risk level
        risk_multiplier = 1.0
        if hasattr(risk_assessment, 'risk_level'):
            if risk_assessment.risk_level == RiskLevel.VERY_HIGH:
                risk_multiplier = 0.3
            elif risk_assessment.risk_level == RiskLevel.HIGH:
                risk_multiplier = 0.5
            elif risk_assessment.risk_level == RiskLevel.MODERATE:
                risk_multiplier = 0.8
        
        final_size = base_size * tier_multiplier * risk_multiplier
        return min(final_size, self.config.max_position_size)

class AITradingAutopilot:
    """Main automated trading engine with AI decision making"""
    
    def __init__(self, data_manager, trading_manager, scanner, signal_enhancer):
        self.data_manager = data_manager
        self.trading_manager = trading_manager
        self.scanner = scanner
        self.signal_enhancer = signal_enhancer
        
        # AI components
        self.risk_manager = AIRiskManager(data_manager, trading_manager)
        self.news_engine = NewsIntelligenceEngine()
        self.regime_detector = MarketRegimeDetector(data_manager)
        
        # Configuration
        self.config = AutoTradingConfig()
        self.executor = ConfidenceBasedExecutor(self.config)
        
        # State tracking
        self.daily_trade_count = 0
        self.last_reset_date = datetime.now().date()
        self.active_decisions = {}
        self.performance_metrics = {
            'total_trades': 0,
            'winning_trades': 0,
            'total_pnl': 0.0,
            'max_drawdown': 0.0
        }
    
    async def evaluate_signal_for_auto_trade(self, enhanced_signal: Any) -> TradingDecision:
        """Comprehensive evaluation of a signal for automated trading"""
        try:
            symbol = enhanced_signal.symbol
            
            # Reset daily counter if new day
            if datetime.now().date() > self.last_reset_date:
                self.daily_trade_count = 0
                self.last_reset_date = datetime.now().date()
            
            # Initial checks
            initial_checks = await self._perform_initial_checks(enhanced_signal)
            if not initial_checks['passed']:
                return self._create_no_trade_decision(
                    symbol, initial_checks['reasons']
                )
            
            # Multi-layer analysis
            analysis_results = await self._perform_multi_layer_analysis(enhanced_signal)
            
            # Calculate final confidence score
            final_score = self._calculate_final_confidence(analysis_results)
            
            # Determine execution tier
            execution_tier = self.executor.determine_execution_tier(final_score)
            
            # Risk assessment
            risk_assessment = analysis_results['risk_assessment']
            
            # Position sizing
            base_size = risk_assessment.recommended_position_size
            position_size = self.executor.calculate_position_size(
                base_size, execution_tier, risk_assessment
            )
            
            # Create trading decision
            decision = TradingDecision(
                symbol=symbol,
                action='buy',  # TTM Squeeze is primarily a long strategy
                confidence_score=final_score,
                execution_tier=execution_tier,
                position_size=position_size,
                entry_price=enhanced_signal.current_price,
                stop_loss=risk_assessment.stop_loss_price,
                take_profit=self._calculate_take_profit(
                    enhanced_signal.current_price,
                    risk_assessment.stop_loss_price,
                    self.config.profit_target_multiplier
                ),
                reasoning=self._compile_reasoning(analysis_results),
                risk_assessment=risk_assessment,
                sentiment_analysis=analysis_results.get('sentiment_analysis'),
                technical_score=analysis_results['technical_score'],
                fundamental_score=analysis_results['fundamental_score'],
                market_context_score=analysis_results['market_context_score'],
                final_score=final_score,
                execute_immediately=self._should_execute_immediately(execution_tier, final_score),
                warnings=self._compile_warnings(analysis_results)
            )
            
            # Execute if conditions are met
            if decision.execute_immediately and self.config.mode != AutoTradingMode.DISABLED:
                await self._execute_trade_decision(decision)
            
            return decision
            
        except Exception as e:
            logger.error(f"Error evaluating signal for auto-trade: {e}")
            return self._create_error_decision(enhanced_signal.symbol, str(e))
    
    async def _perform_initial_checks(self, signal: Any) -> Dict[str, Any]:
        """Perform initial checks before detailed analysis"""
        reasons = []
        
        # Check if autopilot is enabled
        if self.config.mode == AutoTradingMode.DISABLED:
            reasons.append("Autopilot is disabled")
        
        # Check daily trade limit
        if self.daily_trade_count >= self.config.max_daily_trades:
            reasons.append(f"Daily trade limit reached ({self.config.max_daily_trades})")
        
        # Check trading hours
        if self.config.trading_hours_only and not self._is_market_hours():
            reasons.append("Outside trading hours")
        
        # Check if symbol already has position
        existing_position = await self._check_existing_position(signal.symbol)
        if existing_position and existing_position['size'] > 0:
            reasons.append("Already have position in this symbol")
        
        # Check portfolio risk limits
        portfolio_risk = await self._calculate_current_portfolio_risk()
        if portfolio_risk > self.config.max_portfolio_risk:
            reasons.append(f"Portfolio risk limit exceeded ({portfolio_risk:.2%})")
        
        return {
            'passed': len(reasons) == 0,
            'reasons': reasons
        }
    
    async def _perform_multi_layer_analysis(self, signal: Any) -> Dict[str, Any]:
        """Perform comprehensive multi-layer analysis"""
        symbol = signal.symbol
        
        # 1. Technical Analysis (from existing signal)
        technical_score = self._evaluate_technical_strength(signal)
        
        # 2. Risk Assessment
        market_context = await self.regime_detector.detect_market_regime()
        risk_assessment = await self.risk_manager.assess_trade_risk(signal, market_context)
        
        # 3. Sentiment Analysis
        sentiment_analysis = None
        if self.config.enable_news_filter:
            try:
                sentiment_analysis = await self.news_engine.analyze_symbol_sentiment(symbol, 24)
            except Exception as e:
                logger.warning(f"Sentiment analysis failed for {symbol}: {e}")
        
        # 4. Market Context Analysis
        market_context_score = self._evaluate_market_context(market_context)
        
        # 5. Fundamental Score (simplified)
        fundamental_score = self._evaluate_fundamental_factors(signal, sentiment_analysis)
        
        return {
            'technical_score': technical_score,
            'risk_assessment': risk_assessment,
            'sentiment_analysis': sentiment_analysis,
            'market_context_score': market_context_score,
            'fundamental_score': fundamental_score,
            'market_context': market_context
        }
    
    def _evaluate_technical_strength(self, signal: Any) -> float:
        """Evaluate technical strength of the signal"""
        score = 50.0  # Base score
        
        # Signal strength
        signal_strength = getattr(signal, 'signal_strength', 0.5)
        score += signal_strength * 30
        
        # Multi-timeframe confirmations
        confirmations = getattr(signal, 'confirmations_count', 1)
        required = getattr(signal, 'required_confirmations', 2)
        confirmation_ratio = confirmations / required
        score += confirmation_ratio * 20
        
        # Trend alignment
        if getattr(signal, 'trend_alignment', False):
            score += 15
        
        # Volume confirmation
        if getattr(signal, 'volume_confirmation', False):
            score += 10
        
        # AI quality score
        ai_score = getattr(signal, 'ai_quality_score', 50)
        score += (ai_score - 50) * 0.3
        
        return min(100.0, max(0.0, score))
    
    def _evaluate_market_context(self, market_context: Dict[str, Any]) -> float:
        """Evaluate market context favorability"""
        regime_score = market_context.get('regime_score', 50)
        
        # Adjust based on volatility regime
        vol_regime = market_context.get('volatility_regime', 'normal')
        if vol_regime == 'high':
            regime_score -= 15  # High volatility reduces favorability
        elif vol_regime == 'low':
            regime_score += 10  # Low volatility increases favorability
        
        return min(100.0, max(0.0, regime_score))
    
    def _evaluate_fundamental_factors(self, signal: Any, sentiment_analysis: Any) -> float:
        """Evaluate fundamental factors"""
        score = 50.0  # Base score
        
        # Sentiment impact
        if sentiment_analysis:
            sentiment_impact = sentiment_analysis.impact_score * 0.3
            if sentiment_analysis.sentiment_label == 'positive':
                score += sentiment_impact
            elif sentiment_analysis.sentiment_label == 'negative':
                score -= sentiment_impact
        
        # News volume (more news can indicate higher interest)
        if sentiment_analysis and sentiment_analysis.news_volume > 5:
            score += 10
        
        return min(100.0, max(0.0, score))
    
    def _calculate_final_confidence(self, analysis: Dict[str, Any]) -> float:
        """Calculate final confidence score using weighted combination"""
        weights = {
            'technical_score': 0.40,
            'market_context_score': 0.25,
            'fundamental_score': 0.20,
            'risk_factor': 0.15
        }
        
        # Risk factor (inverse of risk score)
        risk_score = analysis['risk_assessment'].overall_risk_score
        risk_factor = 100 - risk_score
        
        final_score = (
            analysis['technical_score'] * weights['technical_score'] +
            analysis['market_context_score'] * weights['market_context_score'] +
            analysis['fundamental_score'] * weights['fundamental_score'] +
            risk_factor * weights['risk_factor']
        )
        
        return final_score / 100  # Convert to 0-1 scale
    
    def _should_execute_immediately(self, tier: ExecutionTier, confidence: float) -> bool:
        """Determine if trade should be executed immediately"""
        if self.config.mode == AutoTradingMode.PAPER_ONLY:
            return False
        
        tier_config = self.executor.execution_tiers[tier]
        return tier_config['execute_immediately'] and confidence >= self.config.min_confidence_threshold
    
    async def _execute_trade_decision(self, decision: TradingDecision):
        """Execute the trading decision"""
        try:
            if self.config.mode == AutoTradingMode.PAPER_ONLY:
                logger.info(f"PAPER TRADE: {decision.action} {decision.symbol} @ {decision.position_size:.2%}")
                return
            
            # Place the actual trade
            order_result = await self.trading_manager.place_order(
                symbol=decision.symbol,
                side=decision.action,
                qty=self._calculate_share_quantity(decision),
                type='market',
                time_in_force='day'
            )
            
            if order_result.get('success'):
                self.daily_trade_count += 1
                self.performance_metrics['total_trades'] += 1
                
                logger.info(f"AUTO TRADE EXECUTED: {decision.action} {decision.symbol} "
                          f"@ {decision.position_size:.2%} confidence")
            else:
                logger.error(f"Auto trade execution failed: {order_result.get('error')}")
                
        except Exception as e:
            logger.error(f"Error executing auto trade: {e}")
    
    def _calculate_share_quantity(self, decision: TradingDecision) -> int:
        """Calculate number of shares to trade"""
        try:
            # Get account value
            account_value = 100000  # Default, should get from trading manager
            
            # Calculate dollar amount
            dollar_amount = account_value * decision.position_size
            
            # Calculate shares
            shares = int(dollar_amount / decision.entry_price)
            
            return max(1, shares)
            
        except Exception as e:
            logger.error(f"Error calculating share quantity: {e}")
            return 1
    
    def _calculate_take_profit(self, entry_price: float, stop_loss: float, multiplier: float) -> float:
        """Calculate take profit price"""
        if stop_loss is None:
            return None
        
        risk = entry_price - stop_loss
        reward = risk * multiplier
        return entry_price + reward
    
    def _compile_reasoning(self, analysis: Dict[str, Any]) -> List[str]:
        """Compile reasoning for the trading decision"""
        reasoning = []
        
        reasoning.append(f"Technical Score: {analysis['technical_score']:.1f}/100")
        reasoning.append(f"Market Context: {analysis['market_context_score']:.1f}/100")
        reasoning.append(f"Risk Level: {analysis['risk_assessment'].risk_level.value}")
        
        if analysis.get('sentiment_analysis'):
            sentiment = analysis['sentiment_analysis']
            reasoning.append(f"Sentiment: {sentiment.sentiment_label} ({sentiment.overall_sentiment:.2f})")
        
        return reasoning
    
    def _compile_warnings(self, analysis: Dict[str, Any]) -> List[str]:
        """Compile warnings from analysis"""
        warnings = []
        
        # Risk warnings
        if hasattr(analysis['risk_assessment'], 'warnings'):
            warnings.extend(analysis['risk_assessment'].warnings)
        
        # Market context warnings
        market_context = analysis.get('market_context', {})
        if market_context.get('volatility_regime') == 'high':
            warnings.append("High market volatility detected")
        
        # Sentiment warnings
        sentiment = analysis.get('sentiment_analysis')
        if sentiment and sentiment.sentiment_label == 'negative':
            warnings.append("Negative news sentiment detected")
        
        return warnings
    
    def _create_no_trade_decision(self, symbol: str, reasons: List[str]) -> TradingDecision:
        """Create a no-trade decision"""
        return TradingDecision(
            symbol=symbol,
            action='hold',
            confidence_score=0.0,
            execution_tier=ExecutionTier.NO_EXECUTION,
            position_size=0.0,
            entry_price=None,
            stop_loss=None,
            take_profit=None,
            reasoning=reasons,
            risk_assessment=None,
            sentiment_analysis=None,
            technical_score=0.0,
            fundamental_score=0.0,
            market_context_score=0.0,
            final_score=0.0,
            execute_immediately=False,
            warnings=reasons
        )
    
    def _create_error_decision(self, symbol: str, error: str) -> TradingDecision:
        """Create an error decision"""
        return TradingDecision(
            symbol=symbol,
            action='hold',
            confidence_score=0.0,
            execution_tier=ExecutionTier.NO_EXECUTION,
            position_size=0.0,
            entry_price=None,
            stop_loss=None,
            take_profit=None,
            reasoning=[f"Analysis error: {error}"],
            risk_assessment=None,
            sentiment_analysis=None,
            technical_score=0.0,
            fundamental_score=0.0,
            market_context_score=0.0,
            final_score=0.0,
            execute_immediately=False,
            warnings=[f"Error in analysis: {error}"]
        )
    
    # Helper methods
    def _is_market_hours(self) -> bool:
        """Check if market is currently open"""
        now = datetime.now()
        if now.weekday() >= 5:  # Weekend
            return False
        
        market_open = now.replace(hour=9, minute=30, second=0, microsecond=0)
        market_close = now.replace(hour=16, minute=0, second=0, microsecond=0)
        
        return market_open <= now <= market_close
    
    async def _check_existing_position(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Check if there's an existing position in the symbol"""
        try:
            positions = await self.trading_manager.get_positions()
            for pos in positions:
                if pos.get('symbol') == symbol:
                    return {
                        'size': abs(float(pos.get('qty', 0))),
                        'value': float(pos.get('market_value', 0))
                    }
            return None
        except Exception as e:
            logger.error(f"Error checking existing position: {e}")
            return None
    
    async def _calculate_current_portfolio_risk(self) -> float:
        """Calculate current portfolio risk exposure"""
        try:
            # This would calculate total portfolio risk
            # For now, return a conservative estimate
            return 0.05  # 5% default
        except Exception as e:
            logger.error(f"Error calculating portfolio risk: {e}")
            return 0.10  # Conservative default
    
    # Configuration methods
    def update_config(self, new_config: Dict[str, Any]):
        """Update autopilot configuration"""
        for key, value in new_config.items():
            if hasattr(self.config, key):
                setattr(self.config, key, value)
        
        # Update executor with new config
        self.executor = ConfidenceBasedExecutor(self.config)
        
        logger.info(f"Autopilot configuration updated: {new_config}")
    
    def get_status(self) -> Dict[str, Any]:
        """Get current autopilot status"""
        return {
            'mode': self.config.mode.value,
            'daily_trades': self.daily_trade_count,
            'max_daily_trades': self.config.max_daily_trades,
            'performance': self.performance_metrics,
            'last_reset': self.last_reset_date.isoformat(),
            'active_decisions': len(self.active_decisions)
        }
