"""
ChatGPT Trading Assistant for TTM Squeeze Trading System
Natural language interface for trading commands and market analysis
"""

import asyncio
import json
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from dataclasses import dataclass
import re

# OpenAI imports
try:
    import openai
    from openai import AsyncOpenAI
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

from config import Config

logger = logging.getLogger(__name__)

@dataclass
class TradingCommand:
    """Parsed trading command from natural language"""
    action: str  # 'buy', 'sell', 'analyze', 'status', 'alerts'
    symbol: Optional[str] = None
    quantity: Optional[float] = None
    price: Optional[float] = None
    order_type: str = 'market'
    timeframe: Optional[str] = None
    confidence: float = 0.0
    reasoning: str = ""

@dataclass
class MarketAnalysis:
    """AI-generated market analysis"""
    summary: str
    key_points: List[str]
    recommendations: List[str]
    risk_assessment: str
    confidence_level: str

class TradingContextManager:
    """Manages trading context for ChatGPT conversations"""
    
    def __init__(self, data_manager, trading_manager, scanner):
        self.data_manager = data_manager
        self.trading_manager = trading_manager
        self.scanner = scanner
        
    async def get_portfolio_summary(self) -> Dict[str, Any]:
        """Get current portfolio summary"""
        try:
            account = await self.trading_manager.get_account()
            positions = await self.trading_manager.get_positions()
            
            return {
                'buying_power': float(account.get('buying_power', 0)),
                'portfolio_value': float(account.get('portfolio_value', 0)),
                'day_trade_count': account.get('day_trade_count', 0),
                'positions_count': len(positions),
                'positions': [
                    {
                        'symbol': pos.get('symbol'),
                        'qty': float(pos.get('qty', 0)),
                        'market_value': float(pos.get('market_value', 0)),
                        'unrealized_pl': float(pos.get('unrealized_pl', 0))
                    }
                    for pos in positions[:5]  # Top 5 positions
                ]
            }
        except Exception as e:
            logger.error(f"Error getting portfolio summary: {e}")
            return {'error': str(e)}
    
    async def get_recent_alerts(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent TTM Squeeze alerts"""
        try:
            alerts = await self.scanner.get_recent_alerts(limit)
            return [
                {
                    'symbol': alert.get('symbol'),
                    'timeframes': alert.get('timeframes', []),
                    'signal_strength': alert.get('signal_strength', 0),
                    'ai_score': alert.get('ai_score', 0),
                    'timestamp': alert.get('timestamp')
                }
                for alert in alerts
            ]
        except Exception as e:
            logger.error(f"Error getting recent alerts: {e}")
            return []
    
    async def get_market_status(self) -> Dict[str, Any]:
        """Get current market status"""
        try:
            # Get major indices
            spy_data = self.data_manager.get_real_time_quote('SPY')
            vix_data = self.data_manager.get_real_time_quote('VIX')
            
            return {
                'market_open': self._is_market_open(),
                'spy_price': spy_data.get('price', 0) if spy_data else 0,
                'spy_change': spy_data.get('change_percent', 0) if spy_data else 0,
                'vix_level': vix_data.get('price', 0) if vix_data else 0,
                'market_sentiment': self._assess_market_sentiment(spy_data, vix_data)
            }
        except Exception as e:
            logger.error(f"Error getting market status: {e}")
            return {'error': str(e)}
    
    def _is_market_open(self) -> bool:
        """Check if market is currently open"""
        now = datetime.now()
        # Simplified market hours check (9:30 AM - 4:00 PM ET, weekdays)
        if now.weekday() >= 5:  # Weekend
            return False
        
        market_open = now.replace(hour=9, minute=30, second=0, microsecond=0)
        market_close = now.replace(hour=16, minute=0, second=0, microsecond=0)
        
        return market_open <= now <= market_close
    
    def _assess_market_sentiment(self, spy_data, vix_data) -> str:
        """Assess overall market sentiment"""
        if not spy_data or not vix_data:
            return "neutral"
        
        spy_change = spy_data.get('change_percent', 0)
        vix_level = vix_data.get('price', 20)
        
        if spy_change > 1 and vix_level < 20:
            return "bullish"
        elif spy_change < -1 and vix_level > 25:
            return "bearish"
        else:
            return "neutral"

class ChatGPTTradingAssistant:
    """Main ChatGPT trading assistant class"""
    
    def __init__(self, context_manager: TradingContextManager):
        self.context_manager = context_manager
        self.client = None
        self.conversation_history = []
        
        if OPENAI_AVAILABLE and hasattr(Config, 'OPENAI_API_KEY'):
            self.client = AsyncOpenAI(api_key=Config.OPENAI_API_KEY)
            logger.info("ChatGPT assistant initialized")
        else:
            logger.warning("OpenAI not available or API key not configured")
    
    async def process_trading_message(self, user_message: str) -> Dict[str, Any]:
        """Process natural language trading message"""
        if not self.client:
            return {
                'response': "ChatGPT assistant not available. Please configure OpenAI API key.",
                'type': 'error'
            }
        
        try:
            # Get current context
            portfolio = await self.context_manager.get_portfolio_summary()
            alerts = await self.context_manager.get_recent_alerts(5)
            market_status = await self.context_manager.get_market_status()
            
            # Build context prompt
            context_prompt = self._build_context_prompt(portfolio, alerts, market_status)
            
            # Create conversation with ChatGPT
            messages = [
                {
                    "role": "system",
                    "content": self._get_system_prompt()
                },
                {
                    "role": "user",
                    "content": f"{context_prompt}\n\nUser Request: {user_message}"
                }
            ]
            
            # Add conversation history (last 5 exchanges)
            messages.extend(self.conversation_history[-10:])
            
            # Get ChatGPT response
            response = await self.client.chat.completions.create(
                model="gpt-4",
                messages=messages,
                max_tokens=1000,
                temperature=0.7
            )
            
            ai_response = response.choices[0].message.content
            
            # Parse response for actionable commands
            parsed_command = self._parse_trading_command(ai_response)
            
            # Update conversation history
            self.conversation_history.extend([
                {"role": "user", "content": user_message},
                {"role": "assistant", "content": ai_response}
            ])
            
            return {
                'response': ai_response,
                'type': 'success',
                'command': parsed_command,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error processing ChatGPT message: {e}")
            return {
                'response': f"Error processing request: {str(e)}",
                'type': 'error'
            }
    
    def _get_system_prompt(self) -> str:
        """Get system prompt for ChatGPT"""
        return """You are an expert trading assistant for a TTM Squeeze trading system. 

Your capabilities:
- Analyze TTM Squeeze signals and market conditions
- Provide trading recommendations based on technical analysis
- Explain market movements and trading opportunities
- Help with portfolio management and risk assessment
- Answer questions about trading strategies and market trends

Guidelines:
- Always consider risk management in your recommendations
- Explain your reasoning clearly
- Use the provided market context and portfolio information
- Be concise but informative
- If suggesting trades, specify entry points, stop losses, and targets
- Acknowledge when you need more information

Current system capabilities:
- Real-time TTM Squeeze scanning across multiple timeframes
- AI-enhanced signal quality scoring
- Multi-timeframe confirmation analysis
- Risk management and position sizing
- Real-time market data and alerts

Respond in a professional, helpful manner suitable for an experienced trader."""
    
    def _build_context_prompt(self, portfolio, alerts, market_status) -> str:
        """Build context prompt with current trading information"""
        context = f"""
CURRENT TRADING CONTEXT:

Portfolio Status:
- Buying Power: ${portfolio.get('buying_power', 0):,.2f}
- Portfolio Value: ${portfolio.get('portfolio_value', 0):,.2f}
- Active Positions: {portfolio.get('positions_count', 0)}

Recent TTM Squeeze Alerts (Top 5):
"""
        
        for alert in alerts[:5]:
            context += f"- {alert.get('symbol')}: {', '.join(alert.get('timeframes', []))} (Strength: {alert.get('signal_strength', 0):.2f}, AI Score: {alert.get('ai_score', 0):.2f})\n"
        
        context += f"""
Market Status:
- Market Open: {market_status.get('market_open', False)}
- SPY: ${market_status.get('spy_price', 0):.2f} ({market_status.get('spy_change', 0):+.2f}%)
- VIX: {market_status.get('vix_level', 0):.2f}
- Sentiment: {market_status.get('market_sentiment', 'neutral').title()}
"""
        
        return context
    
    def _parse_trading_command(self, ai_response: str) -> Optional[TradingCommand]:
        """Parse AI response for actionable trading commands"""
        # Simple regex patterns to extract trading commands
        patterns = {
            'buy': r'(?:buy|purchase|long)\s+(\w+)(?:\s+(\d+(?:\.\d+)?)\s*shares?)?(?:\s+at\s+\$?(\d+(?:\.\d+)?))?',
            'sell': r'(?:sell|short)\s+(\w+)(?:\s+(\d+(?:\.\d+)?)\s*shares?)?(?:\s+at\s+\$?(\d+(?:\.\d+)?))?',
            'analyze': r'(?:analyze|analysis|check)\s+(\w+)',
        }
        
        for action, pattern in patterns.items():
            match = re.search(pattern, ai_response.lower())
            if match:
                groups = match.groups()
                return TradingCommand(
                    action=action,
                    symbol=groups[0].upper() if groups[0] else None,
                    quantity=float(groups[1]) if len(groups) > 1 and groups[1] else None,
                    price=float(groups[2]) if len(groups) > 2 and groups[2] else None,
                    reasoning=ai_response
                )
        
        return None
    
    async def generate_market_commentary(self) -> MarketAnalysis:
        """Generate daily market commentary"""
        if not self.client:
            return MarketAnalysis(
                summary="ChatGPT not available",
                key_points=[],
                recommendations=[],
                risk_assessment="Unable to assess",
                confidence_level="Low"
            )
        
        try:
            # Get comprehensive market data
            portfolio = await self.context_manager.get_portfolio_summary()
            alerts = await self.context_manager.get_recent_alerts(20)
            market_status = await self.context_manager.get_market_status()
            
            prompt = f"""
Generate a professional market briefing based on current TTM Squeeze signals and market conditions:

TTM Squeeze Signals Today: {len(alerts)} alerts generated
Top Signals: {', '.join([alert.get('symbol', '') for alert in alerts[:10]])}

Market Conditions:
- SPY: ${market_status.get('spy_price', 0):.2f} ({market_status.get('spy_change', 0):+.2f}%)
- VIX: {market_status.get('vix_level', 0):.2f}
- Sentiment: {market_status.get('market_sentiment', 'neutral')}

Provide:
1. Market summary (2-3 sentences)
2. Key points (3-5 bullet points)
3. Trading recommendations for TTM Squeeze strategy
4. Risk assessment
5. Confidence level (High/Medium/Low)

Format as JSON with keys: summary, key_points, recommendations, risk_assessment, confidence_level
"""
            
            response = await self.client.chat.completions.create(
                model="gpt-4",
                messages=[{"role": "user", "content": prompt}],
                max_tokens=800,
                temperature=0.5
            )
            
            # Parse JSON response
            try:
                analysis_data = json.loads(response.choices[0].message.content)
                return MarketAnalysis(**analysis_data)
            except json.JSONDecodeError:
                # Fallback to text parsing
                content = response.choices[0].message.content
                return MarketAnalysis(
                    summary=content[:200] + "...",
                    key_points=[content],
                    recommendations=["Review TTM Squeeze signals"],
                    risk_assessment="Moderate",
                    confidence_level="Medium"
                )
                
        except Exception as e:
            logger.error(f"Error generating market commentary: {e}")
            return MarketAnalysis(
                summary=f"Error generating commentary: {str(e)}",
                key_points=[],
                recommendations=[],
                risk_assessment="Unable to assess",
                confidence_level="Low"
            )
