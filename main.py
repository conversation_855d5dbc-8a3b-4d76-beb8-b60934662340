#!/usr/bin/env python3
"""
TTM Squeeze Trading System - Optimized Main Entry Point
Streamlined professional trading system focused on 5Min and 15Min intraday patterns
"""

import asyncio
import logging
import sys
import os
from datetime import datetime

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Import configuration
from config import Config

# Import consolidated core modules
from src.core_data import DataManager
from src.core_trading import AlpacaTradingManager, PortfolioManager, RiskManager
from src.core_scanner import RealTimeScanner, StockScreener, AlertManager
from src.core_indicators import MultiTimeframeAnalyzer, SignalFilter
from src.ai_intelligence import (
    ChatGPTTradingAssistant, TradingContextManager, AIRiskManager,
    AITradingAutopilot, AISignalEnhancer, DashboardIntelligence
)
from src.web_interface import create_app
from src.utils.logger import setup_logging
from src.utils import ConfigManager, PerformanceMonitor

logger = logging.getLogger(__name__)

class OptimizedTTMSqueezeSystem:
    """Optimized TTM Squeeze Trading System - Focused on Intraday Patterns"""

    def __init__(self):
        # Core components
        self.data_manager = None
        self.trading_manager = None
        self.portfolio_manager = None
        self.risk_manager = None

        # Scanner components
        self.scanner = None
        self.screener = None
        self.alert_manager = None

        # AI components
        self.ai_risk_manager = None
        self.ai_enhancer = None
        self.chat_assistant = None
        self.auto_trader = None
        self.dashboard_ai = None

        # System components
        self.web_app = None
        self.config_manager = None
        self.performance_monitor = None
        self.running = False

        logger.info("Optimized TTM Squeeze Trading System initialized")
        logger.info("Focus: 5Min and 15Min intraday patterns")

    async def initialize(self):
        """Initialize all system components with optimized configuration"""
        try:
            logger.info("Initializing Optimized TTM Squeeze Trading System...")

            # Initialize configuration manager
            self.config_manager = ConfigManager()
            self.performance_monitor = PerformanceMonitor()

            # Initialize data manager with optimized settings
            self.data_manager = DataManager(
                alpaca_config={
                    'api_key': Config.ALPACA_API_KEY,
                    'secret_key': Config.ALPACA_SECRET_KEY,
                    'base_url': Config.ALPACA_BASE_URL
                },
                fmp_config={
                    'api_key': Config.FMP_API_KEY
                }
            )

            data_connected = await self.data_manager.initialize()
            if not data_connected:
                logger.error("Failed to initialize data manager")
                return False

            logger.info("✅ Data manager initialized")

            # Initialize trading components
            self.trading_manager = AlpacaTradingManager(
                api_key=Config.ALPACA_API_KEY,
                secret_key=Config.ALPACA_SECRET_KEY,
                base_url=Config.ALPACA_BASE_URL
            )

            trading_connected = await self.trading_manager.connect()
            if trading_connected:
                self.portfolio_manager = PortfolioManager(self.trading_manager)
                self.risk_manager = RiskManager(self.trading_manager)
                logger.info("✅ Trading components initialized")
            else:
                logger.warning("⚠️ Trading manager not connected - continuing without trading")

            # Initialize AI components
            self.ai_enhancer = AISignalEnhancer()
            self.ai_risk_manager = AIRiskManager(self.data_manager, self.trading_manager)
            self.dashboard_ai = DashboardIntelligence()

            # Initialize ChatGPT assistant if available
            openai_key = os.getenv('OPENAI_API_KEY')
            if openai_key and self.trading_manager:
                context_manager = TradingContextManager(
                    self.data_manager, self.trading_manager, None  # Scanner will be set later
                )
                self.chat_assistant = ChatGPTTradingAssistant(context_manager)
                logger.info("✅ AI ChatGPT assistant initialized")
            else:
                logger.warning("⚠️ ChatGPT assistant disabled (missing API key or trading)")

            # Initialize automated trading
            if self.trading_manager:
                self.auto_trader = AITradingAutopilot(
                    self.data_manager, self.trading_manager, None  # Scanner will be set later
                )
                logger.info("✅ AI automated trading initialized")

            # Initialize scanner components
            self.alert_manager = AlertManager()
            self.screener = StockScreener(self.data_manager)

            # Initialize real-time scanner with optimized settings
            self.scanner = RealTimeScanner(self.data_manager)

            # Update context managers with scanner reference
            if self.chat_assistant:
                self.chat_assistant.context_manager.scanner = self.scanner
            if self.auto_trader:
                self.auto_trader.scanner = self.scanner

            logger.info("✅ Scanner components initialized")

            # Initialize web application
            self.web_app = create_app(
                data_manager=self.data_manager,
                trading_manager=self.trading_manager,
                scanner=self.scanner,
                ai_intelligence={
                    'chat_assistant': self.chat_assistant,
                    'risk_manager': self.ai_risk_manager,
                    'enhancer': self.ai_enhancer,
                    'auto_trader': self.auto_trader,
                    'dashboard_ai': self.dashboard_ai
                }
            )

            logger.info("✅ Web interface initialized")
            logger.info("🚀 All components initialized successfully")
            logger.info("📊 System optimized for 5Min and 15Min timeframes")

            return True

        except Exception as e:
            logger.error(f"❌ Error initializing system: {e}")
            return False

    async def start(self):
        """Start the optimized trading system"""
        try:
            if not await self.initialize():
                logger.error("❌ Failed to initialize system")
                return False

            self.running = True

            logger.info("🚀 Starting Optimized TTM Squeeze Trading System...")
            logger.info("⏱️ Timeframes: 5Min and 15Min only")
            logger.info("🎯 Focus: Intraday pattern detection")
            logger.info("📈 Scan interval: 30 seconds")

            # Start performance monitoring
            self.performance_monitor.record_metric('system_start', 1)

            # Start scanner in background
            logger.info("🔍 Starting real-time scanner...")
            scanner_task = asyncio.create_task(self.scanner.start_scanning())

            # Start web server in separate thread
            logger.info("🌐 Starting web server...")
            logger.info("📊 Professional dashboard: http://127.0.0.1:5000/pro")
            logger.info("🤖 AI Chat interface included in dashboard")
            logger.info("⚡ System optimized for high-performance intraday trading")

            import threading
            flask_thread = threading.Thread(
                target=lambda: self.web_app.run(
                    host='127.0.0.1',
                    port=5000,
                    debug=False,
                    use_reloader=False,
                    threaded=True
                )
            )
            flask_thread.daemon = True
            flask_thread.start()

            # Wait for scanner task
            await scanner_task

        except KeyboardInterrupt:
            logger.info("🛑 Shutdown requested by user")
        except Exception as e:
            logger.error(f"❌ Error starting system: {e}")
        finally:
            await self.shutdown()

    async def shutdown(self):
        """Shutdown the optimized trading system"""
        try:
            logger.info("🛑 Shutting down Optimized TTM Squeeze Trading System...")
            self.running = False

            # Record shutdown metric
            if self.performance_monitor:
                self.performance_monitor.record_metric('system_shutdown', 1)

            # Stop scanner
            if self.scanner:
                self.scanner.stop_scanning()
                logger.info("✅ Scanner stopped")

            # Close connections
            if self.data_manager:
                await self.data_manager.close()
                logger.info("✅ Data manager closed")

            if self.trading_manager:
                await self.trading_manager.close()
                logger.info("✅ Trading manager closed")

            logger.info("✅ System shutdown complete")

        except Exception as e:
            logger.error(f"❌ Error during shutdown: {e}")

def main():
    """Main entry point for optimized system"""
    try:
        # Setup logging
        setup_logging()

        logger.info("=" * 80)
        logger.info("🚀 OPTIMIZED TTM SQUEEZE TRADING SYSTEM STARTING")
        logger.info("=" * 80)
        logger.info(f"⏰ Start time: {datetime.now()}")
        logger.info(f"🐍 Python version: {sys.version}")
        logger.info(f"📝 Log level: {Config.LOG_LEVEL}")
        logger.info(f"⚡ Optimization: Streamlined for intraday patterns")
        logger.info(f"📊 Timeframes: 5Min and 15Min only")
        logger.info(f"📁 File structure: Consolidated (11 core files)")
        logger.info("=" * 80)

        # Create and run optimized system
        system = OptimizedTTMSqueezeSystem()

        # Run the async main function
        asyncio.run(system.start())

    except KeyboardInterrupt:
        logger.info("🛑 System interrupted by user")
    except Exception as e:
        logger.error(f"💥 Fatal error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
