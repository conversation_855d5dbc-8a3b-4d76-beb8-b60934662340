"""
TTM Squeeze Trading System - Main Application Entry Point
"""
import logging
import sys
from pathlib import Path

# Add src directory to Python path
sys.path.append(str(Path(__file__).parent / 'src'))

from config import Config
from src.ui.app import create_app
from src.scanner.real_time_scanner import RealTimeScanner
from src.utils.logger import setup_logging

def main():
    """Main application entry point"""

    # Setup logging
    setup_logging()
    logger = logging.getLogger(__name__)

    logger.info("Starting TTM Squeeze Trading System")

    scanner = None
    try:
        # Create Flask app
        app = create_app()

        # Initialize real-time scanner
        scanner = RealTimeScanner()

        # Start the scanner automatically
        logger.info("Starting real-time scanner...")
        scanner.start_scanning()

        # Start the application
        logger.info(f"Starting web interface on {Config.FLASK_HOST}:{Config.FLASK_PORT}")
        app.run(
            host=Config.FLASK_HOST,
            port=Config.FLASK_PORT,
            debug=Config.FLASK_DEBUG
        )

    except KeyboardInterrupt:
        logger.info("Application stopped by user")
        if scanner:
            logger.info("Stopping scanner...")
            scanner.stop_scanning()
    except Exception as e:
        logger.error(f"Application error: {e}")
        if scanner:
            logger.info("Stopping scanner due to error...")
            scanner.stop_scanning()
        raise

if __name__ == "__main__":
    main()
