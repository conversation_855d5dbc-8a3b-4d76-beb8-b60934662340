# TTM Squeeze Trading System

A comprehensive trading system implementing the TTM Squeeze indicator with multi-timeframe analysis, real-time scanning, and automated trading capabilities.

## 🚀 Quick Start

### 1. Installation
```bash
# Clone or download the system
cd TTMFINAL

# Install dependencies
pip install -r requirements.txt
```

### 2. Configuration
Your API keys are already configured in `.env`:
- ✅ Alpaca API (Paper Trading)
- ✅ Financial Modeling Prep API

### 3. Run the System
```bash
# Start the web interface
python main.py
```

Then open: http://127.0.0.1:5000

## 📊 System Status

**✅ SYSTEM READY**
- All tests passed (6/6)
- API connections established
- Account active with $59,790.98 buying power
- Web interface running

## 🎯 Features

### Core TTM Squeeze Algorithm
- **Bollinger Bands**: 20-period MA, 2.0 standard deviation
- **Keltner Channels**: 20-period MA, 1.5 ATR multiplier
- **Momentum Histogram**: Linear regression slope with color coding
- **Squeeze Detection**: BB inside KC detection

### Multi-Timeframe Analysis
- **Primary**: 15-minute charts
- **Confirmation**: 5-minute and 30-minute
- **Entry Requirement**: 2 out of 3 timeframes must show squeeze conditions

### Additional Filters
- ✅ EMA8 > EMA21 (bullish trend alignment)
- ✅ Volume above 80% of 20-period average
- ✅ MACD histogram improvement
- ✅ Price action confirmation

### Risk Management
- **Position Sizing**: Dynamic based on account value and stop loss
- **Maximum Risk**: 2% per trade, 6% total portfolio
- **Stop Loss**: 2% default
- **Take Profit**: 4% default (2:1 R/R)
- **Max Positions**: 10 concurrent

### Stock Universe
- ✅ S&P 500 stocks
- ✅ Stocks with market cap ≥ $100 billion
- ✅ Real-time filtering and screening

## 🖥️ Web Interface

### Dashboard
- Real-time scanner status
- Active alerts count
- Portfolio value and positions
- Market status

### Scanner Tab
- Start/stop real-time scanning
- View live alerts
- Configure scan settings
- Scanner performance statistics

### Analysis Tab
- Analyze specific symbols
- Run stock screener
- View multi-timeframe results
- Signal strength indicators

### Trading Tab
- Place manual orders
- View current positions
- Monitor order status
- Account information

### Risk Management Tab
- Portfolio risk assessment
- Position risk analysis
- Risk alerts and recommendations

## 🔧 Usage Examples

### 1. Analyze a Symbol
```python
# In the web interface Analysis tab:
# Enter symbol: AAPL
# Click "Analyze"
```

### 2. Run Stock Screener
```python
# Configure criteria:
# Min Price: $10
# Max Price: $1000
# Min Volume: 1,000,000
# EMA Alignment: ✅
# Click "Run Screener"
```

### 3. Start Real-time Scanner
```python
# In Scanner tab:
# Click "Start Scanner"
# Monitor alerts in real-time
```

### 4. Place a Trade
```python
# In Trading tab:
# Symbol: AAPL
# Side: Buy
# Quantity: 10
# Order Type: Market
# Click "Place Order"
```

## 📈 Signal Interpretation

### TTM Squeeze Colors
- 🔴 **Red**: Decreasing momentum (bearish)
- 🟡 **Yellow**: Transitional momentum
- 🟢 **Green**: Increasing momentum (bullish)

### Entry Signals
- **Strong Buy**: Entry signal + valid setup + 80%+ strength
- **Buy**: Entry signal + valid setup + 60%+ strength
- **Hold**: Moderate signals
- **Avoid**: Weak or no signals

### Signal Strength
- **High (70-100%)**: Strong momentum, multiple confirmations
- **Medium (40-69%)**: Moderate signals
- **Low (0-39%)**: Weak signals

## ⚙️ Configuration

### TTM Squeeze Parameters (config.py)
```python
TTM_SQUEEZE_CONFIG = {
    'bb_period': 20,           # Bollinger Bands period
    'bb_std_dev': 2.0,         # BB standard deviation
    'kc_period': 20,           # Keltner Channels period
    'kc_atr_multiplier': 1.5,  # KC ATR multiplier
    'momentum_period': 20,      # Momentum calculation period
    'ema_fast': 8,             # Fast EMA
    'ema_slow': 21,            # Slow EMA
}
```

### Risk Management
```python
RISK_MANAGEMENT = {
    'max_position_size': 0.02,    # 2% per position
    'max_portfolio_risk': 0.06,   # 6% total risk
    'stop_loss_pct': 0.02,        # 2% stop loss
    'take_profit_pct': 0.04,      # 4% take profit
    'max_positions': 10,          # Max concurrent positions
}
```

## 🔍 Testing

Run the test suite:
```bash
python test_system.py
```

Expected output:
```
🎯 Test Results: 6/6 tests passed
🎉 All tests passed! System is ready to use.
```

## 📊 Performance Monitoring

### Scanner Statistics
- Total scans performed
- Universe size
- Alerts generated
- Average scan duration

### Trading Metrics
- Win rate
- Average R/R ratio
- Maximum drawdown
- Sharpe ratio

## 🚨 Important Notes

### Paper Trading
- System is configured for **paper trading** by default
- No real money at risk
- Perfect for testing and learning

### Market Hours
- Scanner operates during market hours by default
- Can be configured for extended hours
- Respects market holidays

### API Limits
- Alpaca: 200 requests/minute
- FMP: 250 requests/day (free tier)
- System includes rate limiting

## 🛠️ Troubleshooting

### Common Issues

1. **Import Errors**
   ```bash
   pip install -r requirements.txt
   ```

2. **API Connection Issues**
   - Check API keys in `.env`
   - Verify internet connection
   - Check API status pages

3. **Web Interface Not Loading**
   - Ensure port 5000 is available
   - Check firewall settings
   - Try different browser

### Support
- Check logs in `logs/ttm_squeeze.log`
- Run `python test_system.py` for diagnostics
- Review error messages in terminal

## 📚 Next Steps

1. **Familiarize with Interface**: Explore all tabs and features
2. **Test Analysis**: Analyze known stocks (AAPL, MSFT, etc.)
3. **Run Screener**: Find potential setups
4. **Monitor Scanner**: Watch for real-time alerts
5. **Paper Trade**: Practice with paper trading
6. **Optimize Settings**: Adjust parameters based on results

## 🎯 Trading Strategy

### Entry Criteria
1. TTM Squeeze detected (2/3 timeframes)
2. First yellow/green bar after red sequence
3. EMA8 > EMA21 (bullish trend)
4. Volume > 80% of average
5. MACD histogram improving

### Exit Criteria
1. Take profit at 2:1 R/R ratio
2. Stop loss at 2% below entry
3. Time-based exit (optional)
4. Trend reversal signals

### Position Management
1. Risk 2% per trade maximum
2. Maximum 10 concurrent positions
3. Portfolio risk limit 6%
4. Dynamic position sizing

---

**🎉 Congratulations! Your TTM Squeeze Trading System is ready to use!**

Start by exploring the web interface and analyzing some symbols to get familiar with the system.
