"""
Multi-Timeframe Analysis for TTM Squeeze Trading System
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
import logging
from dataclasses import dataclass
from datetime import datetime, timedelta

from .ttm_squeeze import TTMSqueeze, SqueezeSignal
from .technical_indicators import TechnicalIndicators
from config import Config

logger = logging.getLogger(__name__)

@dataclass
class MultiTimeframeSignal:
    """Multi-timeframe analysis result"""
    symbol: str
    timestamp: datetime
    primary_signal: SqueezeSignal
    confirmation_signals: Dict[str, SqueezeSignal]
    confirmations_count: int
    required_confirmations: int
    is_valid_setup: bool
    overall_strength: float
    trend_alignment: bool
    volume_confirmation: bool
    entry_recommendation: str  # 'strong_buy', 'buy', 'hold', 'avoid'

class MultiTimeframeAnalyzer:
    """Multi-timeframe TTM Squeeze analysis"""
    
    def __init__(self, config: Dict = None):
        """Initialize multi-timeframe analyzer"""
        self.config = config or Config.TIMEFRAMES
        self.primary_timeframe = self.config['primary']
        self.confirmation_timeframes = self.config['confirmation']
        self.required_confirmations = self.config['required_confirmations']
        
        # Initialize TTM Squeeze calculators for each timeframe
        self.squeeze_calculators = {
            timeframe: TTMSqueeze() for timeframe in [self.primary_timeframe] + self.confirmation_timeframes
        }
        
        # Additional filters configuration
        self.filter_config = Config.TTM_SQUEEZE_CONFIG
    
    def analyze_symbol(self, symbol: str, data_dict: Dict[str, pd.DataFrame]) -> Optional[MultiTimeframeSignal]:
        """
        Analyze a symbol across multiple timeframes
        
        Args:
            symbol: Stock symbol
            data_dict: Dictionary with timeframe as key and OHLCV DataFrame as value
            
        Returns:
            MultiTimeframeSignal or None if insufficient data
        """
        try:
            # Get primary timeframe signal
            primary_data = data_dict.get(self.primary_timeframe)
            if primary_data is None or len(primary_data) < 50:
                logger.warning(f"Insufficient primary timeframe data for {symbol}")
                return None
            
            primary_signal = self.squeeze_calculators[self.primary_timeframe].get_current_signal(
                primary_data, symbol, self.primary_timeframe
            )
            
            if primary_signal is None:
                return None
            
            # Get confirmation signals
            confirmation_signals = {}
            valid_confirmations = 0
            
            for timeframe in self.confirmation_timeframes:
                data = data_dict.get(timeframe)
                if data is not None and len(data) >= 50:
                    signal = self.squeeze_calculators[timeframe].get_current_signal(
                        data, symbol, timeframe
                    )
                    if signal is not None:
                        confirmation_signals[timeframe] = signal
                        
                        # Count valid confirmations (squeeze or strong momentum)
                        if signal.is_squeeze or signal.entry_signal or abs(signal.momentum) > 0.5:
                            valid_confirmations += 1
            
            # Check if we have enough confirmations
            is_valid_setup = valid_confirmations >= self.required_confirmations
            
            # Calculate overall strength
            overall_strength = self._calculate_overall_strength(primary_signal, confirmation_signals)
            
            # Check trend alignment
            trend_alignment = self._check_trend_alignment(primary_data)
            
            # Check volume confirmation
            volume_confirmation = self._check_volume_confirmation(primary_data)
            
            # Generate entry recommendation
            entry_recommendation = self._generate_entry_recommendation(
                primary_signal, confirmation_signals, is_valid_setup, 
                trend_alignment, volume_confirmation, overall_strength
            )
            
            return MultiTimeframeSignal(
                symbol=symbol,
                timestamp=datetime.now(),
                primary_signal=primary_signal,
                confirmation_signals=confirmation_signals,
                confirmations_count=valid_confirmations,
                required_confirmations=self.required_confirmations,
                is_valid_setup=is_valid_setup,
                overall_strength=overall_strength,
                trend_alignment=trend_alignment,
                volume_confirmation=volume_confirmation,
                entry_recommendation=entry_recommendation
            )
            
        except Exception as e:
            logger.error(f"Error analyzing {symbol} multi-timeframe: {e}")
            return None
    
    def _calculate_overall_strength(self, primary_signal: SqueezeSignal,
                                  confirmation_signals: Dict[str, SqueezeSignal]) -> float:
        """Calculate overall signal strength across timeframes"""

        # Dynamic weighting based on available timeframes
        total_timeframes = 1 + len(confirmation_signals)
        primary_weight = 0.6 if total_timeframes == 1 else 0.4  # Higher weight if only one timeframe
        confirmation_weight = (1.0 - primary_weight) / len(confirmation_signals) if confirmation_signals else 0

        # Start with primary signal strength
        total_strength = primary_signal.signal_strength * primary_weight

        # Add confirmation signals
        confirmation_strength = 0.0
        for timeframe, signal in confirmation_signals.items():
            confirmation_strength += signal.signal_strength * confirmation_weight

        total_strength += confirmation_strength

        # Bonus for squeeze alignment across timeframes
        all_signals = [primary_signal] + list(confirmation_signals.values())
        squeeze_count = sum(1 for signal in all_signals if signal.is_squeeze)
        squeeze_bonus = min(squeeze_count * 0.05, 0.15)  # Reduced bonus

        # Bonus for momentum alignment
        momentum_alignment = self._check_momentum_alignment(primary_signal, confirmation_signals)
        momentum_bonus = 0.1 if momentum_alignment else 0

        # Entry signal bonus
        entry_signals = sum(1 for signal in all_signals if signal.entry_signal)
        entry_bonus = min(entry_signals * 0.05, 0.1)

        # Calculate final strength
        final_strength = total_strength + squeeze_bonus + momentum_bonus + entry_bonus

        # Add debugging
        logger.debug(f"Overall strength calculation:")
        logger.debug(f"  Primary strength: {primary_signal.signal_strength:.3f} (weight: {primary_weight:.3f})")
        logger.debug(f"  Confirmation strength: {confirmation_strength:.3f}")
        logger.debug(f"  Squeeze bonus: {squeeze_bonus:.3f} ({squeeze_count} timeframes)")
        logger.debug(f"  Momentum bonus: {momentum_bonus:.3f}")
        logger.debug(f"  Entry bonus: {entry_bonus:.3f} ({entry_signals} signals)")
        logger.debug(f"  Final strength: {final_strength:.3f}")

        return min(final_strength, 1.0)
    
    def _check_momentum_alignment(self, primary_signal: SqueezeSignal, 
                                confirmation_signals: Dict[str, SqueezeSignal]) -> bool:
        """Check if momentum is aligned across timeframes"""
        
        all_signals = [primary_signal] + list(confirmation_signals.values())
        
        # Check if momentum direction is consistent
        positive_momentum = sum(1 for signal in all_signals if signal.momentum > 0)
        negative_momentum = sum(1 for signal in all_signals if signal.momentum < 0)
        
        # Consider aligned if 70% or more have same direction
        total_signals = len(all_signals)
        alignment_threshold = 0.7
        
        return (positive_momentum / total_signals >= alignment_threshold or 
                negative_momentum / total_signals >= alignment_threshold)
    
    def _check_trend_alignment(self, data: pd.DataFrame) -> bool:
        """Check if EMAs are aligned for trend confirmation"""
        
        if len(data) < max(self.filter_config['ema_fast'], self.filter_config['ema_slow']):
            return False
        
        ema_fast = TechnicalIndicators.ema(data['close'], self.filter_config['ema_fast'])
        ema_slow = TechnicalIndicators.ema(data['close'], self.filter_config['ema_slow'])
        
        # Bullish alignment: EMA8 > EMA21
        return ema_fast.iloc[-1] > ema_slow.iloc[-1]
    
    def _check_volume_confirmation(self, data: pd.DataFrame) -> bool:
        """Check if volume is above threshold"""
        
        if len(data) < self.filter_config['volume_period']:
            return False
        
        volume_avg = TechnicalIndicators.volume_sma(data['volume'], self.filter_config['volume_period'])
        current_volume = data['volume'].iloc[-1]
        avg_volume = volume_avg.iloc[-1]
        
        return current_volume > (avg_volume * self.filter_config['volume_threshold'])
    
    def _check_macd_confirmation(self, data: pd.DataFrame) -> bool:
        """Check MACD histogram improvement"""
        
        if len(data) < 26:  # MACD requires at least 26 periods
            return False
        
        macd_line, signal_line, histogram = TechnicalIndicators.macd(data['close'])
        
        # Check if histogram is improving (current > previous)
        if len(histogram) < 2:
            return False
        
        return histogram.iloc[-1] > histogram.iloc[-2]
    
    def _check_price_action_confirmation(self, data: pd.DataFrame) -> bool:
        """Check if price action is confirming the signal"""
        
        if len(data) < 3:
            return False
        
        # Check for rising price over last 2-3 bars
        recent_closes = data['close'].tail(3)
        return recent_closes.iloc[-1] > recent_closes.iloc[0]
    
    def _generate_entry_recommendation(self, primary_signal: SqueezeSignal,
                                     confirmation_signals: Dict[str, SqueezeSignal],
                                     is_valid_setup: bool, trend_alignment: bool,
                                     volume_confirmation: bool, overall_strength: float) -> str:
        """Generate entry recommendation based on all factors"""
        
        # Count positive factors
        positive_factors = 0
        total_factors = 5
        
        if primary_signal.entry_signal:
            positive_factors += 1
        
        if is_valid_setup:
            positive_factors += 1
        
        if trend_alignment:
            positive_factors += 1
        
        if volume_confirmation:
            positive_factors += 1
        
        if overall_strength > 0.7:
            positive_factors += 1
        
        # Generate recommendation
        factor_ratio = positive_factors / total_factors
        
        if factor_ratio >= 0.8 and primary_signal.entry_signal:
            return 'strong_buy'
        elif factor_ratio >= 0.6 and (primary_signal.entry_signal or primary_signal.is_squeeze):
            return 'buy'
        elif factor_ratio >= 0.4:
            return 'hold'
        else:
            return 'avoid'
    
    def scan_multiple_symbols(self, symbols: List[str], 
                            data_provider) -> List[MultiTimeframeSignal]:
        """
        Scan multiple symbols for TTM Squeeze setups
        
        Args:
            symbols: List of stock symbols to scan
            data_provider: Data provider instance to fetch data
            
        Returns:
            List of valid MultiTimeframeSignal objects
        """
        valid_signals = []
        
        for symbol in symbols:
            try:
                # Get data for all timeframes
                data_dict = {}
                for timeframe in [self.primary_timeframe] + self.confirmation_timeframes:
                    data = data_provider.get_historical_data(symbol, timeframe, periods=100)
                    if data is not None and len(data) >= 50:
                        data_dict[timeframe] = data
                
                # Analyze the symbol
                signal = self.analyze_symbol(symbol, data_dict)
                
                if signal and signal.is_valid_setup:
                    valid_signals.append(signal)
                    logger.info(f"Valid setup found for {symbol}: {signal.entry_recommendation}")
                
            except Exception as e:
                logger.error(f"Error scanning {symbol}: {e}")
                continue
        
        # Sort by overall strength
        valid_signals.sort(key=lambda x: x.overall_strength, reverse=True)
        
        return valid_signals
