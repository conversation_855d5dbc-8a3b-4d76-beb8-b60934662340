"""
AI-Enhanced Dynamic Stop Loss Manager
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
import logging
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class StopLossType(Enum):
    FIXED = "fixed"
    TRAILING = "trailing"
    BREAKEVEN = "breakeven"
    VOLATILITY_ADAPTIVE = "volatility_adaptive"
    AI_DYNAMIC = "ai_dynamic"

@dataclass
class StopLossLevel:
    """Stop loss level configuration"""
    symbol: str
    stop_type: StopLossType
    initial_stop: float
    current_stop: float
    entry_price: float
    highest_price: float  # For trailing stops
    lowest_price: float   # For short positions
    atr_multiplier: float
    confidence_level: float
    last_updated: datetime
    is_breakeven_triggered: bool = False

@dataclass
class VolatilityMetrics:
    """Real-time volatility metrics"""
    current_atr: float
    atr_percentile: float
    realized_vol: float
    implied_vol: float
    vol_regime: str  # 'low', 'normal', 'high', 'extreme'

class AIStopLossManager:
    """AI-powered dynamic stop loss management system"""
    
    def __init__(self, data_manager, risk_engine):
        self.data_manager = data_manager
        self.risk_engine = risk_engine
        self.active_stops = {}  # symbol -> StopLossLevel
        
        # AI model parameters
        self.volatility_lookback = 20
        self.confidence_threshold = 0.7
        
        # Stop loss parameters by regime
        self.regime_parameters = {
            'low_vol': {
                'base_atr_multiplier': 1.5,
                'trailing_distance': 0.015,  # 1.5%
                'breakeven_trigger': 1.0     # 1:1 risk/reward
            },
            'normal_vol': {
                'base_atr_multiplier': 2.0,
                'trailing_distance': 0.02,   # 2%
                'breakeven_trigger': 1.0
            },
            'high_vol': {
                'base_atr_multiplier': 2.5,
                'trailing_distance': 0.03,   # 3%
                'breakeven_trigger': 1.2     # 1.2:1 for safety
            },
            'extreme_vol': {
                'base_atr_multiplier': 3.0,
                'trailing_distance': 0.04,   # 4%
                'breakeven_trigger': 1.5     # 1.5:1 for safety
            }
        }
        
        logger.info("AI Stop Loss Manager initialized")
    
    def create_ai_stop_loss(self, symbol: str, entry_price: float, 
                           position_side: str, signal_confidence: float,
                           market_context: Dict) -> StopLossLevel:
        """
        Create AI-enhanced dynamic stop loss
        
        Args:
            symbol: Stock symbol
            entry_price: Entry price of position
            position_side: 'long' or 'short'
            signal_confidence: AI signal confidence (0-100)
            market_context: Current market conditions
            
        Returns:
            StopLossLevel configuration
        """
        try:
            # Calculate volatility metrics
            vol_metrics = self._calculate_volatility_metrics(symbol)
            
            # Determine optimal stop loss type
            stop_type = self._determine_stop_type(signal_confidence, vol_metrics)
            
            # Calculate initial stop loss distance
            stop_distance = self._calculate_ai_stop_distance(
                symbol, vol_metrics, signal_confidence, market_context
            )
            
            # Calculate stop price
            if position_side.lower() == 'long':
                stop_price = entry_price * (1 - stop_distance)
            else:  # short
                stop_price = entry_price * (1 + stop_distance)
            
            # Create stop loss level
            stop_level = StopLossLevel(
                symbol=symbol,
                stop_type=stop_type,
                initial_stop=stop_price,
                current_stop=stop_price,
                entry_price=entry_price,
                highest_price=entry_price,
                lowest_price=entry_price,
                atr_multiplier=self._get_atr_multiplier(vol_metrics),
                confidence_level=signal_confidence,
                last_updated=datetime.now()
            )
            
            # Store active stop
            self.active_stops[symbol] = stop_level
            
            logger.info(f"Created {stop_type.value} stop loss for {symbol}: "
                       f"Entry: ${entry_price:.2f}, Stop: ${stop_price:.2f} "
                       f"({stop_distance:.2%} distance)")
            
            return stop_level
            
        except Exception as e:
            logger.error(f"Error creating AI stop loss for {symbol}: {e}")
            # Fallback to simple stop
            return self._create_fallback_stop(symbol, entry_price, position_side)
    
    def update_stop_losses(self, current_prices: Dict[str, float]) -> Dict[str, Dict]:
        """
        Update all active stop losses based on current prices
        
        Args:
            current_prices: Dict of symbol -> current_price
            
        Returns:
            Dict of stop loss updates and triggered stops
        """
        updates = {}
        triggered_stops = {}
        
        for symbol, stop_level in self.active_stops.items():
            if symbol not in current_prices:
                continue
                
            current_price = current_prices[symbol]
            
            try:
                # Update price tracking
                stop_level.highest_price = max(stop_level.highest_price, current_price)
                stop_level.lowest_price = min(stop_level.lowest_price, current_price)
                
                # Update stop based on type
                if stop_level.stop_type == StopLossType.AI_DYNAMIC:
                    new_stop = self._update_ai_dynamic_stop(stop_level, current_price)
                elif stop_level.stop_type == StopLossType.TRAILING:
                    new_stop = self._update_trailing_stop(stop_level, current_price)
                elif stop_level.stop_type == StopLossType.VOLATILITY_ADAPTIVE:
                    new_stop = self._update_volatility_adaptive_stop(stop_level, current_price)
                else:
                    new_stop = stop_level.current_stop  # Fixed stop
                
                # Check for breakeven trigger
                if not stop_level.is_breakeven_triggered:
                    new_stop = self._check_breakeven_trigger(stop_level, current_price, new_stop)
                
                # Update stop level
                if new_stop != stop_level.current_stop:
                    old_stop = stop_level.current_stop
                    stop_level.current_stop = new_stop
                    stop_level.last_updated = datetime.now()
                    
                    updates[symbol] = {
                        'old_stop': old_stop,
                        'new_stop': new_stop,
                        'current_price': current_price,
                        'stop_type': stop_level.stop_type.value
                    }
                
                # Check if stop is triggered
                is_triggered = self._is_stop_triggered(stop_level, current_price)
                if is_triggered:
                    triggered_stops[symbol] = {
                        'stop_price': stop_level.current_stop,
                        'current_price': current_price,
                        'entry_price': stop_level.entry_price,
                        'stop_type': stop_level.stop_type.value
                    }
                    
            except Exception as e:
                logger.error(f"Error updating stop loss for {symbol}: {e}")
        
        return {'updates': updates, 'triggered': triggered_stops}
    
    def _calculate_volatility_metrics(self, symbol: str) -> VolatilityMetrics:
        """Calculate real-time volatility metrics"""
        try:
            # Get recent data
            data = self.data_manager.get_historical_data(symbol, '1Day', periods=50)
            
            if data is None or len(data) < self.volatility_lookback:
                return self._get_default_volatility_metrics()
            
            # Calculate ATR
            high_low = data['high'] - data['low']
            high_close = np.abs(data['high'] - data['close'].shift(1))
            low_close = np.abs(data['low'] - data['close'].shift(1))
            
            true_range = np.maximum(high_low, np.maximum(high_close, low_close))
            atr = true_range.rolling(window=self.volatility_lookback).mean().iloc[-1]
            current_atr = atr / data['close'].iloc[-1]  # Normalize by price
            
            # Calculate ATR percentile
            atr_series = true_range.rolling(window=self.volatility_lookback).mean()
            atr_percentile = (atr_series <= atr).mean() * 100
            
            # Calculate realized volatility
            returns = data['close'].pct_change().dropna()
            realized_vol = returns.rolling(window=self.volatility_lookback).std().iloc[-1] * np.sqrt(252)
            
            # Determine volatility regime
            vol_regime = self._classify_volatility_regime(atr_percentile, realized_vol)
            
            return VolatilityMetrics(
                current_atr=current_atr,
                atr_percentile=atr_percentile,
                realized_vol=realized_vol,
                implied_vol=realized_vol * 1.2,  # Estimate
                vol_regime=vol_regime
            )
            
        except Exception as e:
            logger.warning(f"Error calculating volatility metrics for {symbol}: {e}")
            return self._get_default_volatility_metrics()
    
    def _classify_volatility_regime(self, atr_percentile: float, realized_vol: float) -> str:
        """Classify current volatility regime"""
        if atr_percentile > 80 or realized_vol > 0.4:
            return 'extreme_vol'
        elif atr_percentile > 60 or realized_vol > 0.3:
            return 'high_vol'
        elif atr_percentile < 20 and realized_vol < 0.15:
            return 'low_vol'
        else:
            return 'normal_vol'
    
    def _determine_stop_type(self, signal_confidence: float, 
                           vol_metrics: VolatilityMetrics) -> StopLossType:
        """Determine optimal stop loss type based on conditions"""
        
        # High confidence signals get AI dynamic stops
        if signal_confidence > 85:
            return StopLossType.AI_DYNAMIC
        
        # High volatility gets volatility adaptive
        elif vol_metrics.vol_regime in ['high_vol', 'extreme_vol']:
            return StopLossType.VOLATILITY_ADAPTIVE
        
        # Medium confidence gets trailing stops
        elif signal_confidence > 70:
            return StopLossType.TRAILING
        
        # Low confidence gets fixed stops
        else:
            return StopLossType.FIXED
    
    def _calculate_ai_stop_distance(self, symbol: str, vol_metrics: VolatilityMetrics,
                                  signal_confidence: float, market_context: Dict) -> float:
        """Calculate AI-optimized stop distance"""
        
        # Base distance from volatility
        base_distance = vol_metrics.current_atr * self._get_atr_multiplier(vol_metrics)
        
        # Adjust for signal confidence
        confidence_adjustment = 1.0 - (signal_confidence - 50) / 100 * 0.3  # ±30% adjustment
        
        # Adjust for market conditions
        market_regime = market_context.get('regime', 'unknown')
        market_adjustment = {
            'trending': 0.9,      # Tighter stops in trends
            'ranging': 1.1,       # Wider stops in ranges
            'volatile': 1.3,      # Much wider in volatile markets
            'calm': 0.8,          # Tighter in calm markets
            'unknown': 1.0
        }.get(market_regime, 1.0)
        
        # Market breadth adjustment
        market_breadth = market_context.get('market_breadth', 50)
        breadth_adjustment = 1.0 + (50 - market_breadth) / 100 * 0.2  # ±20% adjustment
        
        # Calculate final distance
        final_distance = (base_distance * confidence_adjustment * 
                         market_adjustment * breadth_adjustment)
        
        # Ensure reasonable bounds
        return max(0.01, min(0.08, final_distance))  # 1% to 8%
    
    def _get_atr_multiplier(self, vol_metrics: VolatilityMetrics) -> float:
        """Get ATR multiplier based on volatility regime"""
        return self.regime_parameters[vol_metrics.vol_regime]['base_atr_multiplier']
    
    def _update_ai_dynamic_stop(self, stop_level: StopLossLevel, 
                              current_price: float) -> float:
        """Update AI dynamic stop loss"""
        try:
            # Recalculate volatility metrics
            vol_metrics = self._calculate_volatility_metrics(stop_level.symbol)
            
            # Calculate new optimal distance
            profit_ratio = (current_price - stop_level.entry_price) / stop_level.entry_price
            
            # Tighten stop as profit increases
            if profit_ratio > 0.05:  # 5% profit
                tightening_factor = 0.8 - min(profit_ratio * 2, 0.3)  # Up to 30% tighter
            else:
                tightening_factor = 1.0
            
            # Calculate new stop distance
            new_distance = (vol_metrics.current_atr * stop_level.atr_multiplier * 
                          tightening_factor)
            
            new_stop = current_price * (1 - new_distance)
            
            # Only move stop up (for long positions)
            return max(stop_level.current_stop, new_stop)
            
        except Exception as e:
            logger.warning(f"Error updating AI dynamic stop: {e}")
            return stop_level.current_stop
    
    def _update_trailing_stop(self, stop_level: StopLossLevel, 
                            current_price: float) -> float:
        """Update trailing stop loss"""
        try:
            vol_metrics = self._calculate_volatility_metrics(stop_level.symbol)
            trailing_distance = self.regime_parameters[vol_metrics.vol_regime]['trailing_distance']
            
            # Calculate new trailing stop
            new_stop = stop_level.highest_price * (1 - trailing_distance)
            
            # Only move stop up
            return max(stop_level.current_stop, new_stop)
            
        except Exception:
            return stop_level.current_stop
    
    def _update_volatility_adaptive_stop(self, stop_level: StopLossLevel,
                                       current_price: float) -> float:
        """Update volatility-adaptive stop loss"""
        try:
            vol_metrics = self._calculate_volatility_metrics(stop_level.symbol)
            
            # Adjust stop based on current volatility
            current_distance = vol_metrics.current_atr * stop_level.atr_multiplier
            new_stop = current_price * (1 - current_distance)
            
            # Only move stop up
            return max(stop_level.current_stop, new_stop)
            
        except Exception:
            return stop_level.current_stop
    
    def _check_breakeven_trigger(self, stop_level: StopLossLevel,
                               current_price: float, current_stop: float) -> float:
        """Check and apply breakeven stop trigger"""
        try:
            vol_metrics = self._calculate_volatility_metrics(stop_level.symbol)
            breakeven_trigger = self.regime_parameters[vol_metrics.vol_regime]['breakeven_trigger']
            
            # Calculate profit ratio
            profit_ratio = (current_price - stop_level.entry_price) / stop_level.entry_price
            risk_ratio = (stop_level.entry_price - stop_level.initial_stop) / stop_level.entry_price
            
            # Check if breakeven should trigger
            if profit_ratio >= risk_ratio * breakeven_trigger:
                stop_level.is_breakeven_triggered = True
                # Move stop to breakeven (entry price)
                breakeven_stop = stop_level.entry_price
                logger.info(f"Breakeven triggered for {stop_level.symbol} at ${current_price:.2f}")
                return max(current_stop, breakeven_stop)
            
            return current_stop
            
        except Exception:
            return current_stop
    
    def _is_stop_triggered(self, stop_level: StopLossLevel, current_price: float) -> bool:
        """Check if stop loss is triggered"""
        return current_price <= stop_level.current_stop
    
    def _create_fallback_stop(self, symbol: str, entry_price: float, 
                            position_side: str) -> StopLossLevel:
        """Create fallback stop loss"""
        stop_distance = 0.02  # 2% default
        
        if position_side.lower() == 'long':
            stop_price = entry_price * (1 - stop_distance)
        else:
            stop_price = entry_price * (1 + stop_distance)
        
        return StopLossLevel(
            symbol=symbol,
            stop_type=StopLossType.FIXED,
            initial_stop=stop_price,
            current_stop=stop_price,
            entry_price=entry_price,
            highest_price=entry_price,
            lowest_price=entry_price,
            atr_multiplier=2.0,
            confidence_level=50.0,
            last_updated=datetime.now()
        )
    
    def _get_default_volatility_metrics(self) -> VolatilityMetrics:
        """Get default volatility metrics"""
        return VolatilityMetrics(
            current_atr=0.02,
            atr_percentile=50.0,
            realized_vol=0.25,
            implied_vol=0.30,
            vol_regime='normal_vol'
        )
    
    def remove_stop(self, symbol: str):
        """Remove stop loss for symbol"""
        if symbol in self.active_stops:
            del self.active_stops[symbol]
            logger.info(f"Removed stop loss for {symbol}")
    
    def get_active_stops(self) -> Dict[str, StopLossLevel]:
        """Get all active stop losses"""
        return self.active_stops.copy()
    
    def get_stop_summary(self) -> Dict:
        """Get summary of stop loss system"""
        return {
            'active_stops': len(self.active_stops),
            'stop_types': {stop_type.value: sum(1 for stop in self.active_stops.values() 
                                              if stop.stop_type == stop_type) 
                          for stop_type in StopLossType},
            'breakeven_triggered': sum(1 for stop in self.active_stops.values() 
                                     if stop.is_breakeven_triggered),
            'regime_parameters': self.regime_parameters
        }
