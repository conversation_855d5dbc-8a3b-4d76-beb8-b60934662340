"""
Alpaca Trading Integration for TTM Squeeze Trading System
"""
import logging
from typing import Dict, List, Optional, Union
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import time

try:
    import alpaca_trade_api as tradeapi
    from alpaca_trade_api.entity import Order, Position
except ImportError:
    logger = logging.getLogger(__name__)
    logger.warning("alpaca_trade_api not installed")
    tradeapi = None

from config import Config

logger = logging.getLogger(__name__)

class OrderSide(Enum):
    BUY = "buy"
    SELL = "sell"

class OrderType(Enum):
    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"
    STOP_LIMIT = "stop_limit"

class TimeInForce(Enum):
    DAY = "day"
    GTC = "gtc"  # Good Till Canceled
    IOC = "ioc"  # Immediate or Cancel
    FOK = "fok"  # Fill or Kill

@dataclass
class TradeOrder:
    """Trade order data class"""
    symbol: str
    side: OrderSide
    quantity: int
    order_type: OrderType
    price: Optional[float] = None
    stop_price: Optional[float] = None
    time_in_force: TimeInForce = TimeInForce.DAY
    client_order_id: Optional[str] = None

@dataclass
class TradeResult:
    """Trade execution result"""
    success: bool
    order_id: Optional[str] = None
    message: str = ""
    filled_qty: int = 0
    filled_price: float = 0.0
    commission: float = 0.0

class AlpacaTrader:
    """Alpaca trading interface"""
    
    def __init__(self):
        """Initialize Alpaca trading API"""
        if tradeapi is None:
            raise ImportError("alpaca_trade_api not installed")
        
        try:
            self.api = tradeapi.REST(
                Config.ALPACA_API_KEY,
                Config.ALPACA_SECRET_KEY,
                Config.ALPACA_BASE_URL,
                api_version='v2'
            )
            
            # Test connection and get account info
            self.account = self.api.get_account()
            logger.info(f"Connected to Alpaca - Account: {self.account.id}")
            logger.info(f"Account Status: {self.account.status}")
            logger.info(f"Buying Power: ${float(self.account.buying_power):,.2f}")
            
            # Risk management settings
            self.risk_config = Config.RISK_MANAGEMENT
            
        except Exception as e:
            logger.error(f"Failed to initialize Alpaca trader: {e}")
            raise
    
    def get_account_info(self) -> Dict:
        """Get account information"""
        try:
            account = self.api.get_account()
            
            return {
                'account_id': account.id,
                'status': account.status,
                'equity': float(account.equity),
                'buying_power': float(account.buying_power),
                'cash': float(account.cash),
                'portfolio_value': float(account.portfolio_value),
                'day_trade_count': int(account.daytrade_count),
                'pattern_day_trader': account.pattern_day_trader
            }
            
        except Exception as e:
            logger.error(f"Error getting account info: {e}")
            return {}
    
    def get_positions(self) -> List[Dict]:
        """Get current positions"""
        try:
            positions = self.api.list_positions()

            position_list = []
            for pos in positions:
                # Handle fractional shares by converting to float first, then int if needed
                qty_float = float(pos.qty)
                qty_display = qty_float if qty_float != int(qty_float) else int(qty_float)

                position_list.append({
                    'symbol': pos.symbol,
                    'quantity': qty_display,
                    'side': 'long' if qty_float > 0 else 'short',
                    'market_value': float(pos.market_value),
                    'cost_basis': float(pos.cost_basis),
                    'unrealized_pl': float(pos.unrealized_pl),
                    'unrealized_plpc': float(pos.unrealized_plpc),
                    'avg_entry_price': float(pos.avg_entry_price),
                    'current_price': float(pos.current_price)
                })

            return position_list

        except Exception as e:
            logger.error(f"Error getting positions: {e}")
            return []
    
    def get_orders(self, status: str = 'all', limit: int = 50) -> List[Dict]:
        """Get orders"""
        try:
            orders = self.api.list_orders(
                status=status,
                limit=limit,
                direction='desc'
            )

            order_list = []
            for order in orders:
                # Handle fractional quantities
                qty_float = float(order.qty)
                qty_display = qty_float if qty_float != int(qty_float) else int(qty_float)

                filled_qty_float = float(order.filled_qty or 0)
                filled_qty_display = filled_qty_float if filled_qty_float != int(filled_qty_float) else int(filled_qty_float)

                order_list.append({
                    'id': order.id,
                    'symbol': order.symbol,
                    'side': order.side,
                    'quantity': qty_display,
                    'filled_qty': filled_qty_display,
                    'order_type': order.order_type,
                    'status': order.status,
                    'limit_price': float(order.limit_price) if order.limit_price else None,
                    'stop_price': float(order.stop_price) if order.stop_price else None,
                    'submitted_at': order.submitted_at,
                    'filled_at': order.filled_at,
                    'filled_avg_price': float(order.filled_avg_price) if order.filled_avg_price else None
                })

            return order_list

        except Exception as e:
            logger.error(f"Error getting orders: {e}")
            return []
    
    def place_order(self, trade_order: TradeOrder) -> TradeResult:
        """Place a trade order"""
        try:
            # Validate order
            validation_result = self._validate_order(trade_order)
            if not validation_result.success:
                return validation_result
            
            # Prepare order parameters
            order_params = {
                'symbol': trade_order.symbol,
                'qty': trade_order.quantity,
                'side': trade_order.side.value,
                'type': trade_order.order_type.value,
                'time_in_force': trade_order.time_in_force.value
            }
            
            # Add price parameters based on order type
            if trade_order.order_type == OrderType.LIMIT:
                if trade_order.price is None:
                    return TradeResult(False, message="Limit price required for limit orders")
                order_params['limit_price'] = trade_order.price
            
            elif trade_order.order_type == OrderType.STOP:
                if trade_order.stop_price is None:
                    return TradeResult(False, message="Stop price required for stop orders")
                order_params['stop_price'] = trade_order.stop_price
            
            elif trade_order.order_type == OrderType.STOP_LIMIT:
                if trade_order.price is None or trade_order.stop_price is None:
                    return TradeResult(False, message="Both limit and stop prices required")
                order_params['limit_price'] = trade_order.price
                order_params['stop_price'] = trade_order.stop_price
            
            # Add client order ID if provided
            if trade_order.client_order_id:
                order_params['client_order_id'] = trade_order.client_order_id
            
            # Submit order
            order = self.api.submit_order(**order_params)
            
            logger.info(f"Order submitted: {order.id} - {trade_order.side.value} {trade_order.quantity} {trade_order.symbol}")
            
            return TradeResult(
                success=True,
                order_id=order.id,
                message=f"Order submitted successfully",
                filled_qty=int(order.filled_qty or 0),
                filled_price=float(order.filled_avg_price or 0)
            )
            
        except Exception as e:
            error_msg = f"Error placing order: {e}"
            logger.error(error_msg)
            return TradeResult(False, message=error_msg)
    
    def cancel_order(self, order_id: str) -> TradeResult:
        """Cancel an order"""
        try:
            self.api.cancel_order(order_id)
            logger.info(f"Order cancelled: {order_id}")
            
            return TradeResult(
                success=True,
                order_id=order_id,
                message="Order cancelled successfully"
            )
            
        except Exception as e:
            error_msg = f"Error cancelling order {order_id}: {e}"
            logger.error(error_msg)
            return TradeResult(False, message=error_msg)
    
    def cancel_all_orders(self) -> TradeResult:
        """Cancel all open orders"""
        try:
            self.api.cancel_all_orders()
            logger.info("All orders cancelled")
            
            return TradeResult(
                success=True,
                message="All orders cancelled successfully"
            )
            
        except Exception as e:
            error_msg = f"Error cancelling all orders: {e}"
            logger.error(error_msg)
            return TradeResult(False, message=error_msg)
    
    def close_position(self, symbol: str, percentage: float = 100.0) -> TradeResult:
        """Close a position"""
        try:
            if percentage <= 0 or percentage > 100:
                return TradeResult(False, message="Percentage must be between 0 and 100")

            # Get current position
            try:
                position = self.api.get_position(symbol)
            except:
                return TradeResult(False, message=f"No position found for {symbol}")

            qty_float = float(position.qty)
            if qty_float == 0:
                return TradeResult(False, message=f"No position to close for {symbol}")

            # Calculate quantity to close (handle fractional shares)
            qty_to_close_float = abs(qty_float) * (percentage / 100)
            qty_to_close = int(qty_to_close_float) if qty_to_close_float == int(qty_to_close_float) else qty_to_close_float

            # Determine side (opposite of current position)
            side = OrderSide.SELL if qty_float > 0 else OrderSide.BUY
            
            # Create and place order
            trade_order = TradeOrder(
                symbol=symbol,
                side=side,
                quantity=qty_to_close,
                order_type=OrderType.MARKET,
                time_in_force=TimeInForce.DAY
            )
            
            result = self.place_order(trade_order)
            
            if result.success:
                logger.info(f"Position closed: {percentage}% of {symbol}")
            
            return result
            
        except Exception as e:
            error_msg = f"Error closing position for {symbol}: {e}"
            logger.error(error_msg)
            return TradeResult(False, message=error_msg)
    
    def _validate_order(self, trade_order: TradeOrder) -> TradeResult:
        """Validate order before submission"""
        
        # Check account status
        if self.account.status != 'ACTIVE':
            return TradeResult(False, message="Account is not active")
        
        # Check buying power for buy orders
        if trade_order.side == OrderSide.BUY:
            account_info = self.get_account_info()
            buying_power = account_info.get('buying_power', 0)
            
            # Estimate order value (use current price if no limit price)
            if trade_order.price:
                estimated_value = trade_order.quantity * trade_order.price
            else:
                # Get current price for market orders
                try:
                    quote = self.api.get_latest_quote(trade_order.symbol)
                    estimated_value = trade_order.quantity * float(quote.ap)
                except:
                    estimated_value = 0
            
            if estimated_value > buying_power:
                return TradeResult(False, message="Insufficient buying power")
        
        # Check position size limits
        portfolio_value = float(self.account.portfolio_value)
        max_position_value = portfolio_value * self.risk_config['max_position_size']
        
        if trade_order.side == OrderSide.BUY:
            estimated_value = trade_order.quantity * (trade_order.price or 100)  # Conservative estimate
            if estimated_value > max_position_value:
                return TradeResult(False, message="Order exceeds maximum position size")
        
        # Check maximum number of positions
        current_positions = len(self.get_positions())
        if (trade_order.side == OrderSide.BUY and 
            current_positions >= self.risk_config['max_positions']):
            return TradeResult(False, message="Maximum number of positions reached")
        
        return TradeResult(True, message="Order validation passed")
    
    def calculate_position_size(self, symbol: str, entry_price: float, 
                              stop_loss_price: float) -> int:
        """Calculate position size based on risk management rules"""
        try:
            account_info = self.get_account_info()
            portfolio_value = account_info.get('portfolio_value', 0)
            
            # Calculate risk per share
            risk_per_share = abs(entry_price - stop_loss_price)
            
            # Calculate maximum risk amount
            max_risk_amount = portfolio_value * self.risk_config['max_position_size']
            
            # Calculate position size
            if risk_per_share > 0:
                position_size = int(max_risk_amount / risk_per_share)
            else:
                position_size = 0
            
            # Ensure position doesn't exceed maximum position value
            max_position_value = portfolio_value * self.risk_config['max_position_size']
            max_shares_by_value = int(max_position_value / entry_price)
            
            position_size = min(position_size, max_shares_by_value)
            
            return max(position_size, 0)
            
        except Exception as e:
            logger.error(f"Error calculating position size: {e}")
            return 0
