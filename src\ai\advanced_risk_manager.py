"""
Advanced AI Risk Management System for TTM Squeeze Trading
Dynamic risk assessment with multi-factor analysis and portfolio optimization
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from datetime import datetime, timedelta
import logging
import asyncio
from enum import Enum

# ML imports
try:
    from sklearn.ensemble import IsolationForest
    from sklearn.preprocessing import StandardScaler
    from sklearn.cluster import KMeans
    import scipy.optimize as optimize
    ML_AVAILABLE = True
except ImportError:
    ML_AVAILABLE = False

logger = logging.getLogger(__name__)

class RiskLevel(Enum):
    """Risk level classifications"""
    VERY_LOW = "very_low"
    LOW = "low"
    MODERATE = "moderate"
    HIGH = "high"
    VERY_HIGH = "very_high"

@dataclass
class RiskAssessment:
    """Comprehensive risk assessment result"""
    overall_risk_score: float  # 0-100
    risk_level: RiskLevel
    risk_factors: Dict[str, float]
    recommended_position_size: float
    max_position_size: float
    stop_loss_price: Optional[float]
    risk_reward_ratio: float
    confidence: float
    warnings: List[str]
    recommendations: List[str]

@dataclass
class PortfolioRisk:
    """Portfolio-level risk metrics"""
    total_risk_score: float
    concentration_risk: float
    correlation_risk: float
    volatility_risk: float
    liquidity_risk: float
    market_risk: float
    var_1day: float  # Value at Risk 1 day
    var_5day: float  # Value at Risk 5 days
    max_drawdown_estimate: float
    sharpe_ratio: float

class VolatilityPredictor:
    """AI-based volatility prediction model"""
    
    def __init__(self):
        self.model = None
        self.scaler = StandardScaler()
        self.lookback_period = 30
        self.is_trained = False
    
    def train(self, historical_data: Dict[str, pd.DataFrame]):
        """Train volatility prediction model"""
        if not ML_AVAILABLE:
            logger.warning("ML libraries not available for volatility prediction")
            return
        
        try:
            # Prepare training data
            features = []
            targets = []
            
            for symbol, data in historical_data.items():
                if len(data) < self.lookback_period + 10:
                    continue
                
                # Calculate features
                data['returns'] = data['close'].pct_change()
                data['volatility'] = data['returns'].rolling(window=20).std()
                data['volume_ratio'] = data['volume'] / data['volume'].rolling(window=20).mean()
                data['price_range'] = (data['high'] - data['low']) / data['close']
                
                # Create feature vectors
                for i in range(self.lookback_period, len(data) - 5):
                    feature_vector = [
                        data['volatility'].iloc[i-20:i].mean(),
                        data['volume_ratio'].iloc[i-5:i].mean(),
                        data['price_range'].iloc[i-5:i].mean(),
                        abs(data['returns'].iloc[i-1]),
                        data['returns'].iloc[i-5:i].std()
                    ]
                    
                    # Target: future 5-day volatility
                    future_vol = data['returns'].iloc[i:i+5].std()
                    
                    features.append(feature_vector)
                    targets.append(future_vol)
            
            if len(features) > 100:
                X = np.array(features)
                y = np.array(targets)
                
                # Scale features
                X_scaled = self.scaler.fit_transform(X)
                
                # Simple linear model for volatility prediction
                from sklearn.linear_model import Ridge
                self.model = Ridge(alpha=1.0)
                self.model.fit(X_scaled, y)
                self.is_trained = True
                
                logger.info(f"Volatility predictor trained on {len(features)} samples")
            
        except Exception as e:
            logger.error(f"Error training volatility predictor: {e}")
    
    def predict_volatility(self, symbol_data: pd.DataFrame) -> float:
        """Predict future volatility for a symbol"""
        if not self.is_trained or not ML_AVAILABLE:
            # Fallback: use historical volatility
            returns = symbol_data['close'].pct_change().dropna()
            return returns.std() * np.sqrt(252) if len(returns) > 0 else 0.2
        
        try:
            # Prepare features
            symbol_data['returns'] = symbol_data['close'].pct_change()
            symbol_data['volatility'] = symbol_data['returns'].rolling(window=20).std()
            symbol_data['volume_ratio'] = symbol_data['volume'] / symbol_data['volume'].rolling(window=20).mean()
            symbol_data['price_range'] = (symbol_data['high'] - symbol_data['low']) / symbol_data['close']
            
            feature_vector = [
                symbol_data['volatility'].iloc[-20:].mean(),
                symbol_data['volume_ratio'].iloc[-5:].mean(),
                symbol_data['price_range'].iloc[-5:].mean(),
                abs(symbol_data['returns'].iloc[-1]),
                symbol_data['returns'].iloc[-5:].std()
            ]
            
            # Scale and predict
            X_scaled = self.scaler.transform([feature_vector])
            predicted_vol = self.model.predict(X_scaled)[0]
            
            # Annualize volatility
            return predicted_vol * np.sqrt(252)
            
        except Exception as e:
            logger.error(f"Error predicting volatility: {e}")
            # Fallback
            returns = symbol_data['close'].pct_change().dropna()
            return returns.std() * np.sqrt(252) if len(returns) > 0 else 0.2

class CorrelationAnalyzer:
    """Analyze portfolio correlations and concentration risk"""
    
    def __init__(self):
        self.correlation_matrix = None
        self.sector_correlations = {}
    
    def analyze_correlations(self, portfolio_data: Dict[str, pd.DataFrame]) -> Dict[str, float]:
        """Analyze correlations between portfolio holdings"""
        try:
            if len(portfolio_data) < 2:
                return {'correlation_risk': 0.0}
            
            # Calculate return correlations
            returns_data = {}
            for symbol, data in portfolio_data.items():
                returns = data['close'].pct_change().dropna()
                if len(returns) > 20:
                    returns_data[symbol] = returns
            
            if len(returns_data) < 2:
                return {'correlation_risk': 0.0}
            
            # Create correlation matrix
            returns_df = pd.DataFrame(returns_data).dropna()
            self.correlation_matrix = returns_df.corr()
            
            # Calculate correlation risk metrics
            avg_correlation = self.correlation_matrix.values[np.triu_indices_from(self.correlation_matrix.values, k=1)].mean()
            max_correlation = self.correlation_matrix.values[np.triu_indices_from(self.correlation_matrix.values, k=1)].max()
            
            # Risk increases with higher correlations
            correlation_risk = (avg_correlation + max_correlation) / 2 * 100
            
            return {
                'correlation_risk': max(0, min(100, correlation_risk)),
                'avg_correlation': avg_correlation,
                'max_correlation': max_correlation,
                'diversification_ratio': 1 - avg_correlation
            }
            
        except Exception as e:
            logger.error(f"Error analyzing correlations: {e}")
            return {'correlation_risk': 50.0}  # Default moderate risk

class AIRiskManager:
    """Advanced AI-powered risk management system"""
    
    def __init__(self, data_manager, trading_manager):
        self.data_manager = data_manager
        self.trading_manager = trading_manager
        self.volatility_predictor = VolatilityPredictor()
        self.correlation_analyzer = CorrelationAnalyzer()
        self.anomaly_detector = None
        
        # Risk parameters
        self.max_portfolio_risk = 0.02  # 2% portfolio risk per trade
        self.max_position_size = 0.10   # 10% max position size
        self.max_correlation = 0.7      # Maximum correlation threshold
        
        if ML_AVAILABLE:
            self.anomaly_detector = IsolationForest(contamination=0.1, random_state=42)
    
    async def assess_trade_risk(self, signal: Any, market_context: Dict[str, Any]) -> RiskAssessment:
        """Comprehensive risk assessment for a trading signal"""
        try:
            symbol = signal.symbol
            
            # Get symbol data
            symbol_data = self.data_manager.get_historical_data(symbol, '1Day', 100)
            if symbol_data is None or len(symbol_data) < 30:
                return self._high_risk_assessment("Insufficient historical data")
            
            # Calculate individual risk factors
            risk_factors = {}
            
            # 1. Volatility Risk
            predicted_vol = self.volatility_predictor.predict_volatility(symbol_data)
            risk_factors['volatility_risk'] = min(100, predicted_vol * 100)
            
            # 2. Liquidity Risk
            avg_volume = symbol_data['volume'].tail(20).mean()
            risk_factors['liquidity_risk'] = self._assess_liquidity_risk(avg_volume, symbol_data['close'].iloc[-1])
            
            # 3. Technical Risk
            risk_factors['technical_risk'] = self._assess_technical_risk(symbol_data, signal)
            
            # 4. Market Regime Risk
            risk_factors['market_regime_risk'] = self._assess_market_regime_risk(market_context)
            
            # 5. Portfolio Concentration Risk
            risk_factors['concentration_risk'] = await self._assess_concentration_risk(symbol)
            
            # 6. Correlation Risk
            portfolio_data = await self._get_portfolio_data()
            correlation_metrics = self.correlation_analyzer.analyze_correlations(portfolio_data)
            risk_factors['correlation_risk'] = correlation_metrics.get('correlation_risk', 50.0)
            
            # Calculate overall risk score
            weights = {
                'volatility_risk': 0.25,
                'liquidity_risk': 0.15,
                'technical_risk': 0.20,
                'market_regime_risk': 0.15,
                'concentration_risk': 0.15,
                'correlation_risk': 0.10
            }
            
            overall_risk = sum(risk_factors[factor] * weight for factor, weight in weights.items())
            
            # Determine risk level
            risk_level = self._classify_risk_level(overall_risk)
            
            # Calculate position sizing
            recommended_size, max_size = self._calculate_position_sizing(
                overall_risk, predicted_vol, signal
            )
            
            # Calculate stop loss
            stop_loss = self._calculate_dynamic_stop_loss(
                symbol_data, predicted_vol, overall_risk
            )
            
            # Risk-reward ratio
            risk_reward = self._calculate_risk_reward_ratio(
                signal, stop_loss, symbol_data['close'].iloc[-1]
            )
            
            # Generate warnings and recommendations
            warnings = self._generate_warnings(risk_factors, overall_risk)
            recommendations = self._generate_recommendations(risk_factors, signal)
            
            return RiskAssessment(
                overall_risk_score=overall_risk,
                risk_level=risk_level,
                risk_factors=risk_factors,
                recommended_position_size=recommended_size,
                max_position_size=max_size,
                stop_loss_price=stop_loss,
                risk_reward_ratio=risk_reward,
                confidence=self._calculate_confidence(risk_factors),
                warnings=warnings,
                recommendations=recommendations
            )
            
        except Exception as e:
            logger.error(f"Error in risk assessment: {e}")
            return self._high_risk_assessment(f"Risk assessment error: {str(e)}")
    
    def _assess_liquidity_risk(self, avg_volume: float, price: float) -> float:
        """Assess liquidity risk based on volume and price"""
        dollar_volume = avg_volume * price
        
        # Risk thresholds
        if dollar_volume > 50_000_000:  # $50M+ daily volume
            return 10.0
        elif dollar_volume > 10_000_000:  # $10M+ daily volume
            return 25.0
        elif dollar_volume > 1_000_000:   # $1M+ daily volume
            return 50.0
        else:
            return 80.0  # High liquidity risk
    
    def _assess_technical_risk(self, data: pd.DataFrame, signal: Any) -> float:
        """Assess technical analysis risk factors"""
        try:
            # Calculate technical indicators
            data['rsi'] = self._calculate_rsi(data['close'])
            data['bb_position'] = self._calculate_bb_position(data)
            
            risk_score = 30.0  # Base risk
            
            # RSI extremes increase risk
            current_rsi = data['rsi'].iloc[-1]
            if current_rsi > 80 or current_rsi < 20:
                risk_score += 20.0
            
            # Bollinger Band position
            bb_pos = data['bb_position'].iloc[-1]
            if bb_pos > 0.9 or bb_pos < 0.1:
                risk_score += 15.0
            
            # Signal strength factor
            signal_strength = getattr(signal, 'signal_strength', 0.5)
            risk_score += (1 - signal_strength) * 30
            
            return min(100.0, risk_score)
            
        except Exception as e:
            logger.error(f"Error assessing technical risk: {e}")
            return 60.0  # Default moderate-high risk
    
    def _assess_market_regime_risk(self, market_context: Dict[str, Any]) -> float:
        """Assess risk based on current market regime"""
        try:
            vix_level = market_context.get('vix', 20.0)
            spy_change = market_context.get('spy_change_5d', 0.0)
            
            risk_score = 20.0  # Base risk
            
            # VIX-based risk
            if vix_level > 30:
                risk_score += 30.0
            elif vix_level > 25:
                risk_score += 20.0
            elif vix_level < 15:
                risk_score += 10.0  # Complacency risk
            
            # Market trend risk
            if abs(spy_change) > 5:  # High market volatility
                risk_score += 25.0
            
            return min(100.0, risk_score)
            
        except Exception as e:
            logger.error(f"Error assessing market regime risk: {e}")
            return 50.0
    
    async def _assess_concentration_risk(self, symbol: str) -> float:
        """Assess portfolio concentration risk"""
        try:
            positions = await self.trading_manager.get_positions()
            account = await self.trading_manager.get_account()
            
            total_value = float(account.get('portfolio_value', 100000))
            
            # Check if symbol already in portfolio
            existing_position = 0.0
            for pos in positions:
                if pos.get('symbol') == symbol:
                    existing_position = abs(float(pos.get('market_value', 0)))
            
            concentration = existing_position / total_value if total_value > 0 else 0
            
            # Risk increases with concentration
            if concentration > 0.15:  # >15% concentration
                return 80.0
            elif concentration > 0.10:  # >10% concentration
                return 60.0
            elif concentration > 0.05:  # >5% concentration
                return 40.0
            else:
                return 20.0
                
        except Exception as e:
            logger.error(f"Error assessing concentration risk: {e}")
            return 50.0
    
    def _calculate_position_sizing(self, risk_score: float, volatility: float, signal: Any) -> Tuple[float, float]:
        """Calculate recommended and maximum position sizes"""
        # Base position size (percentage of portfolio)
        base_size = 0.05  # 5% base position
        
        # Adjust for risk
        risk_multiplier = max(0.2, 1 - (risk_score / 100))
        
        # Adjust for volatility
        vol_multiplier = max(0.3, 1 - (volatility / 0.5))  # Reduce size for high vol
        
        # Adjust for signal strength
        signal_strength = getattr(signal, 'signal_strength', 0.5)
        signal_multiplier = 0.5 + (signal_strength * 0.5)
        
        recommended_size = base_size * risk_multiplier * vol_multiplier * signal_multiplier
        max_size = min(self.max_position_size, recommended_size * 1.5)
        
        return recommended_size, max_size
    
    def _calculate_dynamic_stop_loss(self, data: pd.DataFrame, volatility: float, risk_score: float) -> float:
        """Calculate dynamic stop loss based on volatility and risk"""
        current_price = data['close'].iloc[-1]
        
        # Base stop loss: 2 * ATR
        atr = self._calculate_atr(data)
        base_stop = 2 * atr
        
        # Adjust for volatility
        vol_adjustment = volatility * 0.5
        
        # Adjust for risk (tighter stops for higher risk)
        risk_adjustment = (risk_score / 100) * 0.5
        
        stop_distance = base_stop * (1 + vol_adjustment + risk_adjustment)
        
        return current_price - stop_distance
    
    def _calculate_risk_reward_ratio(self, signal: Any, stop_loss: float, current_price: float) -> float:
        """Calculate risk-reward ratio"""
        try:
            # Estimate target based on signal strength and historical moves
            signal_strength = getattr(signal, 'signal_strength', 0.5)
            target_multiplier = 1 + (signal_strength * 2)  # 1x to 3x risk
            
            risk = current_price - stop_loss
            reward = risk * target_multiplier
            
            return reward / risk if risk > 0 else 1.0
            
        except Exception as e:
            logger.error(f"Error calculating risk-reward ratio: {e}")
            return 1.0
    
    def _classify_risk_level(self, risk_score: float) -> RiskLevel:
        """Classify overall risk level"""
        if risk_score < 20:
            return RiskLevel.VERY_LOW
        elif risk_score < 40:
            return RiskLevel.LOW
        elif risk_score < 60:
            return RiskLevel.MODERATE
        elif risk_score < 80:
            return RiskLevel.HIGH
        else:
            return RiskLevel.VERY_HIGH
    
    def _generate_warnings(self, risk_factors: Dict[str, float], overall_risk: float) -> List[str]:
        """Generate risk warnings"""
        warnings = []
        
        if overall_risk > 80:
            warnings.append("VERY HIGH RISK: Consider avoiding this trade")
        
        if risk_factors.get('volatility_risk', 0) > 70:
            warnings.append("High volatility detected - use smaller position size")
        
        if risk_factors.get('liquidity_risk', 0) > 60:
            warnings.append("Low liquidity - may have difficulty exiting position")
        
        if risk_factors.get('concentration_risk', 0) > 60:
            warnings.append("High concentration risk - already significant exposure to this symbol")
        
        if risk_factors.get('correlation_risk', 0) > 70:
            warnings.append("High correlation with existing positions - limited diversification")
        
        return warnings
    
    def _generate_recommendations(self, risk_factors: Dict[str, float], signal: Any) -> List[str]:
        """Generate risk management recommendations"""
        recommendations = []
        
        if risk_factors.get('volatility_risk', 0) > 50:
            recommendations.append("Use tighter stop losses due to high volatility")
        
        if risk_factors.get('technical_risk', 0) > 60:
            recommendations.append("Wait for better technical setup")
        
        if risk_factors.get('market_regime_risk', 0) > 60:
            recommendations.append("Consider reducing position size due to market conditions")
        
        recommendations.append("Monitor position closely for first 24 hours")
        recommendations.append("Consider scaling into position over multiple entries")
        
        return recommendations
    
    def _calculate_confidence(self, risk_factors: Dict[str, float]) -> float:
        """Calculate confidence in risk assessment"""
        # Higher confidence when risk factors are consistent
        risk_values = list(risk_factors.values())
        std_dev = np.std(risk_values)
        
        # Lower standard deviation = higher confidence
        confidence = max(0.5, 1 - (std_dev / 50))
        return confidence * 100
    
    def _high_risk_assessment(self, reason: str) -> RiskAssessment:
        """Return high-risk assessment for error cases"""
        return RiskAssessment(
            overall_risk_score=90.0,
            risk_level=RiskLevel.VERY_HIGH,
            risk_factors={'error': 90.0},
            recommended_position_size=0.01,
            max_position_size=0.02,
            stop_loss_price=None,
            risk_reward_ratio=1.0,
            confidence=20.0,
            warnings=[f"Risk assessment failed: {reason}"],
            recommendations=["Avoid this trade due to assessment error"]
        )
    
    # Helper methods for technical calculations
    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """Calculate RSI"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))
    
    def _calculate_bb_position(self, data: pd.DataFrame, period: int = 20) -> pd.Series:
        """Calculate position within Bollinger Bands (0-1)"""
        sma = data['close'].rolling(window=period).mean()
        std = data['close'].rolling(window=period).std()
        upper = sma + (2 * std)
        lower = sma - (2 * std)
        return (data['close'] - lower) / (upper - lower)
    
    def _calculate_atr(self, data: pd.DataFrame, period: int = 14) -> float:
        """Calculate Average True Range"""
        high_low = data['high'] - data['low']
        high_close = np.abs(data['high'] - data['close'].shift())
        low_close = np.abs(data['low'] - data['close'].shift())
        
        true_range = np.maximum(high_low, np.maximum(high_close, low_close))
        return true_range.rolling(window=period).mean().iloc[-1]
    
    async def _get_portfolio_data(self) -> Dict[str, pd.DataFrame]:
        """Get historical data for current portfolio holdings"""
        try:
            positions = await self.trading_manager.get_positions()
            portfolio_data = {}
            
            for pos in positions:
                symbol = pos.get('symbol')
                if symbol:
                    data = self.data_manager.get_historical_data(symbol, '1Day', 60)
                    if data is not None and len(data) > 20:
                        portfolio_data[symbol] = data
            
            return portfolio_data
            
        except Exception as e:
            logger.error(f"Error getting portfolio data: {e}")
            return {}
