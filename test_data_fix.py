#!/usr/bin/env python3
"""
Test script to verify the Alpaca data fix is working
"""

import sys
from pathlib import Path

# Add src directory to Python path
sys.path.append(str(Path(__file__).parent / 'src'))

from src.data.data_manager import DataManager
from src.utils.logger import setup_logging

def test_data_retrieval():
    """Test that data retrieval is working after the fix"""
    print("🔧 Testing Data Retrieval Fix")
    print("=" * 50)
    
    # Setup logging
    setup_logging()
    
    # Initialize data manager
    data_manager = DataManager()
    
    # Test symbols
    test_symbols = ['AAPL', 'MSFT', 'TSLA']
    timeframes = ['15Min', '5Min', '30Min']
    
    success_count = 0
    total_tests = len(test_symbols) * len(timeframes)
    
    for symbol in test_symbols:
        print(f"\n📊 Testing {symbol}:")
        
        for timeframe in timeframes:
            try:
                print(f"  {timeframe}: ", end="")
                data = data_manager.get_historical_data(symbol, timeframe, periods=50)
                
                if data is not None and len(data) > 0:
                    print(f"✅ {len(data)} bars retrieved")
                    success_count += 1
                else:
                    print("❌ No data")
                    
            except Exception as e:
                print(f"❌ Error: {e}")
    
    print(f"\n🎯 Results: {success_count}/{total_tests} tests passed")
    
    if success_count == total_tests:
        print("🎉 All data retrieval tests passed! The fix is working.")
    elif success_count > 0:
        print("⚠️ Partial success - some data sources working")
    else:
        print("❌ Data retrieval still failing")
    
    return success_count == total_tests

if __name__ == "__main__":
    test_data_retrieval()
