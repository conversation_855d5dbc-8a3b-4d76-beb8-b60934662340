"""
Risk Management System for TTM Squeeze Trading System
"""
import logging
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import math

from config import Config

logger = logging.getLogger(__name__)

class RiskLevel(Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

@dataclass
class PositionRisk:
    """Position risk assessment"""
    symbol: str
    current_price: float
    entry_price: float
    quantity: int
    stop_loss: Optional[float]
    take_profit: Optional[float]
    unrealized_pnl: float
    unrealized_pnl_pct: float
    risk_amount: float
    risk_level: RiskLevel
    days_held: int
    position_size_pct: float

@dataclass
class PortfolioRisk:
    """Portfolio risk assessment"""
    total_value: float
    total_risk: float
    risk_percentage: float
    max_risk_percentage: float
    position_count: int
    max_positions: int
    largest_position_pct: float
    risk_level: RiskLevel
    recommendations: List[str]

@dataclass
class TradeRisk:
    """Trade risk assessment"""
    symbol: str
    entry_price: float
    stop_loss: float
    take_profit: Optional[float]
    quantity: int
    risk_amount: float
    reward_amount: float
    risk_reward_ratio: float
    position_size_pct: float
    is_acceptable: bool
    warnings: List[str]

class RiskManager:
    """Risk management system"""
    
    def __init__(self, trader=None):
        """Initialize risk manager"""
        self.trader = trader
        self.config = Config.RISK_MANAGEMENT
        
        # Risk limits
        self.max_position_size = self.config['max_position_size']
        self.max_portfolio_risk = self.config['max_portfolio_risk']
        self.stop_loss_pct = self.config['stop_loss_pct']
        self.take_profit_pct = self.config['take_profit_pct']
        self.max_positions = self.config['max_positions']
        
        logger.info("Risk manager initialized")
    
    def assess_trade_risk(self, symbol: str, entry_price: float, 
                         stop_loss: float, quantity: int,
                         take_profit: Optional[float] = None) -> TradeRisk:
        """Assess risk for a potential trade"""
        
        warnings = []
        
        # Calculate risk amount
        risk_per_share = abs(entry_price - stop_loss)
        risk_amount = risk_per_share * quantity
        
        # Calculate reward amount
        if take_profit:
            reward_per_share = abs(take_profit - entry_price)
            reward_amount = reward_per_share * quantity
            risk_reward_ratio = reward_amount / risk_amount if risk_amount > 0 else 0
        else:
            reward_amount = 0
            risk_reward_ratio = 0
        
        # Get portfolio value
        portfolio_value = self._get_portfolio_value()
        position_value = entry_price * quantity
        position_size_pct = position_value / portfolio_value if portfolio_value > 0 else 0
        
        # Check position size
        if position_size_pct > self.max_position_size:
            warnings.append(f"Position size ({position_size_pct:.1%}) exceeds maximum ({self.max_position_size:.1%})")
        
        # Check risk/reward ratio
        if risk_reward_ratio > 0 and risk_reward_ratio < 1.5:
            warnings.append(f"Risk/reward ratio ({risk_reward_ratio:.2f}) is below recommended 1.5:1")
        
        # Check stop loss distance
        stop_loss_pct = abs(entry_price - stop_loss) / entry_price
        if stop_loss_pct > 0.05:  # 5%
            warnings.append(f"Stop loss ({stop_loss_pct:.1%}) is quite wide")
        
        # Check portfolio risk
        current_portfolio_risk = self._calculate_current_portfolio_risk()
        new_portfolio_risk = (current_portfolio_risk + risk_amount) / portfolio_value if portfolio_value > 0 else 0
        
        if new_portfolio_risk > self.max_portfolio_risk:
            warnings.append(f"Trade would exceed maximum portfolio risk ({self.max_portfolio_risk:.1%})")
        
        # Check position count
        current_positions = self._get_position_count()
        if current_positions >= self.max_positions:
            warnings.append(f"Maximum number of positions ({self.max_positions}) reached")
        
        # Determine if trade is acceptable
        is_acceptable = (
            position_size_pct <= self.max_position_size and
            new_portfolio_risk <= self.max_portfolio_risk and
            current_positions < self.max_positions and
            risk_amount > 0
        )
        
        return TradeRisk(
            symbol=symbol,
            entry_price=entry_price,
            stop_loss=stop_loss,
            take_profit=take_profit,
            quantity=quantity,
            risk_amount=risk_amount,
            reward_amount=reward_amount,
            risk_reward_ratio=risk_reward_ratio,
            position_size_pct=position_size_pct,
            is_acceptable=is_acceptable,
            warnings=warnings
        )
    
    def calculate_position_size(self, symbol: str, entry_price: float, 
                              stop_loss: float, risk_amount: Optional[float] = None) -> int:
        """Calculate optimal position size based on risk"""
        
        portfolio_value = self._get_portfolio_value()
        
        if risk_amount is None:
            # Use percentage of portfolio
            risk_amount = portfolio_value * self.max_position_size
        
        # Calculate risk per share
        risk_per_share = abs(entry_price - stop_loss)
        
        if risk_per_share <= 0:
            return 0
        
        # Calculate position size based on risk
        position_size = int(risk_amount / risk_per_share)
        
        # Ensure position doesn't exceed maximum position value
        max_position_value = portfolio_value * self.max_position_size
        max_shares_by_value = int(max_position_value / entry_price)
        
        position_size = min(position_size, max_shares_by_value)
        
        # Ensure we don't exceed buying power
        if self.trader:
            account_info = self.trader.get_account_info()
            buying_power = account_info.get('buying_power', 0)
            max_shares_by_buying_power = int(buying_power / entry_price)
            position_size = min(position_size, max_shares_by_buying_power)
        
        return max(position_size, 0)
    
    def assess_position_risk(self, symbol: str, current_price: float) -> Optional[PositionRisk]:
        """Assess risk for an existing position"""

        if not self.trader:
            return None

        # Get position information
        positions = self.trader.get_positions()
        position = next((p for p in positions if p['symbol'] == symbol), None)

        if not position:
            return None

        quantity = float(position['quantity'])  # Handle fractional shares
        entry_price = position['avg_entry_price']
        unrealized_pnl = position['unrealized_pl']
        unrealized_pnl_pct = position['unrealized_plpc']
        
        # Calculate position metrics
        portfolio_value = self._get_portfolio_value()
        position_value = abs(current_price * quantity)
        position_size_pct = position_value / portfolio_value if portfolio_value > 0 else 0
        
        # Estimate stop loss and take profit (if not available, use defaults)
        stop_loss = self._estimate_stop_loss(entry_price, quantity > 0)
        take_profit = self._estimate_take_profit(entry_price, quantity > 0)
        
        # Calculate risk amount
        risk_amount = abs((current_price - stop_loss) * quantity)
        
        # Determine risk level
        risk_level = self._determine_position_risk_level(unrealized_pnl_pct, position_size_pct)
        
        # Calculate days held (simplified - would need actual entry date)
        days_held = 1  # Placeholder
        
        return PositionRisk(
            symbol=symbol,
            current_price=current_price,
            entry_price=entry_price,
            quantity=quantity,
            stop_loss=stop_loss,
            take_profit=take_profit,
            unrealized_pnl=unrealized_pnl,
            unrealized_pnl_pct=unrealized_pnl_pct,
            risk_amount=risk_amount,
            risk_level=risk_level,
            days_held=days_held,
            position_size_pct=position_size_pct
        )
    
    def assess_portfolio_risk(self) -> PortfolioRisk:
        """Assess overall portfolio risk"""
        
        portfolio_value = self._get_portfolio_value()
        total_risk = self._calculate_current_portfolio_risk()
        risk_percentage = total_risk / portfolio_value if portfolio_value > 0 else 0
        
        # Get position information
        position_count = self._get_position_count()
        largest_position_pct = self._get_largest_position_percentage()
        
        # Determine risk level
        risk_level = self._determine_portfolio_risk_level(risk_percentage, position_count, largest_position_pct)
        
        # Generate recommendations
        recommendations = self._generate_portfolio_recommendations(
            risk_percentage, position_count, largest_position_pct
        )
        
        return PortfolioRisk(
            total_value=portfolio_value,
            total_risk=total_risk,
            risk_percentage=risk_percentage,
            max_risk_percentage=self.max_portfolio_risk,
            position_count=position_count,
            max_positions=self.max_positions,
            largest_position_pct=largest_position_pct,
            risk_level=risk_level,
            recommendations=recommendations
        )
    
    def should_close_position(self, symbol: str, current_price: float) -> Tuple[bool, str]:
        """Determine if a position should be closed"""
        
        position_risk = self.assess_position_risk(symbol, current_price)
        
        if not position_risk:
            return False, "No position found"
        
        # Check stop loss
        if position_risk.quantity > 0:  # Long position
            if current_price <= position_risk.stop_loss:
                return True, "Stop loss triggered"
        else:  # Short position
            if current_price >= position_risk.stop_loss:
                return True, "Stop loss triggered"
        
        # Check take profit
        if position_risk.take_profit:
            if position_risk.quantity > 0:  # Long position
                if current_price >= position_risk.take_profit:
                    return True, "Take profit target reached"
            else:  # Short position
                if current_price <= position_risk.take_profit:
                    return True, "Take profit target reached"
        
        # Check risk level
        if position_risk.risk_level == RiskLevel.CRITICAL:
            return True, "Critical risk level reached"
        
        # Check unrealized loss
        if position_risk.unrealized_pnl_pct < -0.10:  # 10% loss
            return True, "Large unrealized loss"
        
        return False, "Position within acceptable risk"
    
    def _get_portfolio_value(self) -> float:
        """Get current portfolio value"""
        if self.trader:
            account_info = self.trader.get_account_info()
            return account_info.get('portfolio_value', 0)
        return 100000  # Default for testing
    
    def _get_position_count(self) -> int:
        """Get current number of positions"""
        if self.trader:
            positions = self.trader.get_positions()
            return len(positions)
        return 0
    
    def _calculate_current_portfolio_risk(self) -> float:
        """Calculate current portfolio risk amount"""
        if not self.trader:
            return 0
        
        total_risk = 0
        positions = self.trader.get_positions()
        
        for position in positions:
            # Estimate risk based on position size and typical stop loss
            position_value = abs(position['market_value'])
            estimated_risk = position_value * self.stop_loss_pct
            total_risk += estimated_risk
        
        return total_risk
    
    def _get_largest_position_percentage(self) -> float:
        """Get the percentage of the largest position"""
        if not self.trader:
            return 0
        
        portfolio_value = self._get_portfolio_value()
        positions = self.trader.get_positions()
        
        if not positions or portfolio_value <= 0:
            return 0
        
        largest_position = max(positions, key=lambda p: abs(p['market_value']))
        return abs(largest_position['market_value']) / portfolio_value
    
    def _estimate_stop_loss(self, entry_price: float, is_long: bool) -> float:
        """Estimate stop loss price"""
        if is_long:
            return entry_price * (1 - self.stop_loss_pct)
        else:
            return entry_price * (1 + self.stop_loss_pct)
    
    def _estimate_take_profit(self, entry_price: float, is_long: bool) -> float:
        """Estimate take profit price"""
        if is_long:
            return entry_price * (1 + self.take_profit_pct)
        else:
            return entry_price * (1 - self.take_profit_pct)
    
    def _determine_position_risk_level(self, unrealized_pnl_pct: float, position_size_pct: float) -> RiskLevel:
        """Determine risk level for a position"""
        
        if unrealized_pnl_pct < -0.08 or position_size_pct > self.max_position_size * 1.5:
            return RiskLevel.CRITICAL
        elif unrealized_pnl_pct < -0.05 or position_size_pct > self.max_position_size * 1.2:
            return RiskLevel.HIGH
        elif unrealized_pnl_pct < -0.02 or position_size_pct > self.max_position_size:
            return RiskLevel.MEDIUM
        else:
            return RiskLevel.LOW
    
    def _determine_portfolio_risk_level(self, risk_percentage: float, position_count: int, 
                                      largest_position_pct: float) -> RiskLevel:
        """Determine risk level for portfolio"""
        
        if (risk_percentage > self.max_portfolio_risk * 1.5 or 
            position_count > self.max_positions or
            largest_position_pct > self.max_position_size * 2):
            return RiskLevel.CRITICAL
        elif (risk_percentage > self.max_portfolio_risk * 1.2 or
              position_count > self.max_positions * 0.8 or
              largest_position_pct > self.max_position_size * 1.5):
            return RiskLevel.HIGH
        elif (risk_percentage > self.max_portfolio_risk * 0.8 or
              largest_position_pct > self.max_position_size):
            return RiskLevel.MEDIUM
        else:
            return RiskLevel.LOW
    
    def _generate_portfolio_recommendations(self, risk_percentage: float, position_count: int,
                                          largest_position_pct: float) -> List[str]:
        """Generate portfolio risk recommendations"""
        
        recommendations = []
        
        if risk_percentage > self.max_portfolio_risk:
            recommendations.append("Reduce portfolio risk by closing some positions")
        
        if position_count >= self.max_positions:
            recommendations.append("Maximum positions reached - consider closing before opening new ones")
        
        if largest_position_pct > self.max_position_size * 1.5:
            recommendations.append("Largest position is too large - consider reducing size")
        
        if risk_percentage < self.max_portfolio_risk * 0.3:
            recommendations.append("Portfolio risk is very low - consider increasing position sizes")
        
        return recommendations
