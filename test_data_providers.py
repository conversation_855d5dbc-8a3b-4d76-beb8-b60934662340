#!/usr/bin/env python3
"""
Test script to debug data provider issues
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.data.alpaca_provider import AlpacaDataProvider
from src.data.fmp_provider import FMPDataProvider
from src.data.data_manager import DataManager
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_alpaca():
    """Test Alpaca data provider"""
    print("=" * 50)
    print("Testing Alpaca Data Provider")
    print("=" * 50)
    
    try:
        alpaca = AlpacaDataProvider()
        print(f"✓ Alpaca provider initialized")
        
        # Test getting data for AAPL
        print("Testing AAPL daily data...")
        data = alpaca.get_historical_data('AAPL', '1Day', periods=20)
        
        if data is not None:
            print(f"✓ Got {len(data)} periods for AAPL")
            print(f"Columns: {data.columns.tolist()}")
            print("Latest 3 rows:")
            print(data.tail(3))
        else:
            print("✗ No data returned for AAPL")
            
    except Exception as e:
        print(f"✗ Error testing Alpaca: {e}")

def test_fmp():
    """Test FMP data provider"""
    print("\n" + "=" * 50)
    print("Testing FMP Data Provider")
    print("=" * 50)
    
    try:
        fmp = FMPDataProvider()
        print(f"✓ FMP provider initialized")
        
        # Test getting data for AAPL
        print("Testing AAPL daily data...")
        data = fmp.get_historical_data('AAPL', '1Day', periods=20)
        
        if data is not None:
            print(f"✓ Got {len(data)} periods for AAPL")
            print(f"Columns: {data.columns.tolist()}")
            print("Latest 3 rows:")
            print(data.tail(3))
        else:
            print("✗ No data returned for AAPL")
            
    except Exception as e:
        print(f"✗ Error testing FMP: {e}")

def test_data_manager():
    """Test Data Manager"""
    print("\n" + "=" * 50)
    print("Testing Data Manager")
    print("=" * 50)
    
    try:
        dm = DataManager()
        print(f"✓ Data Manager initialized")
        
        # Test getting data for AAPL
        print("Testing AAPL daily data...")
        data = dm.get_historical_data('AAPL', '1Day', periods=20)
        
        if data is not None:
            print(f"✓ Got {len(data)} periods for AAPL")
            print(f"Columns: {data.columns.tolist()}")
            print("Latest 3 rows:")
            print(data.tail(3))
        else:
            print("✗ No data returned for AAPL")
            
    except Exception as e:
        print(f"✗ Error testing Data Manager: {e}")

if __name__ == "__main__":
    test_alpaca()
    test_fmp()
    test_data_manager()
    print("\n" + "=" * 50)
    print("Data Provider Testing Complete")
    print("=" * 50)
