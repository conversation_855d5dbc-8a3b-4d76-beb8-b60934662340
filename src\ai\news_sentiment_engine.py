"""
News Sentiment Analysis Engine for TTM Squeeze Trading System
Real-time news intelligence with FinBERT sentiment analysis and economic event impact prediction
"""

import asyncio
import aiohttp
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
import logging
import re
import json
from collections import defaultdict

# NLP and sentiment analysis imports
try:
    from transformers import pipeline, AutoTokenizer, AutoModelForSequenceClassification
    import torch
    NLP_AVAILABLE = True
except ImportError:
    NLP_AVAILABLE = False

# News API imports
try:
    import feedparser
    import requests
    NEWS_APIS_AVAILABLE = True
except ImportError:
    NEWS_APIS_AVAILABLE = False

from config import Config

logger = logging.getLogger(__name__)

@dataclass
class NewsArticle:
    """Individual news article data"""
    title: str
    content: str
    source: str
    published_at: datetime
    url: str
    symbols_mentioned: List[str]
    sentiment_score: float
    sentiment_label: str
    confidence: float
    relevance_score: float

@dataclass
class SentimentAnalysis:
    """Sentiment analysis result for a symbol"""
    symbol: str
    overall_sentiment: float  # -1 to 1
    sentiment_label: str  # 'positive', 'negative', 'neutral'
    confidence: float
    news_volume: int
    key_themes: List[str]
    recent_articles: List[NewsArticle]
    sentiment_trend: str  # 'improving', 'declining', 'stable'
    impact_score: float  # 0-100

@dataclass
class EconomicEvent:
    """Economic event data"""
    name: str
    date: datetime
    importance: str  # 'low', 'medium', 'high'
    previous_value: Optional[float]
    forecast_value: Optional[float]
    actual_value: Optional[float]
    currency: str
    impact_prediction: str
    ai_impact_score: float

class FinBERTSentimentAnalyzer:
    """FinBERT-based sentiment analysis for financial news"""
    
    def __init__(self):
        self.model = None
        self.tokenizer = None
        self.sentiment_pipeline = None
        self.is_initialized = False
        
        if NLP_AVAILABLE:
            self._initialize_model()
    
    def _initialize_model(self):
        """Initialize FinBERT model for financial sentiment analysis"""
        try:
            # Use FinBERT model specifically trained on financial data
            model_name = "ProsusAI/finbert"
            
            self.tokenizer = AutoTokenizer.from_pretrained(model_name)
            self.model = AutoModelForSequenceClassification.from_pretrained(model_name)
            
            # Create sentiment pipeline
            self.sentiment_pipeline = pipeline(
                "sentiment-analysis",
                model=self.model,
                tokenizer=self.tokenizer,
                device=0 if torch.cuda.is_available() else -1
            )
            
            self.is_initialized = True
            logger.info("FinBERT sentiment analyzer initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing FinBERT model: {e}")
            # Fallback to basic sentiment analysis
            try:
                self.sentiment_pipeline = pipeline("sentiment-analysis")
                self.is_initialized = True
                logger.info("Fallback sentiment analyzer initialized")
            except Exception as e2:
                logger.error(f"Error initializing fallback sentiment analyzer: {e2}")
    
    def analyze_sentiment(self, text: str) -> Tuple[float, str, float]:
        """
        Analyze sentiment of financial text
        
        Returns:
            Tuple of (sentiment_score, sentiment_label, confidence)
        """
        if not self.is_initialized:
            return 0.0, 'neutral', 0.5
        
        try:
            # Clean and truncate text
            cleaned_text = self._clean_text(text)
            if len(cleaned_text) > 512:
                cleaned_text = cleaned_text[:512]
            
            # Get sentiment prediction
            result = self.sentiment_pipeline(cleaned_text)[0]
            
            # Convert to standardized format
            label = result['label'].lower()
            confidence = result['score']
            
            # Map labels to sentiment scores
            if 'positive' in label or 'bullish' in label:
                sentiment_score = confidence
                sentiment_label = 'positive'
            elif 'negative' in label or 'bearish' in label:
                sentiment_score = -confidence
                sentiment_label = 'negative'
            else:
                sentiment_score = 0.0
                sentiment_label = 'neutral'
            
            return sentiment_score, sentiment_label, confidence
            
        except Exception as e:
            logger.error(f"Error analyzing sentiment: {e}")
            return 0.0, 'neutral', 0.5
    
    def _clean_text(self, text: str) -> str:
        """Clean text for sentiment analysis"""
        # Remove URLs
        text = re.sub(r'http\S+', '', text)
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text)
        # Remove special characters but keep financial symbols
        text = re.sub(r'[^\w\s$%\-\.]', ' ', text)
        return text.strip()

class NewsDataProvider:
    """Fetch news data from multiple sources"""
    
    def __init__(self):
        self.news_sources = {
            'reuters': 'https://feeds.reuters.com/reuters/businessNews',
            'marketwatch': 'https://feeds.marketwatch.com/marketwatch/marketpulse/',
            'cnbc': 'https://search.cnbc.com/rs/search/combinedcms/view.xml?partnerId=wrss01&id=15839069',
            'bloomberg': 'https://feeds.bloomberg.com/markets/news.rss'
        }
        
        # Financial news APIs (if available)
        self.api_sources = {}
        if hasattr(Config, 'NEWS_API_KEY'):
            self.api_sources['newsapi'] = Config.NEWS_API_KEY
        if hasattr(Config, 'ALPHA_VANTAGE_KEY'):
            self.api_sources['alpha_vantage'] = Config.ALPHA_VANTAGE_KEY
    
    async def fetch_news_for_symbols(self, symbols: List[str], hours_back: int = 24) -> Dict[str, List[NewsArticle]]:
        """Fetch news articles for specific symbols"""
        news_by_symbol = defaultdict(list)
        
        try:
            # Fetch from RSS feeds
            rss_news = await self._fetch_rss_news(symbols, hours_back)
            for symbol, articles in rss_news.items():
                news_by_symbol[symbol].extend(articles)
            
            # Fetch from API sources
            api_news = await self._fetch_api_news(symbols, hours_back)
            for symbol, articles in api_news.items():
                news_by_symbol[symbol].extend(articles)
            
            # Remove duplicates and sort by relevance
            for symbol in news_by_symbol:
                news_by_symbol[symbol] = self._deduplicate_articles(news_by_symbol[symbol])
                news_by_symbol[symbol].sort(key=lambda x: x.relevance_score, reverse=True)
            
            return dict(news_by_symbol)
            
        except Exception as e:
            logger.error(f"Error fetching news: {e}")
            return {}
    
    async def _fetch_rss_news(self, symbols: List[str], hours_back: int) -> Dict[str, List[NewsArticle]]:
        """Fetch news from RSS feeds"""
        news_by_symbol = defaultdict(list)
        cutoff_time = datetime.now() - timedelta(hours=hours_back)
        
        try:
            async with aiohttp.ClientSession() as session:
                for source_name, rss_url in self.news_sources.items():
                    try:
                        async with session.get(rss_url, timeout=10) as response:
                            if response.status == 200:
                                content = await response.text()
                                feed = feedparser.parse(content)
                                
                                for entry in feed.entries:
                                    # Parse publication date
                                    pub_date = self._parse_date(entry.get('published', ''))
                                    if pub_date < cutoff_time:
                                        continue
                                    
                                    # Extract article content
                                    title = entry.get('title', '')
                                    content = entry.get('summary', '') or entry.get('description', '')
                                    url = entry.get('link', '')
                                    
                                    # Find mentioned symbols
                                    mentioned_symbols = self._extract_symbols(title + ' ' + content, symbols)
                                    
                                    if mentioned_symbols:
                                        article = NewsArticle(
                                            title=title,
                                            content=content,
                                            source=source_name,
                                            published_at=pub_date,
                                            url=url,
                                            symbols_mentioned=mentioned_symbols,
                                            sentiment_score=0.0,  # To be filled by sentiment analyzer
                                            sentiment_label='neutral',
                                            confidence=0.0,
                                            relevance_score=self._calculate_relevance(title, content, mentioned_symbols)
                                        )
                                        
                                        for symbol in mentioned_symbols:
                                            news_by_symbol[symbol].append(article)
                    
                    except Exception as e:
                        logger.warning(f"Error fetching from {source_name}: {e}")
                        continue
            
            return dict(news_by_symbol)
            
        except Exception as e:
            logger.error(f"Error in RSS news fetch: {e}")
            return {}
    
    async def _fetch_api_news(self, symbols: List[str], hours_back: int) -> Dict[str, List[NewsArticle]]:
        """Fetch news from API sources"""
        news_by_symbol = defaultdict(list)
        
        # Implement API-specific news fetching
        # This would include NewsAPI, Alpha Vantage, etc.
        # For now, return empty dict as placeholder
        
        return dict(news_by_symbol)
    
    def _extract_symbols(self, text: str, target_symbols: List[str]) -> List[str]:
        """Extract mentioned stock symbols from text"""
        mentioned = []
        text_upper = text.upper()
        
        for symbol in target_symbols:
            # Look for symbol mentions with various patterns
            patterns = [
                rf'\b{symbol}\b',  # Exact match
                rf'\${symbol}\b',  # With dollar sign
                rf'\b{symbol}\.', # With period (for exchanges)
            ]
            
            for pattern in patterns:
                if re.search(pattern, text_upper):
                    mentioned.append(symbol)
                    break
        
        return mentioned
    
    def _calculate_relevance(self, title: str, content: str, symbols: List[str]) -> float:
        """Calculate relevance score for an article"""
        score = 0.0
        
        # Title mentions are more important
        for symbol in symbols:
            if symbol.upper() in title.upper():
                score += 0.5
            if symbol.upper() in content.upper():
                score += 0.3
        
        # Financial keywords boost relevance
        financial_keywords = [
            'earnings', 'revenue', 'profit', 'loss', 'guidance', 'outlook',
            'merger', 'acquisition', 'partnership', 'contract', 'deal',
            'upgrade', 'downgrade', 'target', 'price', 'analyst'
        ]
        
        text_lower = (title + ' ' + content).lower()
        for keyword in financial_keywords:
            if keyword in text_lower:
                score += 0.1
        
        return min(1.0, score)
    
    def _parse_date(self, date_str: str) -> datetime:
        """Parse various date formats"""
        try:
            # Try common RSS date formats
            from dateutil import parser
            return parser.parse(date_str)
        except:
            return datetime.now() - timedelta(days=1)  # Default to yesterday
    
    def _deduplicate_articles(self, articles: List[NewsArticle]) -> List[NewsArticle]:
        """Remove duplicate articles based on title similarity"""
        unique_articles = []
        seen_titles = set()
        
        for article in articles:
            # Simple deduplication based on title
            title_key = article.title.lower().strip()
            if title_key not in seen_titles:
                seen_titles.add(title_key)
                unique_articles.append(article)
        
        return unique_articles

class EconomicEventAnalyzer:
    """Analyze economic events and their market impact"""
    
    def __init__(self):
        self.event_sources = {
            'economic_calendar': 'https://api.example.com/economic-calendar',  # Placeholder
        }
    
    async def get_upcoming_events(self, days_ahead: int = 7) -> List[EconomicEvent]:
        """Get upcoming economic events"""
        try:
            # This would fetch from economic calendar APIs
            # For now, return sample events
            sample_events = [
                EconomicEvent(
                    name="Federal Reserve Interest Rate Decision",
                    date=datetime.now() + timedelta(days=2),
                    importance="high",
                    previous_value=5.25,
                    forecast_value=5.50,
                    actual_value=None,
                    currency="USD",
                    impact_prediction="Market volatility expected",
                    ai_impact_score=85.0
                ),
                EconomicEvent(
                    name="Non-Farm Payrolls",
                    date=datetime.now() + timedelta(days=5),
                    importance="high",
                    previous_value=150000,
                    forecast_value=180000,
                    actual_value=None,
                    currency="USD",
                    impact_prediction="Strong employment data could boost markets",
                    ai_impact_score=75.0
                )
            ]
            
            return sample_events
            
        except Exception as e:
            logger.error(f"Error fetching economic events: {e}")
            return []
    
    async def predict_event_impact(self, event: EconomicEvent) -> str:
        """Predict market impact of economic event using AI"""
        try:
            # This would use ChatGPT or similar to analyze event impact
            # For now, return basic analysis
            
            if event.importance == "high":
                if "interest rate" in event.name.lower():
                    return "High volatility expected across all sectors. TTM Squeeze signals may be less reliable during announcement."
                elif "employment" in event.name.lower() or "payroll" in event.name.lower():
                    return "Employment data affects market sentiment. Strong data typically bullish for equities."
                elif "inflation" in event.name.lower() or "cpi" in event.name.lower():
                    return "Inflation data impacts Fed policy expectations. Higher than expected inflation typically bearish."
            
            return "Moderate market impact expected. Monitor for increased volatility."
            
        except Exception as e:
            logger.error(f"Error predicting event impact: {e}")
            return "Unable to predict impact"

class NewsIntelligenceEngine:
    """Main news intelligence engine combining sentiment analysis and economic events"""
    
    def __init__(self):
        self.sentiment_analyzer = FinBERTSentimentAnalyzer()
        self.news_provider = NewsDataProvider()
        self.economic_analyzer = EconomicEventAnalyzer()
        self.cache = {}
        self.cache_expiry = timedelta(minutes=30)
    
    async def analyze_symbol_sentiment(self, symbol: str, hours_back: int = 24) -> SentimentAnalysis:
        """Comprehensive sentiment analysis for a symbol"""
        try:
            # Check cache
            cache_key = f"{symbol}_{hours_back}"
            if cache_key in self.cache:
                cached_result, cached_time = self.cache[cache_key]
                if datetime.now() - cached_time < self.cache_expiry:
                    return cached_result
            
            # Fetch news articles
            news_data = await self.news_provider.fetch_news_for_symbols([symbol], hours_back)
            articles = news_data.get(symbol, [])
            
            if not articles:
                return self._create_neutral_sentiment(symbol)
            
            # Analyze sentiment for each article
            sentiment_scores = []
            analyzed_articles = []
            
            for article in articles:
                sentiment_score, sentiment_label, confidence = self.sentiment_analyzer.analyze_sentiment(
                    article.title + ' ' + article.content
                )
                
                # Update article with sentiment
                article.sentiment_score = sentiment_score
                article.sentiment_label = sentiment_label
                article.confidence = confidence
                
                sentiment_scores.append(sentiment_score * article.relevance_score)
                analyzed_articles.append(article)
            
            # Calculate overall sentiment
            if sentiment_scores:
                overall_sentiment = np.mean(sentiment_scores)
                confidence = np.mean([a.confidence for a in analyzed_articles])
            else:
                overall_sentiment = 0.0
                confidence = 0.5
            
            # Determine sentiment label
            if overall_sentiment > 0.1:
                sentiment_label = 'positive'
            elif overall_sentiment < -0.1:
                sentiment_label = 'negative'
            else:
                sentiment_label = 'neutral'
            
            # Extract key themes
            key_themes = self._extract_themes(analyzed_articles)
            
            # Determine sentiment trend
            sentiment_trend = self._analyze_sentiment_trend(analyzed_articles)
            
            # Calculate impact score
            impact_score = self._calculate_impact_score(
                overall_sentiment, len(articles), confidence
            )
            
            result = SentimentAnalysis(
                symbol=symbol,
                overall_sentiment=overall_sentiment,
                sentiment_label=sentiment_label,
                confidence=confidence,
                news_volume=len(articles),
                key_themes=key_themes,
                recent_articles=analyzed_articles[:10],  # Top 10 most relevant
                sentiment_trend=sentiment_trend,
                impact_score=impact_score
            )
            
            # Cache result
            self.cache[cache_key] = (result, datetime.now())
            
            return result
            
        except Exception as e:
            logger.error(f"Error analyzing sentiment for {symbol}: {e}")
            return self._create_neutral_sentiment(symbol)
    
    async def analyze_market_sentiment(self, symbols: List[str]) -> Dict[str, SentimentAnalysis]:
        """Analyze sentiment for multiple symbols"""
        results = {}
        
        # Process symbols in batches to avoid overwhelming news APIs
        batch_size = 10
        for i in range(0, len(symbols), batch_size):
            batch = symbols[i:i + batch_size]
            
            # Analyze each symbol in the batch
            tasks = [self.analyze_symbol_sentiment(symbol) for symbol in batch]
            batch_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            for symbol, result in zip(batch, batch_results):
                if isinstance(result, Exception):
                    logger.error(f"Error analyzing {symbol}: {result}")
                    results[symbol] = self._create_neutral_sentiment(symbol)
                else:
                    results[symbol] = result
        
        return results
    
    def _create_neutral_sentiment(self, symbol: str) -> SentimentAnalysis:
        """Create neutral sentiment analysis for symbols with no news"""
        return SentimentAnalysis(
            symbol=symbol,
            overall_sentiment=0.0,
            sentiment_label='neutral',
            confidence=0.5,
            news_volume=0,
            key_themes=[],
            recent_articles=[],
            sentiment_trend='stable',
            impact_score=0.0
        )
    
    def _extract_themes(self, articles: List[NewsArticle]) -> List[str]:
        """Extract key themes from articles"""
        themes = defaultdict(int)
        
        # Common financial themes
        theme_keywords = {
            'earnings': ['earnings', 'revenue', 'profit', 'eps'],
            'guidance': ['guidance', 'outlook', 'forecast', 'expects'],
            'analyst': ['analyst', 'upgrade', 'downgrade', 'target', 'rating'],
            'merger': ['merger', 'acquisition', 'deal', 'buyout'],
            'partnership': ['partnership', 'agreement', 'contract', 'alliance'],
            'regulatory': ['regulatory', 'fda', 'approval', 'investigation'],
            'product': ['product', 'launch', 'release', 'innovation']
        }
        
        for article in articles:
            text = (article.title + ' ' + article.content).lower()
            for theme, keywords in theme_keywords.items():
                if any(keyword in text for keyword in keywords):
                    themes[theme] += 1
        
        # Return top themes
        return [theme for theme, count in sorted(themes.items(), key=lambda x: x[1], reverse=True)[:5]]
    
    def _analyze_sentiment_trend(self, articles: List[NewsArticle]) -> str:
        """Analyze sentiment trend over time"""
        if len(articles) < 3:
            return 'stable'
        
        # Sort by publication date
        sorted_articles = sorted(articles, key=lambda x: x.published_at)
        
        # Split into recent and older articles
        mid_point = len(sorted_articles) // 2
        older_sentiment = np.mean([a.sentiment_score for a in sorted_articles[:mid_point]])
        recent_sentiment = np.mean([a.sentiment_score for a in sorted_articles[mid_point:]])
        
        change = recent_sentiment - older_sentiment
        
        if change > 0.1:
            return 'improving'
        elif change < -0.1:
            return 'declining'
        else:
            return 'stable'
    
    def _calculate_impact_score(self, sentiment: float, volume: int, confidence: float) -> float:
        """Calculate overall impact score"""
        # Base score from sentiment strength
        sentiment_impact = abs(sentiment) * 50
        
        # Volume impact (more news = higher impact)
        volume_impact = min(30, volume * 3)
        
        # Confidence impact
        confidence_impact = confidence * 20
        
        total_impact = sentiment_impact + volume_impact + confidence_impact
        return min(100.0, total_impact)
