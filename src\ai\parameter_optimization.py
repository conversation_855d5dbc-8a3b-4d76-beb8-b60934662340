"""
Machine Learning Parameter Optimization for TTM Squeeze Trading System
Genetic Algorithm and Bayesian Optimization for adaptive parameter tuning
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any, Callable
from dataclasses import dataclass
from datetime import datetime, timedelta
import logging
import asyncio
import json
import random
from abc import ABC, abstractmethod

# Optimization libraries
try:
    from scipy.optimize import minimize
    from sklearn.gaussian_process import GaussianProcessRegressor
    from sklearn.gaussian_process.kernels import Matern
    import optuna
    OPTIMIZATION_AVAILABLE = True
except ImportError:
    OPTIMIZATION_AVAILABLE = False

logger = logging.getLogger(__name__)

@dataclass
class ParameterSpace:
    """Define parameter search space"""
    name: str
    min_value: float
    max_value: float
    param_type: str  # 'float', 'int', 'categorical'
    categories: Optional[List[Any]] = None
    current_value: Optional[float] = None

@dataclass
class OptimizationResult:
    """Result of parameter optimization"""
    best_parameters: Dict[str, float]
    best_score: float
    optimization_history: List[Dict[str, Any]]
    convergence_info: Dict[str, Any]
    total_evaluations: int
    optimization_time: float
    improvement_percentage: float

class PerformanceEvaluator:
    """Evaluate trading performance for parameter optimization"""
    
    def __init__(self, data_manager, ttm_squeeze_engine):
        self.data_manager = data_manager
        self.ttm_squeeze_engine = ttm_squeeze_engine
        self.evaluation_cache = {}
    
    async def evaluate_parameters(self, parameters: Dict[str, float], 
                                symbols: List[str], 
                                evaluation_period: int = 252) -> float:
        """
        Evaluate trading performance with given parameters
        
        Returns:
            Performance score (higher is better)
        """
        try:
            # Create cache key
            cache_key = self._create_cache_key(parameters, symbols, evaluation_period)
            if cache_key in self.evaluation_cache:
                return self.evaluation_cache[cache_key]
            
            # Apply parameters to TTM Squeeze engine
            self._apply_parameters(parameters)
            
            # Backtest performance
            performance_metrics = await self._backtest_performance(symbols, evaluation_period)
            
            # Calculate composite score
            score = self._calculate_performance_score(performance_metrics)
            
            # Cache result
            self.evaluation_cache[cache_key] = score
            
            return score
            
        except Exception as e:
            logger.error(f"Error evaluating parameters: {e}")
            return 0.0  # Return poor score for invalid parameters
    
    def _apply_parameters(self, parameters: Dict[str, float]):
        """Apply parameters to TTM Squeeze engine"""
        # Update Bollinger Bands parameters
        if 'bb_period' in parameters:
            self.ttm_squeeze_engine.bb_period = int(parameters['bb_period'])
        if 'bb_std_dev' in parameters:
            self.ttm_squeeze_engine.bb_std_dev = parameters['bb_std_dev']
        
        # Update Keltner Channel parameters
        if 'kc_period' in parameters:
            self.ttm_squeeze_engine.kc_period = int(parameters['kc_period'])
        if 'kc_atr_multiplier' in parameters:
            self.ttm_squeeze_engine.kc_atr_multiplier = parameters['kc_atr_multiplier']
        
        # Update momentum parameters
        if 'momentum_period' in parameters:
            self.ttm_squeeze_engine.momentum_period = int(parameters['momentum_period'])
        
        # Update signal thresholds
        if 'signal_threshold' in parameters:
            self.ttm_squeeze_engine.signal_threshold = parameters['signal_threshold']
    
    async def _backtest_performance(self, symbols: List[str], period: int) -> Dict[str, float]:
        """Backtest trading performance"""
        total_trades = 0
        winning_trades = 0
        total_return = 0.0
        max_drawdown = 0.0
        sharpe_ratio = 0.0
        
        returns = []
        
        for symbol in symbols[:20]:  # Limit to 20 symbols for speed
            try:
                # Get historical data
                data = self.data_manager.get_historical_data(symbol, '1Day', period + 50)
                if data is None or len(data) < 100:
                    continue
                
                # Calculate TTM Squeeze signals
                squeeze_data = self.ttm_squeeze_engine.calculate_squeeze(data)
                if squeeze_data is None:
                    continue
                
                # Simulate trades
                symbol_returns = self._simulate_trades(squeeze_data)
                returns.extend(symbol_returns)
                
                # Update metrics
                symbol_trades = len(symbol_returns)
                symbol_winners = sum(1 for r in symbol_returns if r > 0)
                
                total_trades += symbol_trades
                winning_trades += symbol_winners
                total_return += sum(symbol_returns)
                
            except Exception as e:
                logger.warning(f"Error backtesting {symbol}: {e}")
                continue
        
        # Calculate performance metrics
        if total_trades > 0:
            win_rate = winning_trades / total_trades
            avg_return = total_return / total_trades
            
            if len(returns) > 1:
                returns_array = np.array(returns)
                sharpe_ratio = np.mean(returns_array) / np.std(returns_array) if np.std(returns_array) > 0 else 0
                
                # Calculate max drawdown
                cumulative_returns = np.cumsum(returns_array)
                running_max = np.maximum.accumulate(cumulative_returns)
                drawdowns = cumulative_returns - running_max
                max_drawdown = np.min(drawdowns) if len(drawdowns) > 0 else 0
        else:
            win_rate = 0.0
            avg_return = 0.0
        
        return {
            'total_trades': total_trades,
            'win_rate': win_rate,
            'avg_return': avg_return,
            'total_return': total_return,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': abs(max_drawdown)
        }
    
    def _simulate_trades(self, squeeze_data: pd.DataFrame) -> List[float]:
        """Simulate trades based on TTM Squeeze signals"""
        returns = []
        position = None
        entry_price = None
        
        for i in range(1, len(squeeze_data)):
            current_row = squeeze_data.iloc[i]
            prev_row = squeeze_data.iloc[i-1]
            
            # Entry signal: squeeze release with positive momentum
            if (not current_row.get('is_squeeze', True) and 
                prev_row.get('is_squeeze', False) and
                current_row.get('momentum', 0) > 0):
                
                if position is None:  # Enter long position
                    position = 'long'
                    entry_price = current_row['close']
            
            # Exit signal: momentum turns negative or after 10 periods
            elif position == 'long':
                exit_conditions = [
                    current_row.get('momentum', 0) < 0,  # Momentum turns negative
                    i - entry_price > 10  # Maximum holding period
                ]
                
                if any(exit_conditions):
                    exit_price = current_row['close']
                    trade_return = (exit_price - entry_price) / entry_price
                    returns.append(trade_return)
                    position = None
                    entry_price = None
        
        return returns
    
    def _calculate_performance_score(self, metrics: Dict[str, float]) -> float:
        """Calculate composite performance score"""
        # Weights for different metrics
        weights = {
            'win_rate': 0.25,
            'avg_return': 0.30,
            'sharpe_ratio': 0.25,
            'trade_frequency': 0.10,
            'drawdown_penalty': 0.10
        }
        
        # Normalize metrics to 0-100 scale
        win_rate_score = metrics['win_rate'] * 100
        
        # Average return score (assume 1% is good)
        avg_return_score = min(100, max(0, metrics['avg_return'] * 100 / 0.01))
        
        # Sharpe ratio score (assume 1.0 is good)
        sharpe_score = min(100, max(0, metrics['sharpe_ratio'] * 100 / 1.0))
        
        # Trade frequency score (prefer reasonable number of trades)
        trade_freq = metrics['total_trades'] / 252  # Trades per year
        trade_freq_score = min(100, max(0, 100 - abs(trade_freq - 20) * 2))  # Optimal ~20 trades/year
        
        # Drawdown penalty
        drawdown_penalty = min(100, metrics['max_drawdown'] * 100 / 0.20)  # 20% max acceptable
        
        # Calculate weighted score
        score = (
            win_rate_score * weights['win_rate'] +
            avg_return_score * weights['avg_return'] +
            sharpe_score * weights['sharpe_ratio'] +
            trade_freq_score * weights['trade_frequency'] -
            drawdown_penalty * weights['drawdown_penalty']
        )
        
        return max(0, score)
    
    def _create_cache_key(self, parameters: Dict[str, float], symbols: List[str], period: int) -> str:
        """Create cache key for parameter evaluation"""
        param_str = "_".join([f"{k}:{v:.4f}" for k, v in sorted(parameters.items())])
        symbol_str = "_".join(sorted(symbols[:5]))  # Use first 5 symbols for key
        return f"{param_str}_{symbol_str}_{period}"

class GeneticAlgorithmOptimizer:
    """Genetic Algorithm for parameter optimization"""
    
    def __init__(self, parameter_space: List[ParameterSpace], evaluator: PerformanceEvaluator):
        self.parameter_space = parameter_space
        self.evaluator = evaluator
        self.population_size = 50
        self.generations = 30
        self.mutation_rate = 0.1
        self.crossover_rate = 0.8
        self.elite_size = 5
    
    async def optimize(self, symbols: List[str]) -> OptimizationResult:
        """Run genetic algorithm optimization"""
        start_time = datetime.now()
        
        # Initialize population
        population = self._initialize_population()
        fitness_history = []
        best_individual = None
        best_fitness = float('-inf')
        
        logger.info(f"Starting genetic algorithm optimization with {self.population_size} individuals for {self.generations} generations")
        
        for generation in range(self.generations):
            # Evaluate fitness
            fitness_scores = await self._evaluate_population(population, symbols)
            
            # Track best individual
            generation_best_idx = np.argmax(fitness_scores)
            generation_best_fitness = fitness_scores[generation_best_idx]
            
            if generation_best_fitness > best_fitness:
                best_fitness = generation_best_fitness
                best_individual = population[generation_best_idx].copy()
            
            fitness_history.append({
                'generation': generation,
                'best_fitness': generation_best_fitness,
                'avg_fitness': np.mean(fitness_scores),
                'std_fitness': np.std(fitness_scores)
            })
            
            logger.info(f"Generation {generation}: Best={generation_best_fitness:.2f}, Avg={np.mean(fitness_scores):.2f}")
            
            # Create next generation
            if generation < self.generations - 1:
                population = self._create_next_generation(population, fitness_scores)
        
        # Convert best individual to parameter dict
        best_parameters = self._individual_to_parameters(best_individual)
        
        optimization_time = (datetime.now() - start_time).total_seconds()
        
        return OptimizationResult(
            best_parameters=best_parameters,
            best_score=best_fitness,
            optimization_history=fitness_history,
            convergence_info={'converged': True, 'final_generation': self.generations},
            total_evaluations=self.population_size * self.generations,
            optimization_time=optimization_time,
            improvement_percentage=((best_fitness - 50) / 50) * 100  # Assume 50 is baseline
        )
    
    def _initialize_population(self) -> List[List[float]]:
        """Initialize random population"""
        population = []
        
        for _ in range(self.population_size):
            individual = []
            for param in self.parameter_space:
                if param.param_type == 'float':
                    value = random.uniform(param.min_value, param.max_value)
                elif param.param_type == 'int':
                    value = random.randint(int(param.min_value), int(param.max_value))
                else:  # categorical
                    value = random.choice(param.categories)
                
                individual.append(value)
            
            population.append(individual)
        
        return population
    
    async def _evaluate_population(self, population: List[List[float]], symbols: List[str]) -> List[float]:
        """Evaluate fitness of entire population"""
        fitness_scores = []
        
        # Evaluate individuals in batches to avoid overwhelming the system
        batch_size = 10
        for i in range(0, len(population), batch_size):
            batch = population[i:i + batch_size]
            batch_tasks = []
            
            for individual in batch:
                parameters = self._individual_to_parameters(individual)
                task = self.evaluator.evaluate_parameters(parameters, symbols)
                batch_tasks.append(task)
            
            batch_scores = await asyncio.gather(*batch_tasks)
            fitness_scores.extend(batch_scores)
        
        return fitness_scores
    
    def _create_next_generation(self, population: List[List[float]], fitness_scores: List[float]) -> List[List[float]]:
        """Create next generation using selection, crossover, and mutation"""
        # Sort population by fitness
        sorted_indices = np.argsort(fitness_scores)[::-1]
        sorted_population = [population[i] for i in sorted_indices]
        
        next_generation = []
        
        # Elitism: keep best individuals
        next_generation.extend(sorted_population[:self.elite_size])
        
        # Generate rest of population
        while len(next_generation) < self.population_size:
            # Selection
            parent1 = self._tournament_selection(sorted_population, fitness_scores)
            parent2 = self._tournament_selection(sorted_population, fitness_scores)
            
            # Crossover
            if random.random() < self.crossover_rate:
                child1, child2 = self._crossover(parent1, parent2)
            else:
                child1, child2 = parent1.copy(), parent2.copy()
            
            # Mutation
            child1 = self._mutate(child1)
            child2 = self._mutate(child2)
            
            next_generation.extend([child1, child2])
        
        return next_generation[:self.population_size]
    
    def _tournament_selection(self, population: List[List[float]], fitness_scores: List[float], tournament_size: int = 3) -> List[float]:
        """Tournament selection"""
        tournament_indices = random.sample(range(len(population)), min(tournament_size, len(population)))
        tournament_fitness = [fitness_scores[i] for i in tournament_indices]
        winner_idx = tournament_indices[np.argmax(tournament_fitness)]
        return population[winner_idx].copy()
    
    def _crossover(self, parent1: List[float], parent2: List[float]) -> Tuple[List[float], List[float]]:
        """Single-point crossover"""
        crossover_point = random.randint(1, len(parent1) - 1)
        
        child1 = parent1[:crossover_point] + parent2[crossover_point:]
        child2 = parent2[:crossover_point] + parent1[crossover_point:]
        
        return child1, child2
    
    def _mutate(self, individual: List[float]) -> List[float]:
        """Gaussian mutation"""
        mutated = individual.copy()
        
        for i, param in enumerate(self.parameter_space):
            if random.random() < self.mutation_rate:
                if param.param_type == 'float':
                    # Gaussian mutation
                    mutation_strength = (param.max_value - param.min_value) * 0.1
                    mutated[i] += random.gauss(0, mutation_strength)
                    mutated[i] = max(param.min_value, min(param.max_value, mutated[i]))
                elif param.param_type == 'int':
                    # Random integer mutation
                    mutated[i] = random.randint(int(param.min_value), int(param.max_value))
                else:  # categorical
                    mutated[i] = random.choice(param.categories)
        
        return mutated
    
    def _individual_to_parameters(self, individual: List[float]) -> Dict[str, float]:
        """Convert individual to parameter dictionary"""
        parameters = {}
        for i, param in enumerate(self.parameter_space):
            if param.param_type == 'int':
                parameters[param.name] = int(individual[i])
            else:
                parameters[param.name] = individual[i]
        
        return parameters

class BayesianOptimizer:
    """Bayesian Optimization using Gaussian Processes"""
    
    def __init__(self, parameter_space: List[ParameterSpace], evaluator: PerformanceEvaluator):
        self.parameter_space = parameter_space
        self.evaluator = evaluator
        self.n_initial_points = 10
        self.n_calls = 50
        self.acquisition_function = 'EI'  # Expected Improvement
    
    async def optimize(self, symbols: List[str]) -> OptimizationResult:
        """Run Bayesian optimization"""
        if not OPTIMIZATION_AVAILABLE:
            logger.error("Optimization libraries not available")
            return self._create_default_result()
        
        start_time = datetime.now()
        
        # Convert parameter space to bounds
        bounds = [(param.min_value, param.max_value) for param in self.parameter_space]
        
        # Objective function wrapper
        async def objective(x):
            parameters = self._array_to_parameters(x)
            return -(await self.evaluator.evaluate_parameters(parameters, symbols))  # Minimize negative
        
        # Initial random sampling
        X_init = []
        y_init = []
        
        logger.info(f"Bayesian optimization: Initial sampling ({self.n_initial_points} points)")
        
        for i in range(self.n_initial_points):
            x = [random.uniform(bound[0], bound[1]) for bound in bounds]
            y = await objective(x)
            X_init.append(x)
            y_init.append(y)
            
            logger.info(f"Initial point {i+1}/{self.n_initial_points}: Score={-y:.2f}")
        
        # Gaussian Process optimization
        X_init = np.array(X_init)
        y_init = np.array(y_init)
        
        # Initialize GP
        kernel = Matern(length_scale=1.0, nu=2.5)
        gp = GaussianProcessRegressor(kernel=kernel, alpha=1e-6, normalize_y=True)
        
        optimization_history = []
        X_all = X_init.copy()
        y_all = y_init.copy()
        
        logger.info(f"Bayesian optimization: Main loop ({self.n_calls - self.n_initial_points} iterations)")
        
        for iteration in range(self.n_calls - self.n_initial_points):
            # Fit GP
            gp.fit(X_all, y_all)
            
            # Find next point using acquisition function
            next_x = self._optimize_acquisition(gp, bounds)
            next_y = await objective(next_x)
            
            # Update data
            X_all = np.vstack([X_all, next_x])
            y_all = np.append(y_all, next_y)
            
            optimization_history.append({
                'iteration': iteration + self.n_initial_points,
                'best_score': -np.min(y_all),
                'current_score': -next_y
            })
            
            logger.info(f"Iteration {iteration + 1}: Current={-next_y:.2f}, Best={-np.min(y_all):.2f}")
        
        # Get best result
        best_idx = np.argmin(y_all)
        best_x = X_all[best_idx]
        best_score = -y_all[best_idx]
        best_parameters = self._array_to_parameters(best_x)
        
        optimization_time = (datetime.now() - start_time).total_seconds()
        
        return OptimizationResult(
            best_parameters=best_parameters,
            best_score=best_score,
            optimization_history=optimization_history,
            convergence_info={'converged': True, 'total_iterations': self.n_calls},
            total_evaluations=self.n_calls,
            optimization_time=optimization_time,
            improvement_percentage=((best_score - 50) / 50) * 100
        )
    
    def _optimize_acquisition(self, gp, bounds) -> np.ndarray:
        """Optimize acquisition function to find next point"""
        def acquisition(x):
            x = x.reshape(1, -1)
            mu, sigma = gp.predict(x, return_std=True)
            
            # Expected Improvement
            best_f = np.min(gp.y_train_)
            z = (best_f - mu) / (sigma + 1e-9)
            ei = (best_f - mu) * self._normal_cdf(z) + sigma * self._normal_pdf(z)
            
            return -ei[0]  # Minimize negative EI
        
        # Multi-start optimization
        best_x = None
        best_acquisition = float('inf')
        
        for _ in range(10):  # 10 random starts
            x0 = [random.uniform(bound[0], bound[1]) for bound in bounds]
            
            result = minimize(acquisition, x0, bounds=bounds, method='L-BFGS-B')
            
            if result.fun < best_acquisition:
                best_acquisition = result.fun
                best_x = result.x
        
        return best_x
    
    def _normal_cdf(self, x):
        """Standard normal CDF"""
        return 0.5 * (1 + np.sign(x) * np.sqrt(1 - np.exp(-2 * x**2 / np.pi)))
    
    def _normal_pdf(self, x):
        """Standard normal PDF"""
        return np.exp(-0.5 * x**2) / np.sqrt(2 * np.pi)
    
    def _array_to_parameters(self, x: np.ndarray) -> Dict[str, float]:
        """Convert array to parameter dictionary"""
        parameters = {}
        for i, param in enumerate(self.parameter_space):
            if param.param_type == 'int':
                parameters[param.name] = int(round(x[i]))
            else:
                parameters[param.name] = x[i]
        
        return parameters
    
    def _create_default_result(self) -> OptimizationResult:
        """Create default result when optimization fails"""
        default_params = {}
        for param in self.parameter_space:
            if param.current_value is not None:
                default_params[param.name] = param.current_value
            else:
                default_params[param.name] = (param.min_value + param.max_value) / 2
        
        return OptimizationResult(
            best_parameters=default_params,
            best_score=50.0,
            optimization_history=[],
            convergence_info={'converged': False, 'error': 'Optimization libraries not available'},
            total_evaluations=0,
            optimization_time=0.0,
            improvement_percentage=0.0
        )

class ParameterOptimizationManager:
    """Main manager for parameter optimization"""
    
    def __init__(self, data_manager, ttm_squeeze_engine):
        self.data_manager = data_manager
        self.ttm_squeeze_engine = ttm_squeeze_engine
        self.evaluator = PerformanceEvaluator(data_manager, ttm_squeeze_engine)
        
        # Define parameter space for TTM Squeeze
        self.parameter_space = [
            ParameterSpace('bb_period', 10, 30, 'int', current_value=20),
            ParameterSpace('bb_std_dev', 1.0, 3.0, 'float', current_value=2.0),
            ParameterSpace('kc_period', 10, 30, 'int', current_value=20),
            ParameterSpace('kc_atr_multiplier', 1.0, 2.5, 'float', current_value=1.5),
            ParameterSpace('momentum_period', 5, 20, 'int', current_value=12),
            ParameterSpace('signal_threshold', 0.1, 0.5, 'float', current_value=0.2)
        ]
        
        self.optimization_history = []
    
    async def optimize_parameters(self, method: str = 'genetic', symbols: Optional[List[str]] = None) -> OptimizationResult:
        """Optimize TTM Squeeze parameters"""
        if symbols is None:
            # Use a subset of S&P 500 for optimization
            symbols = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'META', 'NVDA', 'JPM', 'JNJ', 'V']
        
        logger.info(f"Starting parameter optimization using {method} method")
        
        if method == 'genetic':
            optimizer = GeneticAlgorithmOptimizer(self.parameter_space, self.evaluator)
        elif method == 'bayesian':
            optimizer = BayesianOptimizer(self.parameter_space, self.evaluator)
        else:
            raise ValueError(f"Unknown optimization method: {method}")
        
        result = await optimizer.optimize(symbols)
        
        # Store optimization result
        self.optimization_history.append({
            'timestamp': datetime.now(),
            'method': method,
            'result': result
        })
        
        logger.info(f"Optimization completed. Best score: {result.best_score:.2f}")
        logger.info(f"Best parameters: {result.best_parameters}")
        
        return result
    
    def apply_optimized_parameters(self, parameters: Dict[str, float]):
        """Apply optimized parameters to TTM Squeeze engine"""
        logger.info(f"Applying optimized parameters: {parameters}")
        
        # Update TTM Squeeze engine parameters
        if 'bb_period' in parameters:
            self.ttm_squeeze_engine.bb_period = int(parameters['bb_period'])
        if 'bb_std_dev' in parameters:
            self.ttm_squeeze_engine.bb_std_dev = parameters['bb_std_dev']
        if 'kc_period' in parameters:
            self.ttm_squeeze_engine.kc_period = int(parameters['kc_period'])
        if 'kc_atr_multiplier' in parameters:
            self.ttm_squeeze_engine.kc_atr_multiplier = parameters['kc_atr_multiplier']
        if 'momentum_period' in parameters:
            self.ttm_squeeze_engine.momentum_period = int(parameters['momentum_period'])
        if 'signal_threshold' in parameters:
            self.ttm_squeeze_engine.signal_threshold = parameters['signal_threshold']
        
        logger.info("Parameters applied successfully")
    
    async def run_periodic_optimization(self, interval_days: int = 30):
        """Run periodic parameter optimization"""
        logger.info(f"Starting periodic optimization every {interval_days} days")
        
        while True:
            try:
                # Run optimization
                result = await self.optimize_parameters(method='genetic')
                
                # Apply if improvement is significant
                if result.improvement_percentage > 5:  # 5% improvement threshold
                    self.apply_optimized_parameters(result.best_parameters)
                    logger.info(f"Applied new parameters with {result.improvement_percentage:.1f}% improvement")
                else:
                    logger.info(f"No significant improvement ({result.improvement_percentage:.1f}%), keeping current parameters")
                
                # Wait for next optimization
                await asyncio.sleep(interval_days * 24 * 3600)
                
            except Exception as e:
                logger.error(f"Error in periodic optimization: {e}")
                await asyncio.sleep(3600)  # Wait 1 hour before retry
    
    def get_optimization_status(self) -> Dict[str, Any]:
        """Get current optimization status"""
        if not self.optimization_history:
            return {'status': 'No optimizations run yet'}
        
        latest = self.optimization_history[-1]
        
        return {
            'last_optimization': latest['timestamp'].isoformat(),
            'method': latest['method'],
            'best_score': latest['result'].best_score,
            'improvement': latest['result'].improvement_percentage,
            'current_parameters': latest['result'].best_parameters,
            'total_optimizations': len(self.optimization_history)
        }
