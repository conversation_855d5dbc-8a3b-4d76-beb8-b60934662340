"""
Simple test script to validate the TTM Squeeze system
"""
import sys
import os
from pathlib import Path
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Add src to path
sys.path.append(str(Path(__file__).parent / 'src'))

def test_imports():
    """Test that all modules can be imported"""
    try:
        from indicators.technical_indicators import TechnicalIndicators
        from indicators.ttm_squeeze import TTMSqueeze
        from indicators.multi_timeframe import MultiTimeframeAnalyzer
        from data.data_manager import DataManager
        from scanner.stock_screener import StockScreener
        from trading.risk_manager import RiskManager
        print("✅ All imports successful")
        return True
    except Exception as e:
        print(f"❌ Import error: {e}")
        return False

def create_sample_data():
    """Create sample OHLCV data for testing"""
    dates = pd.date_range(start='2023-01-01', periods=100, freq='15min')
    np.random.seed(42)
    
    # Generate realistic price data
    base_price = 100
    prices = []
    volumes = []
    
    for i in range(100):
        # Add some trend and volatility
        trend = 0.01 if i > 50 else -0.005
        volatility = 0.5 if i < 30 or i > 70 else 0.2  # Squeeze in middle
        
        price_change = trend + np.random.randn() * volatility
        base_price = max(base_price + price_change, 50)  # Prevent negative prices
        prices.append(base_price)
        volumes.append(np.random.randint(1000000, 5000000))
    
    # Create OHLC from close prices
    opens = [prices[0]] + prices[:-1]
    highs = [p + abs(np.random.randn() * 0.3) for p in prices]
    lows = [p - abs(np.random.randn() * 0.3) for p in prices]
    
    return pd.DataFrame({
        'open': opens,
        'high': highs,
        'low': lows,
        'close': prices,
        'volume': volumes
    }, index=dates)

def test_technical_indicators():
    """Test technical indicators"""
    try:
        from indicators.technical_indicators import TechnicalIndicators
        
        data = create_sample_data()
        
        # Test SMA
        sma = TechnicalIndicators.sma(data['close'], 20)
        assert len(sma) == len(data)
        print("✅ SMA calculation working")
        
        # Test EMA
        ema = TechnicalIndicators.ema(data['close'], 20)
        assert len(ema) == len(data)
        print("✅ EMA calculation working")
        
        # Test Bollinger Bands
        bb_upper, bb_middle, bb_lower = TechnicalIndicators.bollinger_bands(data['close'])
        assert len(bb_upper) == len(data)
        print("✅ Bollinger Bands calculation working")
        
        # Test Keltner Channels
        kc_upper, kc_middle, kc_lower = TechnicalIndicators.keltner_channels(
            data['high'], data['low'], data['close']
        )
        assert len(kc_upper) == len(data)
        print("✅ Keltner Channels calculation working")
        
        # Test MACD
        macd_line, signal_line, histogram = TechnicalIndicators.macd(data['close'])
        assert len(macd_line) == len(data)
        print("✅ MACD calculation working")
        
        return True
    except Exception as e:
        print(f"❌ Technical indicators error: {e}")
        return False

def test_ttm_squeeze():
    """Test TTM Squeeze implementation"""
    try:
        from indicators.ttm_squeeze import TTMSqueeze
        
        data = create_sample_data()
        ttm = TTMSqueeze()
        
        # Test squeeze calculation
        result = ttm.calculate_squeeze(data)
        
        required_columns = [
            'bb_upper', 'bb_middle', 'bb_lower',
            'kc_upper', 'kc_middle', 'kc_lower',
            'momentum', 'is_squeeze', 'momentum_color',
            'signal_strength', 'entry_signal'
        ]
        
        for col in required_columns:
            assert col in result.columns
        
        print("✅ TTM Squeeze calculation working")
        
        # Test current signal
        signal = ttm.get_current_signal(data, 'TEST', '15Min')
        assert signal is not None
        assert signal.symbol == 'TEST'
        print("✅ TTM Squeeze signal generation working")
        
        # Print some sample results
        latest = result.iloc[-1]
        print(f"   Sample signal - Squeeze: {latest['is_squeeze']}, "
              f"Momentum: {latest['momentum']:.4f}, "
              f"Color: {latest['momentum_color']}")
        
        return True
    except Exception as e:
        print(f"❌ TTM Squeeze error: {e}")
        return False

def test_multi_timeframe():
    """Test multi-timeframe analysis"""
    try:
        from indicators.multi_timeframe import MultiTimeframeAnalyzer
        
        analyzer = MultiTimeframeAnalyzer()
        
        # Create data for multiple timeframes
        data_dict = {
            '15Min': create_sample_data(),
            '5Min': create_sample_data(),
            '30Min': create_sample_data()
        }
        
        # Test analysis
        signal = analyzer.analyze_symbol('TEST', data_dict)
        
        if signal:
            print("✅ Multi-timeframe analysis working")
            print(f"   Signal strength: {signal.overall_strength:.2f}")
            print(f"   Confirmations: {signal.confirmations_count}/{signal.required_confirmations}")
            print(f"   Recommendation: {signal.entry_recommendation}")
        else:
            print("✅ Multi-timeframe analysis working (no signal generated)")
        
        return True
    except Exception as e:
        print(f"❌ Multi-timeframe analysis error: {e}")
        return False

def test_risk_manager():
    """Test risk management"""
    try:
        from trading.risk_manager import RiskManager
        
        risk_manager = RiskManager()
        
        # Test trade risk assessment
        trade_risk = risk_manager.assess_trade_risk(
            symbol='TEST',
            entry_price=100.0,
            stop_loss=98.0,
            quantity=100
        )
        
        assert trade_risk.symbol == 'TEST'
        assert trade_risk.risk_amount > 0
        print("✅ Risk management working")
        print(f"   Risk amount: ${trade_risk.risk_amount:.2f}")
        print(f"   Risk/Reward ratio: {trade_risk.risk_reward_ratio:.2f}")
        
        return True
    except Exception as e:
        print(f"❌ Risk management error: {e}")
        return False

def test_configuration():
    """Test configuration loading"""
    try:
        from config import Config
        
        assert hasattr(Config, 'TTM_SQUEEZE_CONFIG')
        assert hasattr(Config, 'TIMEFRAMES')
        assert hasattr(Config, 'RISK_MANAGEMENT')
        
        print("✅ Configuration loading working")
        print(f"   BB Period: {Config.TTM_SQUEEZE_CONFIG['bb_period']}")
        print(f"   Primary timeframe: {Config.TIMEFRAMES['primary']}")
        
        return True
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Testing TTM Squeeze Trading System")
    print("=" * 50)
    
    tests = [
        ("Configuration", test_configuration),
        ("Imports", test_imports),
        ("Technical Indicators", test_technical_indicators),
        ("TTM Squeeze", test_ttm_squeeze),
        ("Multi-timeframe Analysis", test_multi_timeframe),
        ("Risk Management", test_risk_manager),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 Testing {test_name}...")
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"🎯 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! System is ready to use.")
        print("\nNext steps:")
        print("1. Run: python main.py (to start the web interface)")
        print("2. Or test data providers with your API keys")
        print("3. Configure your trading parameters in config.py")
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
    
    return passed == total

if __name__ == "__main__":
    main()
