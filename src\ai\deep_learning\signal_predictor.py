"""
Deep Learning Signal Quality Predictor for TTM Squeeze Trading System
Advanced LSTM and Transformer models for enhanced signal prediction
"""

import numpy as np
import pandas as pd
import tensorflow as tf
from tensorflow.keras.models import Model, Sequential
from tensorflow.keras.layers import LSTM, Dense, Dropout, Input, MultiHeadAttention, LayerNormalization
from tensorflow.keras.optimizers import Adam
from sklearn.preprocessing import MinMaxScaler
from sklearn.ensemble import VotingRegressor
import joblib
import logging
from typing import Dict, List, Tuple, Optional
from datetime import datetime, timedelta
import asyncio

logger = logging.getLogger(__name__)

class LSTMSignalPredictor:
    """LSTM-based signal quality predictor"""
    
    def __init__(self, sequence_length: int = 60, features: int = 20):
        self.sequence_length = sequence_length
        self.features = features
        self.model = None
        self.scaler = MinMaxScaler()
        self.is_trained = False
        
    def _build_lstm_model(self) -> Model:
        """Build LSTM neural network architecture"""
        model = Sequential([
            LSTM(128, return_sequences=True, input_shape=(self.sequence_length, self.features)),
            Dropout(0.2),
            LSTM(64, return_sequences=True),
            Dropout(0.2),
            LSTM(32, return_sequences=False),
            Dropout(0.2),
            Dense(16, activation='relu'),
            Dense(8, activation='relu'),
            Dense(1, activation='sigmoid')  # Signal quality score 0-1
        ])
        
        model.compile(
            optimizer=Adam(learning_rate=0.001),
            loss='mse',
            metrics=['mae']
        )
        
        return model
    
    def prepare_sequences(self, data: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """Prepare time series sequences for LSTM training"""
        # Extract features: OHLCV, technical indicators, squeeze metrics
        feature_columns = [
            'open', 'high', 'low', 'close', 'volume',
            'bb_upper', 'bb_lower', 'kc_upper', 'kc_lower',
            'momentum', 'signal_strength', 'is_squeeze',
            'rsi', 'macd', 'stoch_k', 'stoch_d',
            'atr', 'adx', 'cci', 'williams_r'
        ]
        
        # Fill missing columns with default values
        for col in feature_columns:
            if col not in data.columns:
                data[col] = 0.0
        
        # Scale features
        scaled_data = self.scaler.fit_transform(data[feature_columns])
        
        # Create sequences
        X, y = [], []
        for i in range(self.sequence_length, len(scaled_data)):
            X.append(scaled_data[i-self.sequence_length:i])
            # Target: future signal success (simplified for demo)
            y.append(data['signal_strength'].iloc[i] if 'signal_strength' in data.columns else 0.5)
        
        return np.array(X), np.array(y)
    
    def train(self, training_data: List[pd.DataFrame], epochs: int = 50):
        """Train LSTM model on historical data"""
        logger.info("Training LSTM signal predictor...")
        
        # Combine all training data
        combined_data = pd.concat(training_data, ignore_index=True)
        
        # Prepare sequences
        X, y = self.prepare_sequences(combined_data)
        
        if len(X) == 0:
            logger.warning("No training sequences available")
            return
        
        # Build model
        self.model = self._build_lstm_model()
        
        # Train model
        history = self.model.fit(
            X, y,
            epochs=epochs,
            batch_size=32,
            validation_split=0.2,
            verbose=1
        )
        
        self.is_trained = True
        logger.info(f"LSTM training completed. Final loss: {history.history['loss'][-1]:.4f}")
        
        return history
    
    def predict(self, sequence_data: np.ndarray) -> Tuple[float, float]:
        """Predict signal quality for a sequence"""
        if not self.is_trained or self.model is None:
            return 0.5, 0.5  # Default values
        
        try:
            # Ensure correct shape
            if sequence_data.ndim == 2:
                sequence_data = sequence_data.reshape(1, *sequence_data.shape)
            
            # Predict
            prediction = self.model.predict(sequence_data, verbose=0)[0][0]
            
            # Calculate confidence based on prediction certainty
            confidence = abs(prediction - 0.5) * 2  # Higher confidence for extreme values
            
            return float(prediction), float(confidence)
            
        except Exception as e:
            logger.error(f"LSTM prediction error: {e}")
            return 0.5, 0.5

class TransformerSignalPredictor:
    """Transformer-based signal quality predictor"""
    
    def __init__(self, sequence_length: int = 60, d_model: int = 64, num_heads: int = 8):
        self.sequence_length = sequence_length
        self.d_model = d_model
        self.num_heads = num_heads
        self.model = None
        self.scaler = MinMaxScaler()
        self.is_trained = False
    
    def _build_transformer_model(self) -> Model:
        """Build Transformer neural network architecture"""
        # Input layer
        inputs = Input(shape=(self.sequence_length, self.d_model))
        
        # Multi-head attention
        attention_output = MultiHeadAttention(
            num_heads=self.num_heads,
            key_dim=self.d_model // self.num_heads
        )(inputs, inputs)
        
        # Add & Norm
        attention_output = LayerNormalization()(inputs + attention_output)
        
        # Feed forward
        ffn_output = Dense(128, activation='relu')(attention_output)
        ffn_output = Dense(self.d_model)(ffn_output)
        ffn_output = LayerNormalization()(attention_output + ffn_output)
        
        # Global average pooling
        pooled = tf.keras.layers.GlobalAveragePooling1D()(ffn_output)
        
        # Output layers
        dense1 = Dense(32, activation='relu')(pooled)
        dropout1 = Dropout(0.2)(dense1)
        dense2 = Dense(16, activation='relu')(dropout1)
        outputs = Dense(1, activation='sigmoid')(dense2)
        
        model = Model(inputs=inputs, outputs=outputs)
        model.compile(
            optimizer=Adam(learning_rate=0.001),
            loss='mse',
            metrics=['mae']
        )
        
        return model
    
    def train(self, training_data: List[pd.DataFrame], epochs: int = 30):
        """Train Transformer model"""
        logger.info("Training Transformer signal predictor...")
        
        # Similar to LSTM but adapted for Transformer input
        combined_data = pd.concat(training_data, ignore_index=True)
        X, y = self._prepare_transformer_sequences(combined_data)
        
        if len(X) == 0:
            logger.warning("No training sequences available for Transformer")
            return
        
        self.model = self._build_transformer_model()
        
        history = self.model.fit(
            X, y,
            epochs=epochs,
            batch_size=16,
            validation_split=0.2,
            verbose=1
        )
        
        self.is_trained = True
        logger.info(f"Transformer training completed. Final loss: {history.history['loss'][-1]:.4f}")
        
        return history
    
    def _prepare_transformer_sequences(self, data: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """Prepare sequences for Transformer model"""
        # Similar to LSTM but with different feature engineering
        feature_columns = [
            'open', 'high', 'low', 'close', 'volume',
            'bb_upper', 'bb_lower', 'kc_upper', 'kc_lower',
            'momentum', 'signal_strength', 'is_squeeze'
        ]
        
        # Pad to d_model dimensions
        while len(feature_columns) < self.d_model:
            feature_columns.append(f'padding_{len(feature_columns)}')
        
        for col in feature_columns:
            if col not in data.columns:
                data[col] = 0.0
        
        scaled_data = self.scaler.fit_transform(data[feature_columns[:self.d_model]])
        
        X, y = [], []
        for i in range(self.sequence_length, len(scaled_data)):
            X.append(scaled_data[i-self.sequence_length:i])
            y.append(data['signal_strength'].iloc[i] if 'signal_strength' in data.columns else 0.5)
        
        return np.array(X), np.array(y)
    
    def predict(self, sequence_data: np.ndarray) -> Tuple[float, float]:
        """Predict signal quality using Transformer"""
        if not self.is_trained or self.model is None:
            return 0.5, 0.5
        
        try:
            if sequence_data.ndim == 2:
                sequence_data = sequence_data.reshape(1, *sequence_data.shape)
            
            prediction = self.model.predict(sequence_data, verbose=0)[0][0]
            confidence = abs(prediction - 0.5) * 2
            
            return float(prediction), float(confidence)
            
        except Exception as e:
            logger.error(f"Transformer prediction error: {e}")
            return 0.5, 0.5

class EnsembleSignalPredictor:
    """Ensemble predictor combining LSTM and Transformer models"""
    
    def __init__(self):
        self.lstm_predictor = LSTMSignalPredictor()
        self.transformer_predictor = TransformerSignalPredictor()
        self.ensemble_weights = {'lstm': 0.6, 'transformer': 0.4}
        self.is_trained = False
    
    async def train_ensemble(self, training_data: List[pd.DataFrame]):
        """Train both models in the ensemble"""
        logger.info("Training ensemble signal predictor...")
        
        # Train LSTM
        lstm_history = self.lstm_predictor.train(training_data, epochs=30)
        
        # Train Transformer
        transformer_history = self.transformer_predictor.train(training_data, epochs=20)
        
        self.is_trained = True
        logger.info("Ensemble training completed")
        
        return {
            'lstm_history': lstm_history,
            'transformer_history': transformer_history
        }
    
    def predict_ensemble(self, sequence_data: np.ndarray) -> Tuple[float, float]:
        """Make ensemble prediction"""
        if not self.is_trained:
            return 0.5, 0.5
        
        # Get predictions from both models
        lstm_pred, lstm_conf = self.lstm_predictor.predict(sequence_data)
        transformer_pred, transformer_conf = self.transformer_predictor.predict(sequence_data)
        
        # Weighted ensemble
        ensemble_prediction = (
            lstm_pred * self.ensemble_weights['lstm'] +
            transformer_pred * self.ensemble_weights['transformer']
        )
        
        # Combined confidence
        ensemble_confidence = (
            lstm_conf * self.ensemble_weights['lstm'] +
            transformer_conf * self.ensemble_weights['transformer']
        )
        
        return ensemble_prediction, ensemble_confidence
    
    def save_models(self, base_path: str):
        """Save trained models"""
        try:
            if self.lstm_predictor.model:
                self.lstm_predictor.model.save(f"{base_path}_lstm.h5")
            if self.transformer_predictor.model:
                self.transformer_predictor.model.save(f"{base_path}_transformer.h5")
            
            # Save scalers
            joblib.dump(self.lstm_predictor.scaler, f"{base_path}_lstm_scaler.pkl")
            joblib.dump(self.transformer_predictor.scaler, f"{base_path}_transformer_scaler.pkl")
            
            logger.info(f"Models saved to {base_path}")
            
        except Exception as e:
            logger.error(f"Error saving models: {e}")
    
    def load_models(self, base_path: str):
        """Load pre-trained models"""
        try:
            # Load models
            self.lstm_predictor.model = tf.keras.models.load_model(f"{base_path}_lstm.h5")
            self.transformer_predictor.model = tf.keras.models.load_model(f"{base_path}_transformer.h5")
            
            # Load scalers
            self.lstm_predictor.scaler = joblib.load(f"{base_path}_lstm_scaler.pkl")
            self.transformer_predictor.scaler = joblib.load(f"{base_path}_transformer_scaler.pkl")
            
            self.lstm_predictor.is_trained = True
            self.transformer_predictor.is_trained = True
            self.is_trained = True
            
            logger.info(f"Models loaded from {base_path}")
            
        except Exception as e:
            logger.error(f"Error loading models: {e}")
