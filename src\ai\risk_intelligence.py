"""
Risk Assessment Intelligence - AI-driven position sizing and risk management
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
import logging
from datetime import datetime, timedelta
from dataclasses import dataclass

from ..indicators.multi_timeframe import MultiTimeframeSignal

logger = logging.getLogger(__name__)

@dataclass
class RiskMetrics:
    """Risk metrics for a specific symbol"""
    symbol: str
    volatility: float  # Annualized volatility
    beta: float  # Beta vs SPY
    max_drawdown: float  # Historical max drawdown
    sharpe_ratio: float  # Risk-adjusted returns
    var_95: float  # Value at Risk (95% confidence)
    correlation_spy: float  # Correlation with market

@dataclass
class PositionRisk:
    """Position-specific risk assessment"""
    recommended_size: float  # Position size as % of portfolio
    max_position_size: float  # Maximum allowed size
    stop_loss_pct: float  # Stop loss percentage
    take_profit_pct: float  # Take profit percentage
    risk_reward_ratio: float  # Expected risk/reward
    max_risk_per_trade: float  # Max risk as % of portfolio
    confidence_level: float  # Confidence in risk assessment

class RiskAssessmentEngine:
    """AI-driven risk assessment and position sizing engine"""
    
    def __init__(self):
        self.risk_cache = {}  # symbol -> RiskMetrics
        self.portfolio_risk = 0.02  # Default 2% portfolio risk per trade
        self.max_portfolio_risk = 0.06  # Maximum 6% total portfolio risk
        self.max_single_position = 0.10  # Maximum 10% in single position
        
        # Risk model parameters
        self.lookback_days = 252  # 1 year for risk calculations
        self.confidence_levels = [0.95, 0.99]  # VaR confidence levels
        
        # Market condition adjustments
        self.market_risk_adjustments = {
            'trending': 1.0,      # Normal risk
            'ranging': 0.8,       # Reduce risk in ranging markets
            'volatile': 0.6,      # Significantly reduce risk in volatile markets
            'calm': 1.2,          # Slightly increase risk in calm markets
            'unknown': 0.7        # Conservative in unknown conditions
        }
        
        logger.info("Risk Assessment Engine initialized")
    
    def assess_risk(self, signal: MultiTimeframeSignal, 
                   market_context: Dict) -> Dict:
        """
        Assess risk and calculate position sizing for a signal
        
        Args:
            signal: TTM Squeeze signal
            market_context: Current market conditions
            
        Returns:
            Dictionary with risk assessment and position sizing
        """
        try:
            # Calculate risk metrics for the symbol
            risk_metrics = self._calculate_risk_metrics(signal.symbol)
            
            # Assess signal-specific risk
            signal_risk = self._assess_signal_risk(signal, risk_metrics)
            
            # Calculate position sizing
            position_size = self._calculate_position_size(
                signal, risk_metrics, market_context
            )
            
            # Calculate stop loss and take profit levels
            stop_loss, take_profit = self._calculate_exit_levels(
                signal, risk_metrics, market_context
            )
            
            # Calculate risk/reward ratio
            risk_reward = self._calculate_risk_reward_ratio(
                signal, stop_loss, take_profit
            )
            
            # Adjust for market conditions
            market_adjustment = self._get_market_risk_adjustment(market_context)
            adjusted_position_size = position_size * market_adjustment
            
            # Final risk assessment
            max_risk = adjusted_position_size * stop_loss
            
            return {
                'position_size': min(adjusted_position_size, self.max_single_position),
                'stop_loss': stop_loss,
                'take_profit': take_profit,
                'max_risk': max_risk,
                'risk_reward_ratio': risk_reward,
                'volatility': risk_metrics.volatility,
                'beta': risk_metrics.beta,
                'confidence': signal_risk,
                'market_adjustment': market_adjustment,
                'var_95': risk_metrics.var_95
            }
            
        except Exception as e:
            logger.error(f"Error assessing risk for {signal.symbol}: {e}")
            return self._get_default_risk_assessment()
    
    def _calculate_risk_metrics(self, symbol: str) -> RiskMetrics:
        """Calculate comprehensive risk metrics for a symbol"""
        try:
            # Check cache first
            if symbol in self.risk_cache:
                cached_metrics = self.risk_cache[symbol]
                # Use cached if less than 1 day old
                if hasattr(cached_metrics, 'timestamp'):
                    if datetime.now() - cached_metrics.timestamp < timedelta(days=1):
                        return cached_metrics
            
            # This would typically fetch historical data
            # For now, use estimated metrics based on symbol characteristics
            risk_metrics = self._estimate_risk_metrics(symbol)
            
            # Cache the result
            risk_metrics.timestamp = datetime.now()
            self.risk_cache[symbol] = risk_metrics
            
            return risk_metrics
            
        except Exception as e:
            logger.warning(f"Error calculating risk metrics for {symbol}: {e}")
            return self._get_default_risk_metrics(symbol)
    
    def _estimate_risk_metrics(self, symbol: str) -> RiskMetrics:
        """Estimate risk metrics based on symbol characteristics"""
        # This is a simplified estimation - in production, use actual historical data
        
        # Default values
        volatility = 0.25  # 25% annualized
        beta = 1.0
        max_drawdown = 0.20  # 20%
        sharpe_ratio = 0.5
        var_95 = 0.03  # 3% daily VaR
        correlation_spy = 0.7
        
        # Adjust based on symbol type
        if symbol in ['AAPL', 'MSFT', 'GOOGL', 'AMZN']:
            # Large cap tech - moderate volatility
            volatility = 0.30
            beta = 1.2
            max_drawdown = 0.25
            sharpe_ratio = 0.8
            var_95 = 0.035
            correlation_spy = 0.8
            
        elif symbol in ['TSLA', 'NVDA', 'AMD', 'COIN']:
            # High volatility growth stocks
            volatility = 0.50
            beta = 1.8
            max_drawdown = 0.40
            sharpe_ratio = 0.3
            var_95 = 0.06
            correlation_spy = 0.6
            
        elif symbol in ['KO', 'PG', 'JNJ', 'WMT']:
            # Low volatility defensive stocks
            volatility = 0.15
            beta = 0.6
            max_drawdown = 0.15
            sharpe_ratio = 0.7
            var_95 = 0.02
            correlation_spy = 0.5
            
        elif symbol in ['XLF', 'XLE', 'XLI']:
            # Sector ETFs
            volatility = 0.22
            beta = 1.1
            max_drawdown = 0.30
            sharpe_ratio = 0.4
            var_95 = 0.025
            correlation_spy = 0.85
        
        return RiskMetrics(
            symbol=symbol,
            volatility=volatility,
            beta=beta,
            max_drawdown=max_drawdown,
            sharpe_ratio=sharpe_ratio,
            var_95=var_95,
            correlation_spy=correlation_spy
        )
    
    def _assess_signal_risk(self, signal: MultiTimeframeSignal, 
                          risk_metrics: RiskMetrics) -> float:
        """Assess risk specific to the signal quality"""
        base_confidence = 70.0
        
        # Adjust based on signal strength
        strength_adjustment = (signal.overall_strength - 0.5) * 40  # -20 to +20
        
        # Adjust based on timeframe confirmations
        confirmation_ratio = signal.confirmations_count / max(signal.required_confirmations, 1)
        confirmation_adjustment = (confirmation_ratio - 1) * 20  # Up to +20 for extra confirmations
        
        # Adjust based on symbol volatility
        vol_adjustment = max(-15, min(15, (0.25 - risk_metrics.volatility) * 50))
        
        # Adjust based on trend alignment
        trend_adjustment = 10 if signal.trend_alignment else -5
        
        # Adjust based on volume confirmation
        volume_adjustment = 5 if signal.volume_confirmation else -3
        
        total_confidence = (base_confidence + strength_adjustment + 
                          confirmation_adjustment + vol_adjustment + 
                          trend_adjustment + volume_adjustment)
        
        return max(0.0, min(100.0, total_confidence))
    
    def _calculate_position_size(self, signal: MultiTimeframeSignal,
                               risk_metrics: RiskMetrics,
                               market_context: Dict) -> float:
        """Calculate optimal position size"""
        
        # Base position size using Kelly Criterion approximation
        win_rate = 0.55  # Estimated win rate for TTM Squeeze signals
        avg_win = 0.04   # Average win 4%
        avg_loss = 0.02  # Average loss 2%
        
        # Kelly fraction
        kelly_fraction = (win_rate * avg_win - (1 - win_rate) * avg_loss) / avg_win
        kelly_fraction = max(0, min(kelly_fraction, 0.25))  # Cap at 25%
        
        # Adjust for signal strength
        signal_adjustment = signal.overall_strength
        
        # Adjust for volatility (lower position size for higher volatility)
        volatility_adjustment = max(0.3, min(1.5, 0.25 / risk_metrics.volatility))
        
        # Adjust for market conditions
        market_favorability = market_context.get('favorability_score', 50) / 100
        
        # Calculate base position size
        base_size = kelly_fraction * signal_adjustment * volatility_adjustment * market_favorability
        
        # Apply portfolio risk constraint
        max_size_by_risk = self.portfolio_risk / risk_metrics.var_95
        
        # Final position size
        position_size = min(base_size, max_size_by_risk, self.max_single_position)
        
        return max(0.01, position_size)  # Minimum 1% position
    
    def _calculate_exit_levels(self, signal: MultiTimeframeSignal,
                             risk_metrics: RiskMetrics,
                             market_context: Dict) -> Tuple[float, float]:
        """Calculate stop loss and take profit levels"""
        
        # Base stop loss using ATR or volatility
        base_stop = risk_metrics.var_95 * 1.5  # 1.5x daily VaR
        
        # Adjust stop loss based on signal strength
        # Stronger signals can have tighter stops
        signal_adjustment = 1.2 - (signal.overall_strength * 0.4)  # 0.8 to 1.2
        stop_loss = base_stop * signal_adjustment
        
        # Ensure minimum and maximum stop loss
        stop_loss = max(0.015, min(0.05, stop_loss))  # 1.5% to 5%
        
        # Calculate take profit based on risk/reward target
        target_risk_reward = 2.0  # Target 2:1 risk/reward
        
        # Adjust target based on market conditions
        market_regime = market_context.get('regime', 'unknown')
        if market_regime == 'trending':
            target_risk_reward = 2.5  # Higher targets in trending markets
        elif market_regime == 'ranging':
            target_risk_reward = 1.5  # Lower targets in ranging markets
        elif market_regime == 'volatile':
            target_risk_reward = 1.8  # Moderate targets in volatile markets
        
        take_profit = stop_loss * target_risk_reward
        
        # Ensure reasonable take profit levels
        take_profit = max(0.03, min(0.15, take_profit))  # 3% to 15%
        
        return stop_loss, take_profit
    
    def _calculate_risk_reward_ratio(self, signal: MultiTimeframeSignal,
                                   stop_loss: float, take_profit: float) -> float:
        """Calculate expected risk/reward ratio"""
        if stop_loss <= 0:
            return 0.0
        
        return take_profit / stop_loss
    
    def _get_market_risk_adjustment(self, market_context: Dict) -> float:
        """Get risk adjustment factor based on market conditions"""
        regime = market_context.get('regime', 'unknown')
        base_adjustment = self.market_risk_adjustments.get(regime, 0.7)
        
        # Additional adjustments
        volatility_regime = market_context.get('volatility_regime', 'normal')
        if volatility_regime == 'high':
            base_adjustment *= 0.7
        elif volatility_regime == 'low':
            base_adjustment *= 1.1
        
        # Market breadth adjustment
        market_breadth = market_context.get('market_breadth', 50)
        if market_breadth < 30:  # Poor breadth
            base_adjustment *= 0.8
        elif market_breadth > 70:  # Good breadth
            base_adjustment *= 1.1
        
        return max(0.3, min(1.5, base_adjustment))
    
    def _get_default_risk_metrics(self, symbol: str) -> RiskMetrics:
        """Get default risk metrics when calculation fails"""
        return RiskMetrics(
            symbol=symbol,
            volatility=0.25,
            beta=1.0,
            max_drawdown=0.20,
            sharpe_ratio=0.5,
            var_95=0.03,
            correlation_spy=0.7
        )
    
    def _get_default_risk_assessment(self) -> Dict:
        """Get default risk assessment when calculation fails"""
        return {
            'position_size': 0.02,  # 2% position
            'stop_loss': 0.02,      # 2% stop loss
            'take_profit': 0.04,    # 4% take profit
            'max_risk': 0.0004,     # 0.04% portfolio risk
            'risk_reward_ratio': 2.0,
            'volatility': 0.25,
            'beta': 1.0,
            'confidence': 50.0,
            'market_adjustment': 1.0,
            'var_95': 0.03
        }
    
    def calculate_portfolio_risk(self, positions: List[Dict]) -> Dict:
        """Calculate overall portfolio risk"""
        try:
            total_risk = 0.0
            total_exposure = 0.0
            correlations = []
            
            for position in positions:
                position_risk = position.get('max_risk', 0.0)
                position_size = position.get('position_size', 0.0)
                
                total_risk += position_risk
                total_exposure += position_size
                
                # Get correlation for diversification calculation
                symbol = position.get('symbol', '')
                risk_metrics = self.risk_cache.get(symbol)
                if risk_metrics:
                    correlations.append(risk_metrics.correlation_spy)
            
            # Calculate diversification benefit
            avg_correlation = sum(correlations) / len(correlations) if correlations else 0.7
            diversification_factor = 1.0 - (avg_correlation * 0.3)  # Up to 30% risk reduction
            
            adjusted_risk = total_risk * diversification_factor
            
            return {
                'total_risk': total_risk,
                'adjusted_risk': adjusted_risk,
                'total_exposure': total_exposure,
                'diversification_factor': diversification_factor,
                'risk_utilization': adjusted_risk / self.max_portfolio_risk,
                'position_count': len(positions),
                'avg_correlation': avg_correlation
            }
            
        except Exception as e:
            logger.error(f"Error calculating portfolio risk: {e}")
            return {
                'total_risk': 0.0,
                'adjusted_risk': 0.0,
                'total_exposure': 0.0,
                'diversification_factor': 1.0,
                'risk_utilization': 0.0,
                'position_count': 0,
                'avg_correlation': 0.7
            }
    
    def update_risk_parameters(self, portfolio_performance: Dict):
        """Update risk parameters based on portfolio performance"""
        try:
            # Adjust portfolio risk based on recent performance
            recent_sharpe = portfolio_performance.get('sharpe_ratio', 0.5)
            recent_max_dd = portfolio_performance.get('max_drawdown', 0.1)
            
            # Increase risk if performing well, decrease if poor performance
            if recent_sharpe > 1.0 and recent_max_dd < 0.05:
                self.portfolio_risk = min(0.03, self.portfolio_risk * 1.1)
            elif recent_sharpe < 0.2 or recent_max_dd > 0.15:
                self.portfolio_risk = max(0.01, self.portfolio_risk * 0.9)
            
            logger.info(f"Updated portfolio risk to {self.portfolio_risk:.3f}")
            
        except Exception as e:
            logger.error(f"Error updating risk parameters: {e}")
    
    def is_ready(self) -> bool:
        """Check if risk engine is ready"""
        return True  # Always ready with default parameters
    
    def get_risk_summary(self) -> Dict:
        """Get risk engine summary"""
        return {
            'portfolio_risk_per_trade': self.portfolio_risk,
            'max_portfolio_risk': self.max_portfolio_risk,
            'max_single_position': self.max_single_position,
            'cached_symbols': len(self.risk_cache),
            'lookback_days': self.lookback_days,
            'market_adjustments': self.market_risk_adjustments
        }
