"""
AI-Enhanced Target Achievement Strategy
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
import logging
from datetime import datetime, timedelta
from dataclasses import dataclass
import json

from .profit_target_config import ProfitTarget, TargetStatus
from ..ai.signal_enhancer import AIEnhancedSignal

logger = logging.getLogger(__name__)

@dataclass
class TargetOptimizedTrade:
    """Trade optimized for target achievement"""
    symbol: str
    signal: AIEnhancedSignal
    optimized_position_size: float
    target_contribution: float  # Expected contribution to target
    risk_score: float
    timing_score: float
    priority_score: float
    execution_window: Tuple[datetime, datetime]

@dataclass
class MarketWindow:
    """Optimal trading window analysis"""
    start_time: datetime
    end_time: datetime
    expected_volatility: float
    historical_success_rate: float
    market_regime: str
    confidence_score: float

class AITargetAchievementStrategy:
    """AI-powered strategy for optimizing target achievement"""
    
    def __init__(self, data_manager, risk_engine):
        self.data_manager = data_manager
        self.risk_engine = risk_engine
        
        # Historical performance data for ML
        self.trade_history = []
        self.target_achievement_data = []
        
        # Strategy parameters
        self.strategy_params = {
            'conservative_multiplier': 0.6,    # Reduce position size when near target
            'aggressive_multiplier': 1.2,      # Increase when far from target
            'time_pressure_factor': 1.5,       # Increase urgency near deadline
            'market_regime_weights': {
                'trending': 1.2,
                'ranging': 0.8,
                'volatile': 0.6,
                'calm': 1.1
            }
        }
        
        # ML model placeholders (would be actual models in production)
        self.position_size_model = None
        self.timing_model = None
        self.trade_selection_model = None
        
        logger.info("AI Target Achievement Strategy initialized")
    
    def optimize_for_target(self, signal: AIEnhancedSignal, 
                          target: ProfitTarget,
                          current_progress: Dict,
                          market_conditions: Dict) -> TargetOptimizedTrade:
        """
        Optimize a trading signal for target achievement
        
        Args:
            signal: Enhanced TTM Squeeze signal
            target: Active profit target
            current_progress: Current progress toward target
            market_conditions: Current market conditions
            
        Returns:
            Target-optimized trade recommendation
        """
        try:
            # Calculate optimal position size
            optimized_size = self._calculate_target_optimized_position_size(
                signal, target, current_progress, market_conditions
            )
            
            # Calculate expected target contribution
            target_contribution = self._estimate_target_contribution(
                signal, optimized_size, target, market_conditions
            )
            
            # Calculate risk score
            risk_score = self._calculate_target_risk_score(
                signal, optimized_size, target, current_progress
            )
            
            # Calculate timing score
            timing_score = self._calculate_timing_score(
                signal, target, current_progress, market_conditions
            )
            
            # Calculate overall priority score
            priority_score = self._calculate_priority_score(
                signal, target_contribution, risk_score, timing_score, current_progress
            )
            
            # Determine optimal execution window
            execution_window = self._determine_execution_window(
                signal, target, market_conditions
            )
            
            return TargetOptimizedTrade(
                symbol=signal.original_signal.symbol,
                signal=signal,
                optimized_position_size=optimized_size,
                target_contribution=target_contribution,
                risk_score=risk_score,
                timing_score=timing_score,
                priority_score=priority_score,
                execution_window=execution_window
            )
            
        except Exception as e:
            logger.error(f"Error optimizing signal for target: {e}")
            return self._create_fallback_trade(signal)
    
    def _calculate_target_optimized_position_size(self, signal: AIEnhancedSignal,
                                                target: ProfitTarget,
                                                current_progress: Dict,
                                                market_conditions: Dict) -> float:
        """Calculate position size optimized for target achievement"""
        try:
            # Base position size from AI risk assessment
            base_size = signal.suggested_position_size
            
            # Progress-based adjustment
            completion_pct = current_progress.get('completion_percentage', 0)
            remaining_amount = current_progress.get('remaining_amount', target.target_value)
            
            # Distance to target factor
            if completion_pct < 50:
                # Far from target - can be more aggressive
                distance_factor = self.strategy_params['aggressive_multiplier']
            elif completion_pct >= target.conservative_threshold * 100:
                # Near target - be conservative
                distance_factor = self.strategy_params['conservative_multiplier']
            else:
                # Middle ground - linear interpolation
                progress_ratio = (completion_pct - 50) / (target.conservative_threshold * 100 - 50)
                distance_factor = (self.strategy_params['aggressive_multiplier'] * (1 - progress_ratio) +
                                 self.strategy_params['conservative_multiplier'] * progress_ratio)
            
            # Time pressure factor
            time_remaining = current_progress.get('time_remaining')
            time_factor = 1.0
            if time_remaining and isinstance(time_remaining, timedelta):
                hours_remaining = time_remaining.total_seconds() / 3600
                if hours_remaining < 2:  # Less than 2 hours
                    time_factor = self.strategy_params['time_pressure_factor']
                elif hours_remaining < 6:  # Less than 6 hours
                    time_factor = 1.2
            
            # Market regime factor
            market_regime = market_conditions.get('regime', 'unknown')
            regime_factor = self.strategy_params['market_regime_weights'].get(market_regime, 1.0)
            
            # Signal strength factor
            signal_factor = signal.final_score / 100  # Normalize to 0-1
            
            # Calculate optimized size
            optimized_size = (base_size * distance_factor * time_factor * 
                            regime_factor * signal_factor)
            
            # Apply target-specific constraints
            optimized_size = max(target.min_trade_size_pct, 
                               min(target.max_trade_size_pct, optimized_size))
            
            # Ensure we don't risk more than remaining amount allows
            max_risk_for_remaining = self._calculate_max_risk_for_remaining(
                remaining_amount, signal, target
            )
            
            if optimized_size > max_risk_for_remaining:
                optimized_size = max_risk_for_remaining
            
            return optimized_size
            
        except Exception as e:
            logger.warning(f"Error calculating optimized position size: {e}")
            return signal.suggested_position_size
    
    def _estimate_target_contribution(self, signal: AIEnhancedSignal,
                                    position_size: float,
                                    target: ProfitTarget,
                                    market_conditions: Dict) -> float:
        """Estimate how much this trade will contribute to target"""
        try:
            # Expected return based on signal strength and market conditions
            base_expected_return = signal.suggested_take_profit
            
            # Adjust for market conditions
            market_regime = market_conditions.get('regime', 'unknown')
            if market_regime == 'trending':
                expected_return = base_expected_return * 1.2
            elif market_regime == 'volatile':
                expected_return = base_expected_return * 0.8
            else:
                expected_return = base_expected_return
            
            # Calculate expected dollar contribution
            # This is simplified - in production would use more sophisticated models
            account_value = 100000  # Would get actual account value
            position_value = account_value * position_size
            expected_contribution = position_value * expected_return
            
            # Adjust for win probability
            win_probability = self._estimate_win_probability(signal, market_conditions)
            risk_adjusted_contribution = expected_contribution * win_probability
            
            return risk_adjusted_contribution
            
        except Exception as e:
            logger.warning(f"Error estimating target contribution: {e}")
            return 0.0
    
    def _calculate_target_risk_score(self, signal: AIEnhancedSignal,
                                   position_size: float,
                                   target: ProfitTarget,
                                   current_progress: Dict) -> float:
        """Calculate risk score specific to target achievement"""
        try:
            # Base risk from signal
            base_risk = signal.max_risk_per_trade
            
            # Position size risk
            size_risk = position_size / target.max_trade_size_pct
            
            # Progress risk - higher risk if we're behind schedule
            completion_pct = current_progress.get('completion_percentage', 0)
            time_remaining = current_progress.get('time_remaining')
            
            schedule_risk = 1.0
            if time_remaining and isinstance(time_remaining, timedelta):
                hours_remaining = time_remaining.total_seconds() / 3600
                expected_completion = min(100, (24 - hours_remaining) / 24 * 100)  # Assuming daily target
                if completion_pct < expected_completion:
                    schedule_risk = 1.5  # Behind schedule
            
            # Drawdown risk
            current_drawdown = current_progress.get('current_drawdown', 0)
            drawdown_risk = 1.0 + (current_drawdown / target.max_drawdown_pct)
            
            # Combined risk score (0-100, lower is better)
            combined_risk = (base_risk * size_risk * schedule_risk * drawdown_risk) * 100
            
            return min(100, combined_risk)
            
        except Exception as e:
            logger.warning(f"Error calculating target risk score: {e}")
            return 50.0
    
    def _calculate_timing_score(self, signal: AIEnhancedSignal,
                              target: ProfitTarget,
                              current_progress: Dict,
                              market_conditions: Dict) -> float:
        """Calculate timing score for trade execution"""
        try:
            # Base timing from signal
            base_timing = signal.ai_confidence
            
            # Market timing
            market_regime = market_conditions.get('regime', 'unknown')
            volatility = market_conditions.get('volatility_regime', 'normal')
            
            market_timing = 70.0  # Base
            if market_regime == 'trending' and volatility == 'low':
                market_timing = 90.0
            elif market_regime == 'volatile':
                market_timing = 40.0
            
            # Time pressure timing
            time_remaining = current_progress.get('time_remaining')
            time_pressure = 50.0
            if time_remaining and isinstance(time_remaining, timedelta):
                hours_remaining = time_remaining.total_seconds() / 3600
                if hours_remaining < 1:
                    time_pressure = 95.0  # Very urgent
                elif hours_remaining < 3:
                    time_pressure = 80.0  # Urgent
                elif hours_remaining > 12:
                    time_pressure = 30.0  # No rush
            
            # Historical timing analysis
            historical_timing = self._get_historical_timing_score(
                signal.original_signal.symbol, datetime.now().time()
            )
            
            # Weighted combination
            timing_score = (
                base_timing * 0.3 +
                market_timing * 0.3 +
                time_pressure * 0.2 +
                historical_timing * 0.2
            )
            
            return min(100, max(0, timing_score))
            
        except Exception as e:
            logger.warning(f"Error calculating timing score: {e}")
            return 50.0
    
    def _calculate_priority_score(self, signal: AIEnhancedSignal,
                                target_contribution: float,
                                risk_score: float,
                                timing_score: float,
                                current_progress: Dict) -> float:
        """Calculate overall priority score for trade"""
        try:
            # Normalize target contribution (0-100)
            max_contribution = current_progress.get('remaining_amount', 1000)
            contribution_score = min(100, (target_contribution / max_contribution) * 100)
            
            # Signal quality score
            signal_quality = signal.final_score
            
            # Risk-adjusted score (invert risk so higher is better)
            risk_adjusted_score = 100 - risk_score
            
            # Weighted priority calculation
            priority = (
                signal_quality * 0.25 +
                contribution_score * 0.30 +
                risk_adjusted_score * 0.25 +
                timing_score * 0.20
            )
            
            return min(100, max(0, priority))
            
        except Exception as e:
            logger.warning(f"Error calculating priority score: {e}")
            return 50.0
    
    def _determine_execution_window(self, signal: AIEnhancedSignal,
                                  target: ProfitTarget,
                                  market_conditions: Dict) -> Tuple[datetime, datetime]:
        """Determine optimal execution window"""
        try:
            now = datetime.now()
            
            # Default window - next 30 minutes
            start_time = now
            end_time = now + timedelta(minutes=30)
            
            # Adjust based on market conditions
            market_regime = market_conditions.get('regime', 'unknown')
            if market_regime == 'volatile':
                # Shorter window in volatile markets
                end_time = now + timedelta(minutes=15)
            elif market_regime == 'calm':
                # Longer window in calm markets
                end_time = now + timedelta(hours=1)
            
            # Respect target trading hours
            if target.trading_start_time and target.trading_end_time:
                today = now.date()
                target_start = datetime.combine(today, target.trading_start_time)
                target_end = datetime.combine(today, target.trading_end_time)
                
                start_time = max(start_time, target_start)
                end_time = min(end_time, target_end)
            
            return (start_time, end_time)
            
        except Exception as e:
            logger.warning(f"Error determining execution window: {e}")
            now = datetime.now()
            return (now, now + timedelta(minutes=30))
    
    def _calculate_max_risk_for_remaining(self, remaining_amount: float,
                                        signal: AIEnhancedSignal,
                                        target: ProfitTarget) -> float:
        """Calculate maximum position size based on remaining amount"""
        try:
            # Don't risk more than 50% of remaining amount on single trade
            max_risk_amount = remaining_amount * 0.5
            
            # Convert to position size
            stop_loss_pct = signal.suggested_stop_loss
            if stop_loss_pct > 0:
                account_value = 100000  # Would get actual account value
                max_position_value = max_risk_amount / stop_loss_pct
                max_position_size = max_position_value / account_value
                
                return min(target.max_trade_size_pct, max_position_size)
            
            return target.max_trade_size_pct
            
        except Exception as e:
            logger.warning(f"Error calculating max risk for remaining: {e}")
            return target.max_trade_size_pct
    
    def _estimate_win_probability(self, signal: AIEnhancedSignal,
                                market_conditions: Dict) -> float:
        """Estimate win probability for signal"""
        try:
            # Base probability from signal strength
            base_prob = signal.final_score / 100
            
            # Adjust for market conditions
            market_regime = market_conditions.get('regime', 'unknown')
            if market_regime == 'trending':
                market_adjustment = 1.1
            elif market_regime == 'volatile':
                market_adjustment = 0.8
            else:
                market_adjustment = 1.0
            
            # Adjust for signal confidence
            confidence_adjustment = signal.ai_confidence / 100
            
            win_prob = base_prob * market_adjustment * confidence_adjustment
            
            return min(0.95, max(0.05, win_prob))  # Clamp between 5% and 95%
            
        except Exception as e:
            logger.warning(f"Error estimating win probability: {e}")
            return 0.55  # Default 55%
    
    def _get_historical_timing_score(self, symbol: str, current_time) -> float:
        """Get historical timing score for symbol at current time"""
        try:
            # This would analyze historical performance at different times
            # For now, return a reasonable default based on time of day
            
            hour = current_time.hour
            
            # Market open (9:30-10:30) - high activity
            if 9 <= hour <= 10:
                return 85.0
            # Mid-morning (10:30-12:00) - good activity
            elif 10 <= hour <= 12:
                return 75.0
            # Lunch (12:00-14:00) - lower activity
            elif 12 <= hour <= 14:
                return 60.0
            # Afternoon (14:00-16:00) - increasing activity
            elif 14 <= hour <= 16:
                return 80.0
            # After hours
            else:
                return 40.0
                
        except Exception as e:
            logger.warning(f"Error getting historical timing score: {e}")
            return 50.0
    
    def _create_fallback_trade(self, signal: AIEnhancedSignal) -> TargetOptimizedTrade:
        """Create fallback trade when optimization fails"""
        now = datetime.now()
        return TargetOptimizedTrade(
            symbol=signal.original_signal.symbol,
            signal=signal,
            optimized_position_size=signal.suggested_position_size,
            target_contribution=0.0,
            risk_score=50.0,
            timing_score=50.0,
            priority_score=signal.final_score,
            execution_window=(now, now + timedelta(minutes=30))
        )
    
    def analyze_optimal_trading_windows(self, target: ProfitTarget,
                                      historical_data: pd.DataFrame) -> List[MarketWindow]:
        """Analyze optimal trading windows for target achievement"""
        try:
            windows = []
            
            # Analyze different time periods
            time_periods = [
                (9, 30, 10, 30),   # Market open
                (10, 30, 12, 0),   # Mid-morning
                (12, 0, 14, 0),    # Lunch
                (14, 0, 16, 0),    # Afternoon
            ]
            
            for start_hour, start_min, end_hour, end_min in time_periods:
                # Calculate historical success rate for this window
                success_rate = self._calculate_window_success_rate(
                    historical_data, start_hour, start_min, end_hour, end_min
                )
                
                # Calculate expected volatility
                volatility = self._calculate_window_volatility(
                    historical_data, start_hour, start_min, end_hour, end_min
                )
                
                # Determine market regime for this window
                regime = self._determine_window_regime(
                    historical_data, start_hour, start_min, end_hour, end_min
                )
                
                # Calculate confidence score
                confidence = min(100, success_rate * 100 + (1 - volatility) * 50)
                
                # Create window
                now = datetime.now()
                start_time = now.replace(hour=start_hour, minute=start_min, second=0, microsecond=0)
                end_time = now.replace(hour=end_hour, minute=end_min, second=0, microsecond=0)
                
                window = MarketWindow(
                    start_time=start_time,
                    end_time=end_time,
                    expected_volatility=volatility,
                    historical_success_rate=success_rate,
                    market_regime=regime,
                    confidence_score=confidence
                )
                
                windows.append(window)
            
            # Sort by confidence score
            windows.sort(key=lambda w: w.confidence_score, reverse=True)
            
            return windows
            
        except Exception as e:
            logger.error(f"Error analyzing optimal trading windows: {e}")
            return []
    
    def _calculate_window_success_rate(self, data: pd.DataFrame,
                                     start_hour: int, start_min: int,
                                     end_hour: int, end_min: int) -> float:
        """Calculate success rate for specific time window"""
        try:
            # This would analyze actual historical trade data
            # For now, return reasonable estimates based on market knowledge
            
            if start_hour == 9:  # Market open
                return 0.65
            elif start_hour == 10:  # Mid-morning
                return 0.70
            elif start_hour == 12:  # Lunch
                return 0.55
            elif start_hour == 14:  # Afternoon
                return 0.68
            else:
                return 0.60
                
        except Exception:
            return 0.60
    
    def _calculate_window_volatility(self, data: pd.DataFrame,
                                   start_hour: int, start_min: int,
                                   end_hour: int, end_min: int) -> float:
        """Calculate expected volatility for time window"""
        try:
            # Market open is typically most volatile
            if start_hour == 9:
                return 0.8
            elif start_hour == 10:
                return 0.6
            elif start_hour == 12:
                return 0.3
            elif start_hour == 14:
                return 0.5
            else:
                return 0.4
                
        except Exception:
            return 0.5
    
    def _determine_window_regime(self, data: pd.DataFrame,
                               start_hour: int, start_min: int,
                               end_hour: int, end_min: int) -> str:
        """Determine typical market regime for time window"""
        try:
            # This would analyze actual market behavior
            # For now, return typical patterns
            
            if start_hour == 9:  # Market open - often trending
                return 'trending'
            elif start_hour == 10:  # Mid-morning - mixed
                return 'ranging'
            elif start_hour == 12:  # Lunch - typically ranging
                return 'ranging'
            elif start_hour == 14:  # Afternoon - often trending
                return 'trending'
            else:
                return 'ranging'
                
        except Exception:
            return 'ranging'
    
    def update_strategy_performance(self, trade_result: Dict):
        """Update strategy performance based on trade results"""
        try:
            # Record trade result for ML model improvement
            self.trade_history.append({
                'timestamp': datetime.now(),
                'symbol': trade_result.get('symbol'),
                'optimized_size': trade_result.get('position_size'),
                'actual_result': trade_result.get('pnl'),
                'target_contribution': trade_result.get('target_contribution'),
                'market_conditions': trade_result.get('market_conditions', {})
            })
            
            # Keep only recent history (last 1000 trades)
            if len(self.trade_history) > 1000:
                self.trade_history = self.trade_history[-1000:]
            
            # Update strategy parameters based on performance
            self._update_strategy_parameters()
            
        except Exception as e:
            logger.error(f"Error updating strategy performance: {e}")
    
    def _update_strategy_parameters(self):
        """Update strategy parameters based on recent performance"""
        try:
            if len(self.trade_history) < 50:  # Need minimum data
                return
            
            # Analyze recent performance
            recent_trades = self.trade_history[-50:]
            
            # Calculate success rate by position size
            small_trades = [t for t in recent_trades if t['optimized_size'] < 0.05]
            large_trades = [t for t in recent_trades if t['optimized_size'] >= 0.05]
            
            if small_trades and large_trades:
                small_success = len([t for t in small_trades if t['actual_result'] > 0]) / len(small_trades)
                large_success = len([t for t in large_trades if t['actual_result'] > 0]) / len(large_trades)
                
                # Adjust multipliers based on performance
                if small_success > large_success + 0.1:  # Small trades performing better
                    self.strategy_params['conservative_multiplier'] = min(0.8, 
                        self.strategy_params['conservative_multiplier'] + 0.05)
                elif large_success > small_success + 0.1:  # Large trades performing better
                    self.strategy_params['aggressive_multiplier'] = min(1.5,
                        self.strategy_params['aggressive_multiplier'] + 0.05)
            
            logger.debug("Strategy parameters updated based on performance")
            
        except Exception as e:
            logger.error(f"Error updating strategy parameters: {e}")
    
    def get_strategy_status(self) -> Dict:
        """Get current strategy status and parameters"""
        return {
            'strategy_params': self.strategy_params,
            'trade_history_count': len(self.trade_history),
            'target_achievement_count': len(self.target_achievement_data),
            'models_trained': {
                'position_size_model': self.position_size_model is not None,
                'timing_model': self.timing_model is not None,
                'trade_selection_model': self.trade_selection_model is not None
            }
        }
