"""
Consolidated Web Interface Module for TTM Squeeze Trading System
Combines Flask app, dashboard, and API endpoints with optimized timeframe focus
"""

from flask import Flask, render_template, jsonify, request
from flask_cors import CORS
import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any

logger = logging.getLogger(__name__)

def create_app(data_manager, trading_manager, scanner, ai_intelligence=None):
    """Create and configure Flask application"""

    import os
    # Get correct paths relative to this file
    current_dir = os.path.dirname(__file__)
    template_dir = os.path.join(current_dir, 'ui', 'templates')
    static_dir = os.path.join(current_dir, 'ui', 'static')

    app = Flask(__name__,
                template_folder=template_dir,
                static_folder=static_dir)
    
    CORS(app)
    app.config['SECRET_KEY'] = 'ttm-squeeze-trading-system'
    
    # ============================================================================
    # DASHBOARD ROUTES
    # ============================================================================
    
    @app.route('/')
    def index():
        """Main dashboard"""
        return render_template('index.html')
    
    @app.route('/pro')
    def professional_dashboard():
        """Professional trading dashboard"""
        return render_template('professional_dashboard.html')
    
    # ============================================================================
    # SCANNER API ENDPOINTS
    # ============================================================================
    
    @app.route('/api/scanner/alerts')
    def get_scanner_alerts():
        """Get current TTM Squeeze alerts (5Min and 15Min only)"""
        try:
            limit = request.args.get('limit', 50, type=int)
            timeframe_filter = request.args.get('timeframe', None)
            
            alerts = scanner.get_recent_alerts(limit)
            
            # Filter by timeframe if specified
            if timeframe_filter:
                alerts = [
                    alert for alert in alerts 
                    if timeframe_filter in alert.get('timeframes', [])
                ]
            
            # Format alerts for API response
            formatted_alerts = []
            for alert in alerts:
                formatted_alert = {
                    'id': alert.get('id'),
                    'symbol': alert.get('symbol'),
                    'signal_type': alert.get('signal_type'),
                    'timeframes': alert.get('timeframes', []),  # Only 5Min and 15Min
                    'confirmations': alert.get('confirmations', 0),
                    'required_confirmations': 1,  # Reduced requirement
                    'price': alert.get('price', 0),
                    'confidence': alert.get('confidence', 0),
                    'ai_score': alert.get('ai_score', 0),
                    'risk_level': alert.get('risk_level', 'moderate'),
                    'timestamp': alert.get('timestamp').isoformat() if alert.get('timestamp') else None,
                    'reasoning': alert.get('reasoning', [])
                }
                formatted_alerts.append(formatted_alert)
            
            return jsonify({
                'success': True,
                'alerts': formatted_alerts,
                'total_count': len(formatted_alerts),
                'timeframes_supported': ['5Min', '15Min'],  # Only intraday
                'timestamp': datetime.now().isoformat()
            })
            
        except Exception as e:
            logger.error(f"Error getting scanner alerts: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500
    
    @app.route('/api/scanner/status')
    def get_scanner_status():
        """Get scanner status and statistics"""
        try:
            stats = scanner.get_scanner_stats()
            
            return jsonify({
                'success': True,
                'status': stats,
                'timeframes': ['5Min', '15Min'],  # Optimized timeframes
                'scan_focus': 'intraday_patterns',
                'timestamp': datetime.now().isoformat()
            })
            
        except Exception as e:
            logger.error(f"Error getting scanner status: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500
    
    @app.route('/api/scanner/symbol/<symbol>')
    def get_symbol_analysis(symbol):
        """Get detailed analysis for specific symbol"""
        try:
            symbol = symbol.upper()
            
            # Get alerts for this symbol
            symbol_alerts = scanner.get_alerts_by_symbol(symbol, 10)
            
            # Get current quote
            quote = None
            if data_manager:
                quote_data = asyncio.run(data_manager.get_real_time_quote(symbol))
                if quote_data:
                    quote = {
                        'price': quote_data.price,
                        'bid': quote_data.bid,
                        'ask': quote_data.ask,
                        'timestamp': quote_data.timestamp.isoformat()
                    }
            
            return jsonify({
                'success': True,
                'symbol': symbol,
                'alerts': symbol_alerts,
                'current_quote': quote,
                'timeframes_analyzed': ['5Min', '15Min'],
                'timestamp': datetime.now().isoformat()
            })
            
        except Exception as e:
            logger.error(f"Error getting symbol analysis: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500
    
    # ============================================================================
    # TRADING API ENDPOINTS
    # ============================================================================
    
    @app.route('/api/trading/account')
    def get_account_info():
        """Get trading account information"""
        try:
            if not trading_manager:
                return jsonify({'success': False, 'error': 'Trading not available'}), 503
            
            account = asyncio.run(trading_manager.get_account())
            
            if account:
                account_info = {
                    'id': account.id,
                    'status': account.status,
                    'buying_power': account.buying_power,
                    'portfolio_value': account.portfolio_value,
                    'cash': account.cash,
                    'equity': account.equity,
                    'day_change': account.equity - account.last_equity,
                    'day_change_percent': ((account.equity - account.last_equity) / account.last_equity * 100) if account.last_equity > 0 else 0,
                    'daytrade_count': account.daytrade_count,
                    'multiplier': account.multiplier
                }
                
                return jsonify({
                    'success': True,
                    'account': account_info,
                    'timestamp': datetime.now().isoformat()
                })
            else:
                return jsonify({'success': False, 'error': 'Unable to get account info'}), 500
                
        except Exception as e:
            logger.error(f"Error getting account info: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500
    
    @app.route('/api/trading/positions')
    def get_positions():
        """Get current trading positions"""
        try:
            if not trading_manager:
                return jsonify({'success': False, 'error': 'Trading not available'}), 503
            
            positions = asyncio.run(trading_manager.get_positions())
            
            formatted_positions = []
            for pos in positions:
                formatted_pos = {
                    'symbol': pos.symbol,
                    'quantity': pos.quantity,
                    'side': pos.side,
                    'avg_entry_price': pos.avg_entry_price,
                    'current_price': pos.current_price,
                    'market_value': pos.market_value,
                    'unrealized_pnl': pos.unrealized_pnl,
                    'unrealized_pnl_percent': pos.unrealized_pnl_percent
                }
                formatted_positions.append(formatted_pos)
            
            return jsonify({
                'success': True,
                'positions': formatted_positions,
                'total_positions': len(formatted_positions),
                'timestamp': datetime.now().isoformat()
            })
            
        except Exception as e:
            logger.error(f"Error getting positions: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500
    
    @app.route('/api/trading/orders')
    def get_orders():
        """Get trading orders"""
        try:
            if not trading_manager:
                return jsonify({'success': False, 'error': 'Trading not available'}), 503
            
            status_filter = request.args.get('status', 'all')
            limit = request.args.get('limit', 50, type=int)
            
            orders = asyncio.run(trading_manager.get_orders(status_filter, limit))
            
            formatted_orders = []
            for order in orders:
                formatted_order = {
                    'id': order.id,
                    'symbol': order.symbol,
                    'side': order.side,
                    'quantity': order.quantity,
                    'type': order.order_type,
                    'status': order.status,
                    'filled_quantity': order.filled_quantity,
                    'avg_fill_price': order.avg_fill_price,
                    'timestamp': order.timestamp.isoformat()
                }
                formatted_orders.append(formatted_order)
            
            return jsonify({
                'success': True,
                'orders': formatted_orders,
                'total_orders': len(formatted_orders),
                'timestamp': datetime.now().isoformat()
            })
            
        except Exception as e:
            logger.error(f"Error getting orders: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500
    
    # ============================================================================
    # AI API ENDPOINTS
    # ============================================================================
    
    @app.route('/api/ai/status')
    def get_ai_status():
        """Get AI system status"""
        try:
            ai_available = ai_intelligence is not None
            
            return jsonify({
                'success': True,
                'ai_available': ai_available,
                'components': {
                    'chat_assistant': ai_available,
                    'risk_manager': ai_available,
                    'signal_enhancer': ai_available,
                    'dashboard_intelligence': ai_available,
                    'automated_trading': ai_available
                },
                'timeframe_focus': ['5Min', '15Min'],
                'optimization_status': 'streamlined_for_intraday',
                'timestamp': datetime.now().isoformat()
            })
            
        except Exception as e:
            logger.error(f"Error getting AI status: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500
    
    @app.route('/api/ai/chat', methods=['POST'])
    def ai_chat():
        """AI chat assistant endpoint"""
        try:
            if not ai_intelligence:
                return jsonify({'success': False, 'error': 'AI not available'}), 503
            
            data = request.get_json()
            message = data.get('message', '')
            user_id = data.get('user_id', 'default')
            
            if not message:
                return jsonify({'success': False, 'error': 'Message required'}), 400
            
            # Process message with AI
            response = f"AI Assistant: I received your message '{message}'. The system is now optimized for 5Min and 15Min intraday TTM Squeeze patterns. I can help you analyze current signals, assess risks, and provide trading insights focused on short-term opportunities."
            
            return jsonify({
                'success': True,
                'response': response,
                'type': 'success',
                'focus': 'intraday_patterns',
                'timeframes': ['5Min', '15Min'],
                'timestamp': datetime.now().isoformat()
            })
            
        except Exception as e:
            logger.error(f"Error in AI chat: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500
    
    # ============================================================================
    # DATA API ENDPOINTS
    # ============================================================================
    
    @app.route('/api/data/quote/<symbol>')
    def get_real_time_quote(symbol):
        """Get real-time quote for symbol"""
        try:
            symbol = symbol.upper()
            
            if not data_manager:
                return jsonify({'success': False, 'error': 'Data manager not available'}), 503
            
            quote = asyncio.run(data_manager.get_real_time_quote(symbol))
            
            if quote:
                quote_data = {
                    'symbol': quote.symbol,
                    'price': quote.price,
                    'bid': quote.bid,
                    'ask': quote.ask,
                    'volume': quote.volume,
                    'timestamp': quote.timestamp.isoformat()
                }
                
                return jsonify({
                    'success': True,
                    'quote': quote_data,
                    'timestamp': datetime.now().isoformat()
                })
            else:
                return jsonify({'success': False, 'error': 'Quote not available'}), 404
                
        except Exception as e:
            logger.error(f"Error getting quote for {symbol}: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500
    
    @app.route('/api/data/universe')
    def get_stock_universe():
        """Get current stock universe"""
        try:
            if not data_manager:
                return jsonify({'success': False, 'error': 'Data manager not available'}), 503
            
            universe = data_manager.get_stock_universe()
            
            return jsonify({
                'success': True,
                'universe': universe,
                'total_stocks': len(universe),
                'focus': 'intraday_trading',
                'timeframes': ['5Min', '15Min'],
                'timestamp': datetime.now().isoformat()
            })
            
        except Exception as e:
            logger.error(f"Error getting stock universe: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500
    
    # ============================================================================
    # SYSTEM API ENDPOINTS
    # ============================================================================
    
    @app.route('/api/system/status')
    def get_system_status():
        """Get overall system status"""
        try:
            # Check component status
            data_status = data_manager is not None
            trading_status = trading_manager is not None
            scanner_status = scanner is not None and scanner.is_running
            ai_status = ai_intelligence is not None
            
            status = {
                'system_online': True,
                'components': {
                    'data_manager': data_status,
                    'trading_manager': trading_status,
                    'scanner': scanner_status,
                    'ai_intelligence': ai_status
                },
                'optimization': {
                    'timeframes': ['5Min', '15Min'],
                    'focus': 'intraday_patterns',
                    'file_count': 'streamlined',
                    'performance': 'optimized'
                },
                'scanner_stats': scanner.get_scanner_stats() if scanner else {},
                'timestamp': datetime.now().isoformat()
            }
            
            return jsonify({
                'success': True,
                'status': status
            })
            
        except Exception as e:
            logger.error(f"Error getting system status: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500
    
    @app.route('/api/system/config')
    def get_system_config():
        """Get system configuration"""
        try:
            config = {
                'timeframes': ['5Min', '15Min'],
                'scan_interval': 30,
                'max_alerts': 1000,
                'focus': 'intraday_ttm_squeeze',
                'optimization_level': 'high_performance',
                'file_structure': 'consolidated',
                'ai_enabled': ai_intelligence is not None,
                'trading_enabled': trading_manager is not None
            }
            
            return jsonify({
                'success': True,
                'config': config,
                'timestamp': datetime.now().isoformat()
            })
            
        except Exception as e:
            logger.error(f"Error getting system config: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500
    
    # ============================================================================
    # ERROR HANDLERS
    # ============================================================================
    
    @app.errorhandler(404)
    def not_found(error):
        return jsonify({'success': False, 'error': 'Endpoint not found'}), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        return jsonify({'success': False, 'error': 'Internal server error'}), 500
    
    return app
