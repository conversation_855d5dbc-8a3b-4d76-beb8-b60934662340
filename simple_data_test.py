#!/usr/bin/env python3
"""
Simple test to check if we can get any data at all
"""

import os
from datetime import datetime, timedelta
import alpaca_trade_api as tradeapi
import requests

# Test Alpaca directly
print("Testing Alpaca API directly...")
try:
    api = tradeapi.REST(
        'PK2O4NB71EQDMMENX77L',
        'HLv1oA7hH3Yah59LElAayj17pncG5KmMTNKEoW8j',
        'https://paper-api.alpaca.markets',
        api_version='v2'
    )
    
    # Test account connection
    account = api.get_account()
    print(f"✓ Alpaca account connected: {account.id}")
    print(f"  Buying power: ${account.buying_power}")
    
    # Test getting bars for AAPL
    end_date = datetime.now()
    start_date = end_date - timedelta(days=30)
    
    print(f"Requesting AAPL bars from {start_date.date()} to {end_date.date()}")
    
    bars = api.get_bars(
        'AAPL',
        tradeapi.TimeFrame(1, tradeapi.TimeFrameUnit.Day),
        start=start_date.strftime('%Y-%m-%dT%H:%M:%SZ'),
        end=end_date.strftime('%Y-%m-%dT%H:%M:%SZ'),
        adjustment='raw'
    )
    
    if bars:
        print(f"✓ Got {len(bars)} bars for AAPL")
        print("Latest 3 bars:")
        for bar in bars[-3:]:
            print(f"  {bar.t}: O={bar.o} H={bar.h} L={bar.l} C={bar.c} V={bar.v}")
    else:
        print("✗ No bars returned for AAPL")
        
except Exception as e:
    print(f"✗ Alpaca error: {e}")

# Test FMP directly
print("\nTesting FMP API directly...")
try:
    api_key = 'K63wnAbUDHNPICjzeq3JcEG1vi8Q2oz7'
    
    # Test getting daily data for AAPL
    url = f"https://financialmodelingprep.com/api/v3/historical-price-full/AAPL?apikey={api_key}&limit=20"
    
    print(f"Requesting: {url}")
    response = requests.get(url, timeout=30)
    
    if response.status_code == 200:
        data = response.json()
        if 'historical' in data and data['historical']:
            print(f"✓ Got {len(data['historical'])} records for AAPL")
            print("Latest 3 records:")
            for record in data['historical'][:3]:
                print(f"  {record['date']}: O={record['open']} H={record['high']} L={record['low']} C={record['close']} V={record['volume']}")
        else:
            print(f"✗ No historical data in response: {data}")
    else:
        print(f"✗ FMP API error: {response.status_code} - {response.text}")
        
except Exception as e:
    print(f"✗ FMP error: {e}")

print("\nData provider test complete.")
