#!/usr/bin/env python3
"""
TTM Squeeze Scanner Debug Test Script
Tests the scanner functionality with known symbols to verify it's working correctly.
"""

import sys
import asyncio
import logging
from pathlib import Path

# Add src directory to Python path
sys.path.append(str(Path(__file__).parent / 'src'))

from config import Config
from src.data.data_manager import DataManager
from src.scanner.real_time_scanner import RealTimeScanner
from src.indicators.multi_timeframe import MultiTimeframeAnalyzer
from src.utils.logger import setup_logging

def test_data_retrieval():
    """Test basic data retrieval functionality"""
    print("🔍 Testing Data Retrieval...")
    
    data_manager = DataManager()
    test_symbols = ['AAPL', 'MSFT', 'TSLA', 'SPY', 'QQQ']
    
    for symbol in test_symbols:
        print(f"  Testing {symbol}...")
        
        # Test different timeframes
        for timeframe in ['15Min', '5Min', '30Min']:
            try:
                data = data_manager.get_historical_data(symbol, timeframe, periods=50)
                if data is not None and len(data) > 0:
                    print(f"    ✅ {timeframe}: {len(data)} bars retrieved")
                else:
                    print(f"    ❌ {timeframe}: No data retrieved")
            except Exception as e:
                print(f"    ❌ {timeframe}: Error - {e}")
    
    print()

def test_ttm_squeeze_analysis():
    """Test TTM Squeeze analysis on specific symbols"""
    print("🎯 Testing TTM Squeeze Analysis...")
    
    analyzer = MultiTimeframeAnalyzer()
    data_manager = DataManager()
    
    test_symbols = ['AAPL', 'MSFT', 'TSLA']
    timeframes = ['15Min', '5Min', '30Min']
    
    for symbol in test_symbols:
        print(f"  Analyzing {symbol}...")
        
        try:
            # Get data for all timeframes
            data_dict = {}
            for timeframe in timeframes:
                data = data_manager.get_historical_data(symbol, timeframe, periods=100)
                if data is not None and len(data) >= 30:
                    data_dict[timeframe] = data
                    print(f"    ✅ {timeframe}: {len(data)} bars")
                else:
                    print(f"    ❌ {timeframe}: Insufficient data")
            
            if len(data_dict) >= 2:
                # Analyze with multi-timeframe analyzer
                signal = analyzer.analyze_symbol(symbol, data_dict)
                
                if signal:
                    print(f"    🎯 Signal found!")
                    print(f"       Entry Recommendation: {signal.entry_recommendation}")
                    print(f"       Signal Strength: {signal.signal_strength:.2f}")
                    print(f"       Timeframe Confirmations: {signal.timeframe_confirmations}")
                else:
                    print(f"    ⚪ No signal detected")
            else:
                print(f"    ❌ Insufficient timeframe data")
                
        except Exception as e:
            print(f"    ❌ Analysis error: {e}")
    
    print()

def test_scanner_manual():
    """Test manual scanning of specific symbols"""
    print("🔍 Testing Manual Scanner...")
    
    scanner = RealTimeScanner()
    test_symbols = ['AAPL', 'MSFT', 'TSLA', 'SPY']
    
    for symbol in test_symbols:
        print(f"  Manually scanning {symbol}...")
        
        try:
            signal = scanner.manual_scan_symbol(symbol)
            
            if signal:
                print(f"    🎯 Signal detected!")
                print(f"       Entry: {signal.entry_recommendation}")
                print(f"       Strength: {signal.signal_strength:.2f}")
            else:
                print(f"    ⚪ No signal")
                
        except Exception as e:
            print(f"    ❌ Error: {e}")
    
    print()

def test_universe_refresh():
    """Test universe refresh functionality"""
    print("🌍 Testing Universe Refresh...")
    
    scanner = RealTimeScanner()
    
    try:
        # Force universe refresh
        scanner._refresh_universe_if_needed()
        
        if scanner.scan_universe:
            print(f"  ✅ Universe refreshed with {len(scanner.scan_universe)} symbols")
            print(f"  Sample symbols: {scanner.scan_universe[:10]}")
        else:
            print(f"  ❌ Universe is empty after refresh")
            
    except Exception as e:
        print(f"  ❌ Universe refresh error: {e}")
    
    print()

def test_scanner_should_scan():
    """Test scanner conditions"""
    print("⚙️ Testing Scanner Conditions...")
    
    scanner = RealTimeScanner()
    
    # Test market hours check
    market_open = scanner.data_manager.is_market_open()
    print(f"  Market Open: {market_open}")
    print(f"  Market Hours Only: {scanner.market_hours_only}")
    
    # Force universe refresh to have symbols
    scanner._refresh_universe_if_needed()
    print(f"  Universe Size: {len(scanner.scan_universe)}")
    
    should_scan = scanner._should_scan()
    print(f"  Should Scan: {should_scan}")
    
    print()

def main():
    """Main test function"""
    print("🚀 TTM Squeeze Scanner Debug Test")
    print("=" * 50)
    
    # Setup logging
    setup_logging()
    
    # Run tests
    test_data_retrieval()
    test_ttm_squeeze_analysis()
    test_scanner_manual()
    test_universe_refresh()
    test_scanner_should_scan()
    
    print("✅ Debug tests completed!")

if __name__ == "__main__":
    main()
