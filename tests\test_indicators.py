"""
Test suite for TTM Squeeze indicators
"""
import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent.parent / 'src'))

from indicators.technical_indicators import TechnicalIndicators
from indicators.ttm_squeeze import TTMSqueeze

class TestTechnicalIndicators:
    """Test technical indicators"""
    
    @pytest.fixture
    def sample_data(self):
        """Create sample OHLCV data"""
        dates = pd.date_range(start='2023-01-01', periods=100, freq='D')
        np.random.seed(42)
        
        # Generate realistic price data
        close_prices = 100 + np.cumsum(np.random.randn(100) * 0.5)
        high_prices = close_prices + np.random.rand(100) * 2
        low_prices = close_prices - np.random.rand(100) * 2
        open_prices = close_prices + np.random.randn(100) * 0.5
        volumes = np.random.randint(1000000, 5000000, 100)
        
        return pd.DataFrame({
            'timestamp': dates,
            'open': open_prices,
            'high': high_prices,
            'low': low_prices,
            'close': close_prices,
            'volume': volumes
        }).set_index('timestamp')
    
    def test_sma(self, sample_data):
        """Test Simple Moving Average"""
        sma = TechnicalIndicators.sma(sample_data['close'], 20)
        
        assert len(sma) == len(sample_data)
        assert not sma.iloc[19:].isna().any()  # Should have values after period
        assert sma.iloc[:19].isna().all()  # Should be NaN before period
    
    def test_ema(self, sample_data):
        """Test Exponential Moving Average"""
        ema = TechnicalIndicators.ema(sample_data['close'], 20)
        
        assert len(ema) == len(sample_data)
        assert not ema.iloc[19:].isna().any()
    
    def test_atr(self, sample_data):
        """Test Average True Range"""
        atr = TechnicalIndicators.atr(
            sample_data['high'], 
            sample_data['low'], 
            sample_data['close'], 
            14
        )
        
        assert len(atr) == len(sample_data)
        assert (atr >= 0).all()  # ATR should be positive
        assert not atr.iloc[14:].isna().any()
    
    def test_bollinger_bands(self, sample_data):
        """Test Bollinger Bands"""
        upper, middle, lower = TechnicalIndicators.bollinger_bands(
            sample_data['close'], 20, 2.0
        )
        
        assert len(upper) == len(sample_data)
        assert len(middle) == len(sample_data)
        assert len(lower) == len(sample_data)
        
        # Upper should be above middle, middle above lower
        valid_data = ~upper.isna()
        assert (upper[valid_data] >= middle[valid_data]).all()
        assert (middle[valid_data] >= lower[valid_data]).all()
    
    def test_keltner_channels(self, sample_data):
        """Test Keltner Channels"""
        upper, middle, lower = TechnicalIndicators.keltner_channels(
            sample_data['high'],
            sample_data['low'], 
            sample_data['close'],
            20, 1.5
        )
        
        assert len(upper) == len(sample_data)
        assert len(middle) == len(sample_data)
        assert len(lower) == len(sample_data)
        
        # Upper should be above middle, middle above lower
        valid_data = ~upper.isna()
        assert (upper[valid_data] >= middle[valid_data]).all()
        assert (middle[valid_data] >= lower[valid_data]).all()
    
    def test_macd(self, sample_data):
        """Test MACD"""
        macd_line, signal_line, histogram = TechnicalIndicators.macd(
            sample_data['close'], 12, 26, 9
        )
        
        assert len(macd_line) == len(sample_data)
        assert len(signal_line) == len(sample_data)
        assert len(histogram) == len(sample_data)
        
        # Histogram should equal macd_line - signal_line
        valid_data = ~histogram.isna()
        np.testing.assert_array_almost_equal(
            histogram[valid_data],
            (macd_line - signal_line)[valid_data]
        )

class TestTTMSqueeze:
    """Test TTM Squeeze implementation"""
    
    @pytest.fixture
    def sample_data(self):
        """Create sample OHLCV data"""
        dates = pd.date_range(start='2023-01-01', periods=100, freq='D')
        np.random.seed(42)
        
        close_prices = 100 + np.cumsum(np.random.randn(100) * 0.5)
        high_prices = close_prices + np.random.rand(100) * 2
        low_prices = close_prices - np.random.rand(100) * 2
        open_prices = close_prices + np.random.randn(100) * 0.5
        volumes = np.random.randint(1000000, 5000000, 100)
        
        return pd.DataFrame({
            'timestamp': dates,
            'open': open_prices,
            'high': high_prices,
            'low': low_prices,
            'close': close_prices,
            'volume': volumes
        }).set_index('timestamp')
    
    @pytest.fixture
    def ttm_squeeze(self):
        """Create TTM Squeeze instance"""
        return TTMSqueeze()
    
    def test_calculate_squeeze(self, ttm_squeeze, sample_data):
        """Test TTM Squeeze calculation"""
        result = ttm_squeeze.calculate_squeeze(sample_data)
        
        # Check that all required columns are present
        required_columns = [
            'bb_upper', 'bb_middle', 'bb_lower',
            'kc_upper', 'kc_middle', 'kc_lower',
            'momentum', 'is_squeeze', 'momentum_color',
            'signal_strength', 'entry_signal'
        ]
        
        for col in required_columns:
            assert col in result.columns
        
        # Check data types
        assert result['is_squeeze'].dtype == bool
        assert result['entry_signal'].dtype == bool
        assert result['momentum_color'].dtype == object
        
        # Check squeeze logic
        valid_data = ~result['bb_upper'].isna()
        squeeze_condition = (
            (result['bb_lower'] > result['kc_lower']) &
            (result['bb_upper'] < result['kc_upper'])
        )
        assert (result['is_squeeze'][valid_data] == squeeze_condition[valid_data]).all()
    
    def test_get_current_signal(self, ttm_squeeze, sample_data):
        """Test getting current signal"""
        signal = ttm_squeeze.get_current_signal(sample_data, 'TEST', '15Min')
        
        assert signal is not None
        assert signal.symbol == 'TEST'
        assert signal.timeframe == '15Min'
        assert isinstance(signal.is_squeeze, bool)
        assert isinstance(signal.momentum, float)
        assert signal.momentum_color in ['red', 'yellow', 'green']
        assert isinstance(signal.signal_strength, float)
        assert 0 <= signal.signal_strength <= 1
    
    def test_momentum_color_calculation(self, ttm_squeeze, sample_data):
        """Test momentum color calculation"""
        result = ttm_squeeze.calculate_squeeze(sample_data)
        
        # All colors should be valid
        valid_colors = {'red', 'yellow', 'green'}
        momentum_colors = set(result['momentum_color'].dropna().unique())
        assert momentum_colors.issubset(valid_colors)
    
    def test_signal_strength_range(self, ttm_squeeze, sample_data):
        """Test signal strength is in valid range"""
        result = ttm_squeeze.calculate_squeeze(sample_data)
        
        valid_strength = result['signal_strength'].dropna()
        assert (valid_strength >= 0).all()
        assert (valid_strength <= 1).all()

def test_integration():
    """Integration test with real-like data"""
    # Create more realistic data with a squeeze pattern
    dates = pd.date_range(start='2023-01-01', periods=50, freq='D')
    
    # Create data that should trigger a squeeze
    base_price = 100
    prices = []
    
    for i in range(50):
        if i < 20:
            # Normal volatility
            price = base_price + np.random.randn() * 2
        elif i < 35:
            # Squeeze period - low volatility
            price = base_price + np.random.randn() * 0.5
        else:
            # Breakout - increasing volatility and trend
            price = base_price + (i - 35) * 0.5 + np.random.randn() * 1
        
        prices.append(price)
        base_price = price
    
    data = pd.DataFrame({
        'timestamp': dates,
        'open': prices,
        'high': [p + abs(np.random.randn() * 0.5) for p in prices],
        'low': [p - abs(np.random.randn() * 0.5) for p in prices],
        'close': prices,
        'volume': np.random.randint(1000000, 3000000, 50)
    }).set_index('timestamp')
    
    # Test TTM Squeeze
    ttm = TTMSqueeze()
    result = ttm.calculate_squeeze(data)
    
    # Should have some squeeze periods
    assert result['is_squeeze'].any()
    
    # Should have some entry signals
    assert result['entry_signal'].any()
    
    print("✅ Integration test passed - TTM Squeeze system working correctly")

if __name__ == "__main__":
    # Run basic tests
    test_integration()
    print("✅ All basic tests passed!")
